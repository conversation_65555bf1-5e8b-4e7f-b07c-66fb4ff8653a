# HaoBBS - 简洁高效的论坛系统

HaoBBS 是一个基于 Flask 和 Tailwind CSS 构建的现代化论坛系统，支持 Markdown 语法，具有响应式设计和完整的用户交互功能。

## ✨ 特性

- 🚀 **现代化技术栈**: Flask + Tailwind CSS + SQLite
- 📝 **Markdown 支持**: 完整的 Markdown 语法支持，包括代码高亮
- 📱 **响应式设计**: 完美适配桌面和移动设备
- 🎨 **美观界面**: 基于 Tailwind CSS 的现代化 UI 设计
- 🔐 **用户认证**: 简单的登录系统
- 📂 **分类管理**: 支持帖子分类和筛选
- 💬 **实时交互**: Ajax 驱动的回复和编辑功能
- 🔍 **搜索功能**: 支持帖子内容搜索
- 📊 **分页显示**: 高效的分页加载机制

## 🚀 快速开始

### 系统要求

- **Python**: 3.8 或更高版本
- **Node.js**: 14 或更高版本
- **npm**: 6 或更高版本
- **操作系统**: macOS, Linux, Windows

### 一键启动

```bash
# 1. 克隆项目
git clone <repository-url>
cd HaoBBS

# 2. 完整安装和启动（推荐首次使用）
chmod +x scripts/start.sh
./scripts/start.sh

# 3. 或者快速启动（环境已配置的情况下）
chmod +x scripts/run.sh
./scripts/run.sh
```

启动成功后，访问 http://localhost:5002 即可使用。

### 启动选项

```bash
# 开发模式（监听CSS文件变化）
./scripts/start.sh --dev

# 生产模式（压缩CSS）
./scripts/start.sh --prod

# 清理模式（重新安装所有依赖）
./scripts/start.sh --clean

# 查看帮助
./scripts/start.sh --help
```

## 📁 项目结构

```
HaoBBS/
├── app.py                 # Flask 主应用
├── forum.db              # SQLite 数据库
├── requirements.txt      # Python 依赖
├── package.json          # Node.js 依赖
├── tailwind.config.js    # Tailwind CSS 配置
├── postcss.config.js     # PostCSS 配置
├── static/               # 静态资源
│   ├── css/
│   │   ├── input.css     # Tailwind CSS 输入文件
│   │   └── output.css    # 编译后的 CSS
│   ├── js/               # JavaScript 文件
│   └── images/           # 图片资源
├── templates/            # Jinja2 模板
│   ├── index.html        # 首页模板
│   ├── post.html         # 帖子详情模板
│   ├── new_post.html     # 新建帖子模板
│   └── ...
├── scripts/              # 部署和工具脚本
│   ├── start.sh          # 完整启动脚本
│   ├── run.sh            # 快速启动脚本
│   ├── deploy.sh         # 远程部署脚本
│   ├── build-css.sh      # CSS 构建脚本
│   ├── config.sh         # 配置文件
│   └── README.md         # 脚本说明文档
├── backup/               # 数据库备份
├── venv_fixed/           # Python 虚拟环境
└── node_modules/         # Node.js 依赖
```

## 🛠️ 开发指南

### 本地开发

```bash
# 启动开发模式（CSS 文件监听）
./scripts/start.sh --dev

# 或者分别启动
npm run build-css        # 监听 CSS 变化
python app.py            # 启动 Flask 应用
```

### 样式开发

```bash
# 构建开发环境 CSS
./scripts/build-css.sh dev

# 构建生产环境 CSS
./scripts/build-css.sh prod
```

### 数据库管理

```bash
# 备份数据库
python scripts/backup.py

# 添加用户
python scripts/add_user.py
```

## 🚢 部署指南

### 本地部署

```bash
# 生产模式启动
./scripts/start.sh --prod
```

### 远程部署

```bash
# 配置 scripts/deploy.sh 中的服务器信息
# 然后执行部署
./scripts/deploy.sh
```

部署脚本会自动：
- 同步数据库备份
- 上传项目文件
- 配置远程环境
- 使用 PM2 管理进程

## 📖 使用说明

### 基本功能

1. **浏览帖子**: 在首页查看所有帖子，支持分类筛选
2. **发布帖子**: 点击"发布新帖"创建新帖子
3. **回复帖子**: 在帖子详情页面进行回复
4. **编辑内容**: 支持编辑帖子和回复内容
5. **删除内容**: 支持删除帖子和回复

### Markdown 语法

支持完整的 Markdown 语法：

```markdown
# 标题
**粗体** *斜体*
`代码` 
> 引用
- 列表项
[链接](url)
![图片](url)
```

### 分类管理

- 发布帖子时可以指定分类
- 首页支持按分类筛选
- 分类标签显示帖子数量

## 🔧 配置说明

### 应用配置

主要配置在 `app.py` 中：
- 端口: 默认 5002
- 调试模式: 开发环境启用
- 数据库: SQLite (forum.db)

### 样式配置

Tailwind CSS 配置在 `tailwind.config.js` 中：
- 自定义颜色主题
- 响应式断点
- 自定义组件样式

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   lsof -t -i:5002 | xargs kill -9
   ```

2. **虚拟环境问题**
   ```bash
   rm -rf venv_fixed
   ./scripts/start.sh --clean
   ```

3. **CSS 构建失败**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **权限问题**
   ```bash
   chmod +x scripts/*.sh
   ```

### 日志查看

应用运行时会显示详细的日志信息，包括：
- 请求日志
- 错误信息
- 调试信息

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

- [Flask](https://flask.palletsprojects.com/) - Web 框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Markdown](https://python-markdown.github.io/) - Markdown 解析
- [Bleach](https://bleach.readthedocs.io/) - HTML 清理

---

**HaoBBS** - 让交流更简单 ✨
