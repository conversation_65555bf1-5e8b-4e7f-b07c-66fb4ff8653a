#!/bin/bash

# HaoBBS 项目配置文件
# 包含所有脚本共用的配置变量和函数

# 项目配置
export PROJECT_NAME="HaoBBS"
export PROJECT_VERSION="1.0.0"
export VENV_NAME="venv_fixed"
export DEFAULT_PORT="5002"
export PYTHON_MIN_VERSION="3.8"
export NODE_MIN_VERSION="14"

# 路径配置
export PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
export VENV_PATH="$PROJECT_ROOT/$VENV_NAME"
export STATIC_DIR="$PROJECT_ROOT/static"
export TEMPLATES_DIR="$PROJECT_ROOT/templates"
export SCRIPTS_DIR="$PROJECT_ROOT/scripts"
export BACKUP_DIR="$PROJECT_ROOT/backup"

# 文件配置
export REQUIREMENTS_FILE="$PROJECT_ROOT/requirements.txt"
export PACKAGE_JSON="$PROJECT_ROOT/package.json"
export DATABASE_FILE="$PROJECT_ROOT/forum.db"
export CSS_INPUT="$PROJECT_ROOT/static/css/input.css"
export CSS_OUTPUT="$PROJECT_ROOT/static/css/output.css"

# 远程部署配置（可根据需要修改）
export REMOTE_USER="lighthouse"
export REMOTE_HOST="**************"
export REMOTE_PORT="22"
export REMOTE_DIR="/home/<USER>/flask/haobbs"

# 颜色定义
export COLOR_RESET='\033[0m'
export COLOR_RED='\033[31m'
export COLOR_GREEN='\033[32m'
export COLOR_YELLOW='\033[33m'
export COLOR_BLUE='\033[34m'
export COLOR_PURPLE='\033[35m'
export COLOR_CYAN='\033[36m'
export COLOR_WHITE='\033[37m'

# 通用函数库

# 颜色输出函数
function echo_info() {
    echo -e "${COLOR_BLUE}[INFO] $1${COLOR_RESET}"
}

function echo_success() {
    echo -e "${COLOR_GREEN}[SUCCESS] $1${COLOR_RESET}"
}

function echo_error() {
    echo -e "${COLOR_RED}[ERROR] $1${COLOR_RESET}"
}

function echo_warning() {
    echo -e "${COLOR_YELLOW}[WARNING] $1${COLOR_RESET}"
}

function echo_debug() {
    if [ "$DEBUG" = "true" ]; then
        echo -e "${COLOR_PURPLE}[DEBUG] $1${COLOR_RESET}"
    fi
}

# 检查命令是否执行成功
function check_status() {
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        echo_success "$1"
        return 0
    else
        echo_error "$2"
        if [ "$3" = "exit" ]; then
            exit $exit_code
        fi
        return $exit_code
    fi
}

# 检查命令是否存在
function command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查文件是否存在
function file_exists() {
    [ -f "$1" ]
}

# 检查目录是否存在
function dir_exists() {
    [ -d "$1" ]
}

# 检查端口是否被占用
function port_in_use() {
    lsof -i:"$1" >/dev/null 2>&1
}

# 清理端口
function cleanup_port() {
    local port="$1"
    if port_in_use "$port"; then
        echo_warning "端口 $port 被占用，正在清理..."
        lsof -t -i:"$port" | xargs -r kill -9
        sleep 1
        if port_in_use "$port"; then
            echo_error "端口 $port 清理失败"
            return 1
        else
            echo_success "端口 $port 已清理"
        fi
    fi
    return 0
}

# 检查Python版本
function check_python_version() {
    if ! command_exists python3; then
        echo_error "Python3 未安装"
        return 1
    fi
    
    local version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    local min_version="$PYTHON_MIN_VERSION"
    
    if [ "$(printf '%s\n' "$min_version" "$version" | sort -V | head -n1)" = "$min_version" ]; then
        echo_info "Python 版本检查通过: $version (>= $min_version)"
        return 0
    else
        echo_error "Python 版本过低: $version (需要 >= $min_version)"
        return 1
    fi
}

# 检查Node.js版本
function check_node_version() {
    if ! command_exists node; then
        echo_error "Node.js 未安装"
        return 1
    fi
    
    local version=$(node --version | sed 's/v//')
    local min_version="$NODE_MIN_VERSION"
    
    if [ "$(printf '%s\n' "$min_version" "$version" | sort -V | head -n1)" = "$min_version" ]; then
        echo_info "Node.js 版本检查通过: $version (>= $min_version)"
        return 0
    else
        echo_error "Node.js 版本过低: $version (需要 >= $min_version)"
        return 1
    fi
}

# 激活虚拟环境
function activate_venv() {
    if [ ! -d "$VENV_PATH" ]; then
        echo_error "虚拟环境不存在: $VENV_PATH"
        return 1
    fi
    
    source "$VENV_PATH/bin/activate"
    if [ "$VIRTUAL_ENV" = "$VENV_PATH" ]; then
        echo_success "虚拟环境已激活: $VENV_NAME"
        return 0
    else
        echo_error "虚拟环境激活失败"
        return 1
    fi
}

# 创建虚拟环境
function create_venv() {
    if [ -d "$VENV_PATH" ]; then
        echo_info "虚拟环境已存在: $VENV_NAME"
        return 0
    fi
    
    echo_info "创建虚拟环境: $VENV_NAME"
    python3 -m venv "$VENV_PATH"
    check_status "虚拟环境创建成功" "虚拟环境创建失败" "exit"
}

# 显示项目信息
function show_project_info() {
    echo_success "=== $PROJECT_NAME v$PROJECT_VERSION ==="
    echo_info "项目路径: $PROJECT_ROOT"
    echo_info "虚拟环境: $VENV_PATH"
    echo_info "默认端口: $DEFAULT_PORT"
    echo ""
}

# 显示系统信息
function show_system_info() {
    echo_info "系统信息:"
    echo "  操作系统: $(uname -s)"
    echo "  架构: $(uname -m)"
    if command_exists python3; then
        echo "  Python: $(python3 --version)"
    fi
    if command_exists node; then
        echo "  Node.js: $(node --version)"
    fi
    if command_exists npm; then
        echo "  npm: $(npm --version)"
    fi
    echo ""
}

# 创建必要的目录
function create_directories() {
    local dirs=("$BACKUP_DIR" "$STATIC_DIR/css" "$TEMPLATES_DIR")
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            echo_info "创建目录: $dir"
        fi
    done
}

# 检查必要文件
function check_required_files() {
    local files=("$REQUIREMENTS_FILE" "$PACKAGE_JSON")
    local missing=false
    
    for file in "${files[@]}"; do
        if [ ! -f "$file" ]; then
            echo_error "缺少必要文件: $file"
            missing=true
        fi
    done
    
    if [ "$missing" = true ]; then
        return 1
    fi
    
    echo_success "必要文件检查通过"
    return 0
}

# 导出所有函数，使其在其他脚本中可用
export -f echo_info echo_success echo_error echo_warning echo_debug
export -f check_status command_exists file_exists dir_exists
export -f port_in_use cleanup_port
export -f check_python_version check_node_version
export -f activate_venv create_venv
export -f show_project_info show_system_info
export -f create_directories check_required_files
