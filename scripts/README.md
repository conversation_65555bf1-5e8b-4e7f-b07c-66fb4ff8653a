# HaoBBS 部署脚本说明

本目录包含了 HaoBBS 项目的各种部署和启动脚本。

## 脚本概览

### 🚀 启动脚本

#### `start.sh` - 完整一键启动脚本
**推荐用于首次部署或完整环境设置**

```bash
# 赋予执行权限
chmod +x scripts/start.sh

# 默认启动（生产模式）
./scripts/start.sh

# 开发模式（监听CSS文件变化）
./scripts/start.sh --dev

# 生产模式（压缩CSS）
./scripts/start.sh --prod

# 清理模式（重新安装所有依赖）
./scripts/start.sh --clean

# 查看帮助
./scripts/start.sh --help
```

**功能特性：**
- ✅ 自动检查系统要求（Python3, Node.js, npm）
- ✅ 创建和管理虚拟环境（venv_fixed）
- ✅ 安装/更新 Python 和 Node.js 依赖
- ✅ 构建 Tailwind CSS 资源
- ✅ 清理端口占用
- ✅ 完整的错误处理和状态检查
- ✅ 支持开发和生产模式
- ✅ 彩色日志输出

#### `run.sh` - 快速启动脚本
**推荐用于日常开发，环境已配置好的情况**

```bash
# 赋予执行权限
chmod +x scripts/run.sh

# 快速启动
./scripts/run.sh
```

**功能特性：**
- ✅ 轻量级，启动速度快
- ✅ 检查虚拟环境和CSS文件
- ✅ 自动清理端口占用
- ✅ 适合日常开发使用

### 🏗️ 构建脚本

#### `build-css.sh` - CSS 构建脚本
```bash
# 开发模式（监听文件变化）
./scripts/build-css.sh dev

# 生产模式（压缩输出）
./scripts/build-css.sh prod

# 默认构建
./scripts/build-css.sh
```

### 🚢 部署脚本

#### `deploy.sh` - 远程服务器部署脚本
**用于部署到远程服务器**

```bash
# 部署到远程服务器
./scripts/deploy.sh
```

**功能特性：**
- ✅ 自动同步数据库备份
- ✅ 上传项目文件到服务器
- ✅ 远程环境配置
- ✅ PM2 进程管理
- ✅ 已修复虚拟环境路径问题

### 🛠️ 工具脚本

- `add_user.py` - 添加用户脚本
- `add_user_cli.py` - 命令行添加用户
- `backup.py` - 数据库备份脚本
- `test-styles.py` - 样式测试脚本

## 使用建议

### 首次部署
```bash
# 1. 完整安装和启动
./scripts/start.sh --clean

# 2. 或者分步执行
./scripts/build-css.sh prod
./scripts/start.sh
```

### 日常开发
```bash
# 快速启动
./scripts/run.sh

# 或者开发模式（CSS监听）
./scripts/start.sh --dev
```

### 生产部署
```bash
# 本地构建
./scripts/start.sh --prod

# 远程部署
./scripts/deploy.sh
```

## 系统要求

- **Python**: 3.8 或更高版本
- **Node.js**: 14 或更高版本
- **npm**: 6 或更高版本
- **操作系统**: macOS, Linux（已针对 macOS 优化）

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 手动清理端口
   lsof -t -i:5002 | xargs kill -9
   ```

2. **虚拟环境问题**
   ```bash
   # 重新创建虚拟环境
   rm -rf venv_fixed
   ./scripts/start.sh --clean
   ```

3. **CSS 构建失败**
   ```bash
   # 重新安装 Node.js 依赖
   rm -rf node_modules package-lock.json
   npm install
   npm run build-css-prod
   ```

4. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x scripts/*.sh
   ```

### 日志和调试

所有脚本都包含详细的日志输出：
- 🔵 **[INFO]** - 信息日志
- 🟢 **[SUCCESS]** - 成功日志
- 🔴 **[ERROR]** - 错误日志
- 🟡 **[WARNING]** - 警告日志

## 配置说明

### 环境变量
脚本会自动检测和配置以下环境：
- `PROJECT_ROOT` - 项目根目录
- `VENV_PATH` - 虚拟环境路径
- `VIRTUAL_ENV` - Python 虚拟环境

### 端口配置
- 默认端口：`5002`
- 可在 `app.py` 中修改

### 数据库
- 数据库文件：`forum.db`
- 备份目录：`backup/`

## 更新日志

### v1.0.0 (2025-08-19)
- ✅ 创建完整的一键启动脚本 `start.sh`
- ✅ 创建快速启动脚本 `run.sh`
- ✅ 修复部署脚本中的虚拟环境路径问题
- ✅ 添加完整的错误处理和状态检查
- ✅ 支持开发和生产模式
- ✅ 添加清理和重置功能
