#!/bin/bash

# HaoBBS 快速启动脚本
# 简化版启动脚本，适用于已经配置好环境的情况
#
# 使用方法:
#   chmod +x scripts/run.sh
#   ./scripts/run.sh

set -e

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
VENV_PATH="$PROJECT_ROOT/venv_fixed"

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

function echo_warning() {
    echo -e "\033[33m[WARNING] $1\033[0m"
}

# 清理函数
function cleanup() {
    echo ""
    echo_info "应用已停止，再见！"
}

# 设置清理陷阱
trap cleanup EXIT INT TERM

echo_success "=== HaoBBS 快速启动 ==="

cd "$PROJECT_ROOT"

# 检查虚拟环境
if [ ! -d "$VENV_PATH" ]; then
    echo_error "虚拟环境不存在: $VENV_PATH"
    echo_info "请先运行: ./scripts/start.sh 进行完整安装"
    exit 1
fi

# 激活虚拟环境
echo_info "激活虚拟环境..."
source "$VENV_PATH/bin/activate"

# 检查CSS文件
if [ ! -f "static/css/output.css" ]; then
    echo_warning "CSS文件不存在，正在构建..."
    if [ -f "package.json" ] && command -v npm &> /dev/null; then
        npm run build-css-prod
    else
        echo_error "无法构建CSS，请先运行: ./scripts/start.sh"
        exit 1
    fi
fi

# 清理端口
if lsof -i:5002 > /dev/null 2>&1; then
    echo_warning "端口 5002 被占用，正在清理..."
    lsof -t -i:5002 | xargs -r kill -9
    sleep 1
fi

echo_success "启动 HaoBBS..."
echo_info "应用地址: http://localhost:5002"
echo_info "按 Ctrl+C 停止应用"
echo ""

# 启动应用
python app.py
