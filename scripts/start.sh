#!/bin/bash

# HaoBBS 一键启动脚本
# 用于本地开发环境的完整启动流程
# 
# 使用方法:
#   chmod +x scripts/start.sh
#   ./scripts/start.sh [选项]
#
# 选项:
#   --dev, -d     开发模式（监听CSS文件变化）
#   --prod, -p    生产模式（构建压缩CSS）
#   --clean, -c   清理模式（重新安装依赖）
#   --help, -h    显示帮助信息

set -e  # 遇到错误立即退出

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
VENV_NAME="venv_fixed"
VENV_PATH="$PROJECT_ROOT/$VENV_NAME"

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

function echo_warning() {
    echo -e "\033[33m[WARNING] $1\033[0m"
}

# 检查命令是否执行成功
function check_status() {
    if [ $? -eq 0 ]; then
        echo_success "$1"
    else
        echo_error "$2"
        exit 1
    fi
}

# 显示帮助信息
function show_help() {
    echo "HaoBBS 一键启动脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --dev, -d     开发模式（监听CSS文件变化）"
    echo "  --prod, -p    生产模式（构建压缩CSS）"
    echo "  --clean, -c   清理模式（重新安装依赖）"
    echo "  --help, -h    显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 默认启动"
    echo "  $0 --dev       # 开发模式启动"
    echo "  $0 --prod      # 生产模式启动"
    echo "  $0 --clean     # 清理并重新安装依赖后启动"
}

# 检查系统要求
function check_requirements() {
    echo_info "检查系统要求..."
    
    # 检查Python3
    if ! command -v python3 &> /dev/null; then
        echo_error "Python3 未安装，请先安装 Python 3.8 或更高版本"
        exit 1
    fi
    
    # 检查Python版本
    PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    echo_info "检测到 Python 版本: $PYTHON_VERSION"
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        echo_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    echo_info "检测到 Node.js 版本: $NODE_VERSION, npm 版本: $NPM_VERSION"
    
    echo_success "系统要求检查通过"
}

# 创建或检查虚拟环境
function setup_python_env() {
    echo_info "设置 Python 虚拟环境..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -d "$VENV_PATH" ]; then
        echo_info "创建虚拟环境: $VENV_NAME"
        python3 -m venv "$VENV_PATH"
        check_status "虚拟环境创建成功" "虚拟环境创建失败"
    else
        echo_info "虚拟环境已存在: $VENV_NAME"
    fi
    
    # 激活虚拟环境
    echo_info "激活虚拟环境..."
    source "$VENV_PATH/bin/activate"
    check_status "虚拟环境激活成功" "虚拟环境激活失败"
    
    # 升级pip
    echo_info "升级 pip..."
    pip install --upgrade pip > /dev/null 2>&1
    check_status "pip 升级成功" "pip 升级失败"
}

# 安装Python依赖
function install_python_deps() {
    echo_info "安装 Python 依赖..."
    
    if [ ! -f "$PROJECT_ROOT/requirements.txt" ]; then
        echo_error "requirements.txt 文件不存在"
        exit 1
    fi
    
    pip install -r requirements.txt
    check_status "Python 依赖安装成功" "Python 依赖安装失败"
}

# 安装Node.js依赖
function install_node_deps() {
    echo_info "安装 Node.js 依赖..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -f "package.json" ]; then
        echo_error "package.json 文件不存在"
        exit 1
    fi
    
    npm install
    check_status "Node.js 依赖安装成功" "Node.js 依赖安装失败"
}

# 构建CSS
function build_css() {
    local mode="$1"
    echo_info "构建 CSS 资源..."
    
    cd "$PROJECT_ROOT"
    
    case "$mode" in
        "dev")
            echo_info "构建开发环境 CSS（监听模式）..."
            npm run build-css &
            CSS_PID=$!
            echo_info "CSS 监听进程已启动 (PID: $CSS_PID)"
            ;;
        "prod")
            echo_info "构建生产环境 CSS..."
            npm run build-css-prod
            check_status "生产环境 CSS 构建完成" "CSS 构建失败"
            ;;
        *)
            echo_info "构建默认 CSS..."
            npm run build-css-prod
            check_status "CSS 构建完成" "CSS 构建失败"
            ;;
    esac
}

# 检查数据库
function check_database() {
    echo_info "检查数据库..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -f "forum.db" ]; then
        echo_warning "数据库文件不存在，将在首次运行时自动创建"
    else
        echo_info "数据库文件已存在"
    fi
}

# 清理旧的进程
function cleanup_processes() {
    echo_info "清理可能存在的旧进程..."
    
    # 检查端口5002是否被占用
    if lsof -i:5002 > /dev/null 2>&1; then
        echo_warning "端口 5002 被占用，正在清理..."
        lsof -t -i:5002 | xargs -r kill -9
        sleep 2
        echo_success "端口已清理"
    fi
    
    # 清理可能存在的CSS监听进程
    pkill -f "tailwindcss.*--watch" > /dev/null 2>&1 || true
}

# 启动应用
function start_application() {
    echo_info "启动 HaoBBS 应用..."
    
    cd "$PROJECT_ROOT"
    
    # 确保虚拟环境已激活
    if [[ "$VIRTUAL_ENV" != "$VENV_PATH" ]]; then
        source "$VENV_PATH/bin/activate"
    fi
    
    echo_success "=== HaoBBS 启动成功! ==="
    echo_info "应用地址: http://localhost:5002"
    echo_info "按 Ctrl+C 停止应用"
    echo ""
    
    # 启动Flask应用
    python app.py
}

# 清理函数（在脚本退出时调用）
function cleanup() {
    echo ""
    echo_info "正在清理资源..."
    
    # 停止CSS监听进程
    if [ ! -z "$CSS_PID" ]; then
        kill $CSS_PID > /dev/null 2>&1 || true
        echo_info "CSS 监听进程已停止"
    fi
    
    echo_success "清理完成，再见！"
}

# 设置清理陷阱
trap cleanup EXIT INT TERM

# 主函数
function main() {
    local mode="default"
    local clean_mode=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dev|-d)
                mode="dev"
                shift
                ;;
            --prod|-p)
                mode="prod"
                shift
                ;;
            --clean|-c)
                clean_mode=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                echo_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo_success "=== HaoBBS 一键启动脚本 ==="
    echo_info "项目路径: $PROJECT_ROOT"
    echo_info "启动模式: $mode"
    echo ""
    
    # 执行启动流程
    check_requirements
    cleanup_processes
    
    # 清理模式：删除现有依赖
    if [ "$clean_mode" = true ]; then
        echo_info "清理模式：删除现有依赖..."
        rm -rf "$VENV_PATH" node_modules package-lock.json
        echo_success "依赖清理完成"
    fi
    
    setup_python_env
    install_python_deps
    install_node_deps
    build_css "$mode"
    check_database
    start_application
}

# 运行主函数
main "$@"
