#!/bin/bash

# 服务器配置
SERVER_USER="lighthouse"
SERVER_HOST="**************"
SERVER_PORT="22"
REMOTE_DIR="/home/<USER>/flask/haobbs"

# 颜色输出函数
function echo_info() {
    echo -e "\033[34m[INFO] $1\033[0m"
}

function echo_success() {
    echo -e "\033[32m[SUCCESS] $1\033[0m"
}

function echo_error() {
    echo -e "\033[31m[ERROR] $1\033[0m"
}

# 检查命令是否执行成功
function check_status() {
    if [ $? -eq 0 ]; then
        echo_success "$1"
    else
        echo_error "$2"
        exit 1
    fi
}

# 确保远程目录存在
echo_info "创建远程部署目录..."
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "mkdir -p $REMOTE_DIR"
check_status "远程目录创建成功" "远程目录创建失败"

# 同步服务器数据库到本地
echo_info "同步服务器数据库到本地..."
TIMESTAMP=$(date +'%Y%m%d')
rsync -avz -e "ssh -p $SERVER_PORT" $SERVER_USER@$SERVER_HOST:$REMOTE_DIR/forum.db ./backup/forum_$TIMESTAMP.db
check_status "数据库同步成功" "数据库同步失败"

# 同步项目文件
echo_info "同步项目文件到服务器..."
rsync -avz --exclude '.git' \
    --exclude '__pycache__' \
    --exclude '*.pyc' \
    --exclude 'venv/' \
    --exclude 'backup/' \
    --exclude 'forum.db' \
    --exclude 'node_modules/' \
    -e "ssh -p $SERVER_PORT" \
    ./ $SERVER_USER@$SERVER_HOST:$REMOTE_DIR/
check_status "文件同步成功" "文件同步失败"

# 检查并安装pm2
echo_info "检查并安装pm2..."
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "if ! command -v pm2 &> /dev/null; then npm install -g pm2; fi"
check_status "pm2检查完成" "pm2安装失败"

# 在服务器上执行部署命令
ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST << ENDSSH
    cd $REMOTE_DIR
    
    # 检查并创建虚拟环境
    if [ ! -d "venv_fixed" ]; then
        echo "创建虚拟环境..."
        python3 -m venv venv_fixed
    fi

    # 激活虚拟环境
    echo "激活虚拟环境..."
    source venv_fixed/bin/activate

    # 升级pip
    echo "升级 pip..."
    pip install --upgrade pip

    # 安装依赖
    echo "安装项目依赖..."
    pip install -r requirements.txt

    # 安装 Node.js 依赖并构建 CSS
    echo "安装 Node.js 依赖..."
    npm install
    echo "构建 Tailwind CSS..."
    npm run build-css-prod
    
    # 检查数据库是否存在
    if [ ! -f "forum.db" ]; then
        echo "数据库文件不存在，开始初始化数据库..."
        python3 -c "from app import init_db; init_db()"
    else
        echo "数据库文件已存在，跳过初始化步骤"
    fi
    
    # 创建日志目录
    mkdir -p logs

    # 检查并清理端口占用
    echo "检查端口占用情况..."
    if lsof -i:5002 > /dev/null 2>&1; then
        echo "端口5002被占用，正在清理..."
        sudo lsof -t -i:5002 | xargs -r sudo kill -9
        echo "端口已清理"
    else
        echo "端口5002未被占用"
    fi

    # 使用pm2管理应用
    echo "配置并启动应用服务..."
    # 如果haobbs应用已经在pm2中注册，先删除它
    pm2 delete haobbs > /dev/null 2>&1
    
    # 启动应用
    pm2 start app.py --name haobbs --interpreter ./venv_fixed/bin/python3
    
    # 保存pm2配置，以便重启后自动启动
    pm2 save
    
    # 退出虚拟环境
    deactivate
ENDSSH
check_status "部署完成" "部署失败"

echo_success "=== 部署完成! ==="
