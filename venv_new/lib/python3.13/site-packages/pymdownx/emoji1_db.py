"""Emojione autogen.

Generated from emojione source. Do not edit by hand.

MIT license.

Copyright (c) http://www.emojione.com
"""
version = "v2.2.7"
name = "emojione"
emoji = {
    ":100:": {
        "category": "symbols",
        "name": "hundred points symbol",
        "unicode": "1f4af"
    },
    ":1234:": {
        "category": "symbols",
        "name": "input symbol for numbers",
        "unicode": "1f522"
    },
    ":8ball:": {
        "category": "activity",
        "name": "billiards",
        "unicode": "1f3b1"
    },
    ":a:": {
        "category": "symbols",
        "name": "negative squared latin capital letter a",
        "unicode": "1f170"
    },
    ":ab:": {
        "category": "symbols",
        "name": "negative squared ab",
        "unicode": "1f18e"
    },
    ":abc:": {
        "category": "symbols",
        "name": "input symbol for latin letters",
        "unicode": "1f524"
    },
    ":abcd:": {
        "category": "symbols",
        "name": "input symbol for latin small letters",
        "unicode": "1f521"
    },
    ":accept:": {
        "category": "symbols",
        "name": "circled ideograph accept",
        "unicode": "1f251"
    },
    ":aerial_tramway:": {
        "category": "travel",
        "name": "aerial tramway",
        "unicode": "1f6a1"
    },
    ":airplane:": {
        "category": "travel",
        "name": "airplane",
        "unicode": "2708",
        "unicode_alt": "2708-fe0f"
    },
    ":airplane_arriving:": {
        "category": "travel",
        "name": "airplane arriving",
        "unicode": "1f6ec"
    },
    ":airplane_departure:": {
        "category": "travel",
        "name": "airplane departure",
        "unicode": "1f6eb"
    },
    ":airplane_small:": {
        "category": "travel",
        "name": "small airplane",
        "unicode": "1f6e9",
        "unicode_alt": "1f6e9-fe0f"
    },
    ":alarm_clock:": {
        "category": "objects",
        "name": "alarm clock",
        "unicode": "23f0"
    },
    ":alembic:": {
        "category": "objects",
        "name": "alembic",
        "unicode": "2697",
        "unicode_alt": "2697-fe0f"
    },
    ":alien:": {
        "category": "people",
        "name": "extraterrestrial alien",
        "unicode": "1f47d"
    },
    ":ambulance:": {
        "category": "travel",
        "name": "ambulance",
        "unicode": "1f691"
    },
    ":amphora:": {
        "category": "objects",
        "name": "amphora",
        "unicode": "1f3fa"
    },
    ":anchor:": {
        "category": "travel",
        "name": "anchor",
        "unicode": "2693",
        "unicode_alt": "2693-fe0f"
    },
    ":angel:": {
        "category": "people",
        "name": "baby angel",
        "unicode": "1f47c"
    },
    ":angel_tone1:": {
        "category": "people",
        "name": "baby angel tone 1",
        "unicode": "1f47c-1f3fb"
    },
    ":angel_tone2:": {
        "category": "people",
        "name": "baby angel tone 2",
        "unicode": "1f47c-1f3fc"
    },
    ":angel_tone3:": {
        "category": "people",
        "name": "baby angel tone 3",
        "unicode": "1f47c-1f3fd"
    },
    ":angel_tone4:": {
        "category": "people",
        "name": "baby angel tone 4",
        "unicode": "1f47c-1f3fe"
    },
    ":angel_tone5:": {
        "category": "people",
        "name": "baby angel tone 5",
        "unicode": "1f47c-1f3ff"
    },
    ":anger:": {
        "category": "symbols",
        "name": "anger symbol",
        "unicode": "1f4a2"
    },
    ":anger_right:": {
        "category": "symbols",
        "name": "right anger bubble",
        "unicode": "1f5ef",
        "unicode_alt": "1f5ef-fe0f"
    },
    ":angry:": {
        "category": "people",
        "name": "angry face",
        "unicode": "1f620"
    },
    ":anguished:": {
        "category": "people",
        "name": "anguished face",
        "unicode": "1f627"
    },
    ":ant:": {
        "category": "nature",
        "name": "ant",
        "unicode": "1f41c"
    },
    ":apple:": {
        "category": "food",
        "name": "red apple",
        "unicode": "1f34e"
    },
    ":aquarius:": {
        "category": "symbols",
        "name": "aquarius",
        "unicode": "2652",
        "unicode_alt": "2652-fe0f"
    },
    ":aries:": {
        "category": "symbols",
        "name": "aries",
        "unicode": "2648",
        "unicode_alt": "2648-fe0f"
    },
    ":arrow_backward:": {
        "category": "symbols",
        "name": "black left-pointing triangle",
        "unicode": "25c0",
        "unicode_alt": "25c0-fe0f"
    },
    ":arrow_double_down:": {
        "category": "symbols",
        "name": "black down-pointing double triangle",
        "unicode": "23ec"
    },
    ":arrow_double_up:": {
        "category": "symbols",
        "name": "black up-pointing double triangle",
        "unicode": "23eb"
    },
    ":arrow_down:": {
        "category": "symbols",
        "name": "downwards black arrow",
        "unicode": "2b07",
        "unicode_alt": "2b07-fe0f"
    },
    ":arrow_down_small:": {
        "category": "symbols",
        "name": "down-pointing small red triangle",
        "unicode": "1f53d"
    },
    ":arrow_forward:": {
        "category": "symbols",
        "name": "black right-pointing triangle",
        "unicode": "25b6",
        "unicode_alt": "25b6-fe0f"
    },
    ":arrow_heading_down:": {
        "category": "symbols",
        "name": "arrow pointing rightwards then curving downwards",
        "unicode": "2935",
        "unicode_alt": "2935-fe0f"
    },
    ":arrow_heading_up:": {
        "category": "symbols",
        "name": "arrow pointing rightwards then curving upwards",
        "unicode": "2934",
        "unicode_alt": "2934-fe0f"
    },
    ":arrow_left:": {
        "category": "symbols",
        "name": "leftwards black arrow",
        "unicode": "2b05",
        "unicode_alt": "2b05-fe0f"
    },
    ":arrow_lower_left:": {
        "category": "symbols",
        "name": "south west arrow",
        "unicode": "2199",
        "unicode_alt": "2199-fe0f"
    },
    ":arrow_lower_right:": {
        "category": "symbols",
        "name": "south east arrow",
        "unicode": "2198",
        "unicode_alt": "2198-fe0f"
    },
    ":arrow_right:": {
        "category": "symbols",
        "name": "black rightwards arrow",
        "unicode": "27a1",
        "unicode_alt": "27a1-fe0f"
    },
    ":arrow_right_hook:": {
        "category": "symbols",
        "name": "rightwards arrow with hook",
        "unicode": "21aa",
        "unicode_alt": "21aa-fe0f"
    },
    ":arrow_up:": {
        "category": "symbols",
        "name": "upwards black arrow",
        "unicode": "2b06",
        "unicode_alt": "2b06-fe0f"
    },
    ":arrow_up_down:": {
        "category": "symbols",
        "name": "up down arrow",
        "unicode": "2195",
        "unicode_alt": "2195-fe0f"
    },
    ":arrow_up_small:": {
        "category": "symbols",
        "name": "up-pointing small red triangle",
        "unicode": "1f53c"
    },
    ":arrow_upper_left:": {
        "category": "symbols",
        "name": "north west arrow",
        "unicode": "2196",
        "unicode_alt": "2196-fe0f"
    },
    ":arrow_upper_right:": {
        "category": "symbols",
        "name": "north east arrow",
        "unicode": "2197",
        "unicode_alt": "2197-fe0f"
    },
    ":arrows_clockwise:": {
        "category": "symbols",
        "name": "clockwise downwards and upwards open circle arrows",
        "unicode": "1f503"
    },
    ":arrows_counterclockwise:": {
        "category": "symbols",
        "name": "anticlockwise downwards and upwards open circle arrows",
        "unicode": "1f504"
    },
    ":art:": {
        "category": "activity",
        "name": "artist palette",
        "unicode": "1f3a8"
    },
    ":articulated_lorry:": {
        "category": "travel",
        "name": "articulated lorry",
        "unicode": "1f69b"
    },
    ":asterisk:": {
        "category": "symbols",
        "name": "keycap asterisk",
        "unicode": "002a-20e3",
        "unicode_alt": "002a-fe0f-20e3"
    },
    ":astonished:": {
        "category": "people",
        "name": "astonished face",
        "unicode": "1f632"
    },
    ":athletic_shoe:": {
        "category": "people",
        "name": "athletic shoe",
        "unicode": "1f45f"
    },
    ":atm:": {
        "category": "symbols",
        "name": "automated teller machine",
        "unicode": "1f3e7"
    },
    ":atom:": {
        "category": "symbols",
        "name": "atom symbol",
        "unicode": "269b",
        "unicode_alt": "269b-fe0f"
    },
    ":avocado:": {
        "category": "food",
        "name": "avocado",
        "unicode": "1f951"
    },
    ":b:": {
        "category": "symbols",
        "name": "negative squared latin capital letter b",
        "unicode": "1f171"
    },
    ":baby:": {
        "category": "people",
        "name": "baby",
        "unicode": "1f476"
    },
    ":baby_bottle:": {
        "category": "food",
        "name": "baby bottle",
        "unicode": "1f37c"
    },
    ":baby_chick:": {
        "category": "nature",
        "name": "baby chick",
        "unicode": "1f424"
    },
    ":baby_symbol:": {
        "category": "symbols",
        "name": "baby symbol",
        "unicode": "1f6bc"
    },
    ":baby_tone1:": {
        "category": "people",
        "name": "baby tone 1",
        "unicode": "1f476-1f3fb"
    },
    ":baby_tone2:": {
        "category": "people",
        "name": "baby tone 2",
        "unicode": "1f476-1f3fc"
    },
    ":baby_tone3:": {
        "category": "people",
        "name": "baby tone 3",
        "unicode": "1f476-1f3fd"
    },
    ":baby_tone4:": {
        "category": "people",
        "name": "baby tone 4",
        "unicode": "1f476-1f3fe"
    },
    ":baby_tone5:": {
        "category": "people",
        "name": "baby tone 5",
        "unicode": "1f476-1f3ff"
    },
    ":back:": {
        "category": "symbols",
        "name": "back with leftwards arrow above",
        "unicode": "1f519"
    },
    ":bacon:": {
        "category": "food",
        "name": "bacon",
        "unicode": "1f953"
    },
    ":badminton:": {
        "category": "activity",
        "name": "badminton racquet",
        "unicode": "1f3f8"
    },
    ":baggage_claim:": {
        "category": "symbols",
        "name": "baggage claim",
        "unicode": "1f6c4"
    },
    ":balloon:": {
        "category": "objects",
        "name": "balloon",
        "unicode": "1f388"
    },
    ":ballot_box:": {
        "category": "objects",
        "name": "ballot box with ballot",
        "unicode": "1f5f3",
        "unicode_alt": "1f5f3-fe0f"
    },
    ":ballot_box_with_check:": {
        "category": "symbols",
        "name": "ballot box with check",
        "unicode": "2611",
        "unicode_alt": "2611-fe0f"
    },
    ":bamboo:": {
        "category": "nature",
        "name": "pine decoration",
        "unicode": "1f38d"
    },
    ":banana:": {
        "category": "food",
        "name": "banana",
        "unicode": "1f34c"
    },
    ":bangbang:": {
        "category": "symbols",
        "name": "double exclamation mark",
        "unicode": "203c",
        "unicode_alt": "203c-fe0f"
    },
    ":bank:": {
        "category": "travel",
        "name": "bank",
        "unicode": "1f3e6"
    },
    ":bar_chart:": {
        "category": "objects",
        "name": "bar chart",
        "unicode": "1f4ca"
    },
    ":barber:": {
        "category": "objects",
        "name": "barber pole",
        "unicode": "1f488"
    },
    ":baseball:": {
        "category": "activity",
        "name": "baseball",
        "unicode": "26be",
        "unicode_alt": "26be-fe0f"
    },
    ":basketball:": {
        "category": "activity",
        "name": "basketball and hoop",
        "unicode": "1f3c0"
    },
    ":basketball_player:": {
        "category": "activity",
        "name": "person with ball",
        "unicode": "26f9",
        "unicode_alt": "26f9-fe0f"
    },
    ":basketball_player_tone1:": {
        "category": "activity",
        "name": "person with ball tone 1",
        "unicode": "26f9-1f3fb"
    },
    ":basketball_player_tone2:": {
        "category": "activity",
        "name": "person with ball tone 2",
        "unicode": "26f9-1f3fc"
    },
    ":basketball_player_tone3:": {
        "category": "activity",
        "name": "person with ball tone 3",
        "unicode": "26f9-1f3fd"
    },
    ":basketball_player_tone4:": {
        "category": "activity",
        "name": "person with ball tone 4",
        "unicode": "26f9-1f3fe"
    },
    ":basketball_player_tone5:": {
        "category": "activity",
        "name": "person with ball tone 5",
        "unicode": "26f9-1f3ff"
    },
    ":bat:": {
        "category": "nature",
        "name": "bat",
        "unicode": "1f987"
    },
    ":bath:": {
        "category": "activity",
        "name": "bath",
        "unicode": "1f6c0"
    },
    ":bath_tone1:": {
        "category": "activity",
        "name": "bath tone 1",
        "unicode": "1f6c0-1f3fb"
    },
    ":bath_tone2:": {
        "category": "activity",
        "name": "bath tone 2",
        "unicode": "1f6c0-1f3fc"
    },
    ":bath_tone3:": {
        "category": "activity",
        "name": "bath tone 3",
        "unicode": "1f6c0-1f3fd"
    },
    ":bath_tone4:": {
        "category": "activity",
        "name": "bath tone 4",
        "unicode": "1f6c0-1f3fe"
    },
    ":bath_tone5:": {
        "category": "activity",
        "name": "bath tone 5",
        "unicode": "1f6c0-1f3ff"
    },
    ":bathtub:": {
        "category": "objects",
        "name": "bathtub",
        "unicode": "1f6c1"
    },
    ":battery:": {
        "category": "objects",
        "name": "battery",
        "unicode": "1f50b"
    },
    ":beach:": {
        "category": "travel",
        "name": "beach with umbrella",
        "unicode": "1f3d6",
        "unicode_alt": "1f3d6-fe0f"
    },
    ":beach_umbrella:": {
        "category": "objects",
        "name": "umbrella on ground",
        "unicode": "26f1",
        "unicode_alt": "26f1-fe0f"
    },
    ":bear:": {
        "category": "nature",
        "name": "bear face",
        "unicode": "1f43b"
    },
    ":bed:": {
        "category": "objects",
        "name": "bed",
        "unicode": "1f6cf",
        "unicode_alt": "1f6cf-fe0f"
    },
    ":bee:": {
        "category": "nature",
        "name": "honeybee",
        "unicode": "1f41d"
    },
    ":beer:": {
        "category": "food",
        "name": "beer mug",
        "unicode": "1f37a"
    },
    ":beers:": {
        "category": "food",
        "name": "clinking beer mugs",
        "unicode": "1f37b"
    },
    ":beetle:": {
        "category": "nature",
        "name": "lady beetle",
        "unicode": "1f41e"
    },
    ":beginner:": {
        "category": "symbols",
        "name": "japanese symbol for beginner",
        "unicode": "1f530"
    },
    ":bell:": {
        "category": "symbols",
        "name": "bell",
        "unicode": "1f514"
    },
    ":bellhop:": {
        "category": "objects",
        "name": "bellhop bell",
        "unicode": "1f6ce",
        "unicode_alt": "1f6ce-fe0f"
    },
    ":bento:": {
        "category": "food",
        "name": "bento box",
        "unicode": "1f371"
    },
    ":bicyclist:": {
        "category": "activity",
        "name": "bicyclist",
        "unicode": "1f6b4"
    },
    ":bicyclist_tone1:": {
        "category": "activity",
        "name": "bicyclist tone 1",
        "unicode": "1f6b4-1f3fb"
    },
    ":bicyclist_tone2:": {
        "category": "activity",
        "name": "bicyclist tone 2",
        "unicode": "1f6b4-1f3fc"
    },
    ":bicyclist_tone3:": {
        "category": "activity",
        "name": "bicyclist tone 3",
        "unicode": "1f6b4-1f3fd"
    },
    ":bicyclist_tone4:": {
        "category": "activity",
        "name": "bicyclist tone 4",
        "unicode": "1f6b4-1f3fe"
    },
    ":bicyclist_tone5:": {
        "category": "activity",
        "name": "bicyclist tone 5",
        "unicode": "1f6b4-1f3ff"
    },
    ":bike:": {
        "category": "travel",
        "name": "bicycle",
        "unicode": "1f6b2"
    },
    ":bikini:": {
        "category": "people",
        "name": "bikini",
        "unicode": "1f459"
    },
    ":biohazard:": {
        "category": "symbols",
        "name": "biohazard sign",
        "unicode": "2623",
        "unicode_alt": "2623-fe0f"
    },
    ":bird:": {
        "category": "nature",
        "name": "bird",
        "unicode": "1f426"
    },
    ":birthday:": {
        "category": "food",
        "name": "birthday cake",
        "unicode": "1f382"
    },
    ":black_circle:": {
        "category": "symbols",
        "name": "black circle",
        "unicode": "26ab",
        "unicode_alt": "26ab-fe0f"
    },
    ":black_heart:": {
        "category": "symbols",
        "name": "black heart",
        "unicode": "1f5a4"
    },
    ":black_joker:": {
        "category": "symbols",
        "name": "playing card black joker",
        "unicode": "1f0cf"
    },
    ":black_large_square:": {
        "category": "symbols",
        "name": "black large square",
        "unicode": "2b1b",
        "unicode_alt": "2b1b-fe0f"
    },
    ":black_medium_small_square:": {
        "category": "symbols",
        "name": "black medium small square",
        "unicode": "25fe",
        "unicode_alt": "25fe-fe0f"
    },
    ":black_medium_square:": {
        "category": "symbols",
        "name": "black medium square",
        "unicode": "25fc",
        "unicode_alt": "25fc-fe0f"
    },
    ":black_nib:": {
        "category": "objects",
        "name": "black nib",
        "unicode": "2712",
        "unicode_alt": "2712-fe0f"
    },
    ":black_small_square:": {
        "category": "symbols",
        "name": "black small square",
        "unicode": "25aa",
        "unicode_alt": "25aa-fe0f"
    },
    ":black_square_button:": {
        "category": "symbols",
        "name": "black square button",
        "unicode": "1f532"
    },
    ":blossom:": {
        "category": "nature",
        "name": "blossom",
        "unicode": "1f33c"
    },
    ":blowfish:": {
        "category": "nature",
        "name": "blowfish",
        "unicode": "1f421"
    },
    ":blue_book:": {
        "category": "objects",
        "name": "blue book",
        "unicode": "1f4d8"
    },
    ":blue_car:": {
        "category": "travel",
        "name": "recreational vehicle",
        "unicode": "1f699"
    },
    ":blue_circle:": {
        "category": "symbols",
        "name": "blue circle",
        "unicode": "1f535"
    },
    ":blue_heart:": {
        "category": "symbols",
        "name": "blue heart",
        "unicode": "1f499"
    },
    ":blush:": {
        "category": "people",
        "name": "smiling face with smiling eyes",
        "unicode": "1f60a"
    },
    ":boar:": {
        "category": "nature",
        "name": "boar",
        "unicode": "1f417"
    },
    ":bomb:": {
        "category": "objects",
        "name": "bomb",
        "unicode": "1f4a3"
    },
    ":book:": {
        "category": "objects",
        "name": "open book",
        "unicode": "1f4d6"
    },
    ":bookmark:": {
        "category": "objects",
        "name": "bookmark",
        "unicode": "1f516"
    },
    ":bookmark_tabs:": {
        "category": "objects",
        "name": "bookmark tabs",
        "unicode": "1f4d1"
    },
    ":books:": {
        "category": "objects",
        "name": "books",
        "unicode": "1f4da"
    },
    ":boom:": {
        "category": "symbols",
        "name": "collision symbol",
        "unicode": "1f4a5"
    },
    ":boot:": {
        "category": "people",
        "name": "womans boots",
        "unicode": "1f462"
    },
    ":bouquet:": {
        "category": "nature",
        "name": "bouquet",
        "unicode": "1f490"
    },
    ":bow:": {
        "category": "people",
        "name": "person bowing deeply",
        "unicode": "1f647"
    },
    ":bow_and_arrow:": {
        "category": "activity",
        "name": "bow and arrow",
        "unicode": "1f3f9"
    },
    ":bow_tone1:": {
        "category": "people",
        "name": "person bowing deeply tone 1",
        "unicode": "1f647-1f3fb"
    },
    ":bow_tone2:": {
        "category": "people",
        "name": "person bowing deeply tone 2",
        "unicode": "1f647-1f3fc"
    },
    ":bow_tone3:": {
        "category": "people",
        "name": "person bowing deeply tone 3",
        "unicode": "1f647-1f3fd"
    },
    ":bow_tone4:": {
        "category": "people",
        "name": "person bowing deeply tone 4",
        "unicode": "1f647-1f3fe"
    },
    ":bow_tone5:": {
        "category": "people",
        "name": "person bowing deeply tone 5",
        "unicode": "1f647-1f3ff"
    },
    ":bowling:": {
        "category": "activity",
        "name": "bowling",
        "unicode": "1f3b3"
    },
    ":boxing_glove:": {
        "category": "activity",
        "name": "boxing glove",
        "unicode": "1f94a"
    },
    ":boy:": {
        "category": "people",
        "name": "boy",
        "unicode": "1f466"
    },
    ":boy_tone1:": {
        "category": "people",
        "name": "boy tone 1",
        "unicode": "1f466-1f3fb"
    },
    ":boy_tone2:": {
        "category": "people",
        "name": "boy tone 2",
        "unicode": "1f466-1f3fc"
    },
    ":boy_tone3:": {
        "category": "people",
        "name": "boy tone 3",
        "unicode": "1f466-1f3fd"
    },
    ":boy_tone4:": {
        "category": "people",
        "name": "boy tone 4",
        "unicode": "1f466-1f3fe"
    },
    ":boy_tone5:": {
        "category": "people",
        "name": "boy tone 5",
        "unicode": "1f466-1f3ff"
    },
    ":bread:": {
        "category": "food",
        "name": "bread",
        "unicode": "1f35e"
    },
    ":bride_with_veil:": {
        "category": "people",
        "name": "bride with veil",
        "unicode": "1f470"
    },
    ":bride_with_veil_tone1:": {
        "category": "people",
        "name": "bride with veil tone 1",
        "unicode": "1f470-1f3fb"
    },
    ":bride_with_veil_tone2:": {
        "category": "people",
        "name": "bride with veil tone 2",
        "unicode": "1f470-1f3fc"
    },
    ":bride_with_veil_tone3:": {
        "category": "people",
        "name": "bride with veil tone 3",
        "unicode": "1f470-1f3fd"
    },
    ":bride_with_veil_tone4:": {
        "category": "people",
        "name": "bride with veil tone 4",
        "unicode": "1f470-1f3fe"
    },
    ":bride_with_veil_tone5:": {
        "category": "people",
        "name": "bride with veil tone 5",
        "unicode": "1f470-1f3ff"
    },
    ":bridge_at_night:": {
        "category": "travel",
        "name": "bridge at night",
        "unicode": "1f309"
    },
    ":briefcase:": {
        "category": "people",
        "name": "briefcase",
        "unicode": "1f4bc"
    },
    ":broken_heart:": {
        "category": "symbols",
        "name": "broken heart",
        "unicode": "1f494"
    },
    ":bug:": {
        "category": "nature",
        "name": "bug",
        "unicode": "1f41b"
    },
    ":bulb:": {
        "category": "objects",
        "name": "electric light bulb",
        "unicode": "1f4a1"
    },
    ":bullettrain_front:": {
        "category": "travel",
        "name": "high-speed train with bullet nose",
        "unicode": "1f685"
    },
    ":bullettrain_side:": {
        "category": "travel",
        "name": "high-speed train",
        "unicode": "1f684"
    },
    ":burrito:": {
        "category": "food",
        "name": "burrito",
        "unicode": "1f32f"
    },
    ":bus:": {
        "category": "travel",
        "name": "bus",
        "unicode": "1f68c"
    },
    ":busstop:": {
        "category": "travel",
        "name": "bus stop",
        "unicode": "1f68f"
    },
    ":bust_in_silhouette:": {
        "category": "people",
        "name": "bust in silhouette",
        "unicode": "1f464"
    },
    ":busts_in_silhouette:": {
        "category": "people",
        "name": "busts in silhouette",
        "unicode": "1f465"
    },
    ":butterfly:": {
        "category": "nature",
        "name": "butterfly",
        "unicode": "1f98b"
    },
    ":cactus:": {
        "category": "nature",
        "name": "cactus",
        "unicode": "1f335"
    },
    ":cake:": {
        "category": "food",
        "name": "shortcake",
        "unicode": "1f370"
    },
    ":calendar:": {
        "category": "objects",
        "name": "tear-off calendar",
        "unicode": "1f4c6"
    },
    ":calendar_spiral:": {
        "category": "objects",
        "name": "spiral calendar pad",
        "unicode": "1f5d3",
        "unicode_alt": "1f5d3-fe0f"
    },
    ":call_me:": {
        "category": "people",
        "name": "call me hand",
        "unicode": "1f919"
    },
    ":call_me_tone1:": {
        "category": "people",
        "name": "call me hand tone 1",
        "unicode": "1f919-1f3fb"
    },
    ":call_me_tone2:": {
        "category": "people",
        "name": "call me hand tone 2",
        "unicode": "1f919-1f3fc"
    },
    ":call_me_tone3:": {
        "category": "people",
        "name": "call me hand tone 3",
        "unicode": "1f919-1f3fd"
    },
    ":call_me_tone4:": {
        "category": "people",
        "name": "call me hand tone 4",
        "unicode": "1f919-1f3fe"
    },
    ":call_me_tone5:": {
        "category": "people",
        "name": "call me hand tone 5",
        "unicode": "1f919-1f3ff"
    },
    ":calling:": {
        "category": "objects",
        "name": "mobile phone with rightwards arrow at left",
        "unicode": "1f4f2"
    },
    ":camel:": {
        "category": "nature",
        "name": "bactrian camel",
        "unicode": "1f42b"
    },
    ":camera:": {
        "category": "objects",
        "name": "camera",
        "unicode": "1f4f7"
    },
    ":camera_with_flash:": {
        "category": "objects",
        "name": "camera with flash",
        "unicode": "1f4f8"
    },
    ":camping:": {
        "category": "travel",
        "name": "camping",
        "unicode": "1f3d5",
        "unicode_alt": "1f3d5-fe0f"
    },
    ":cancer:": {
        "category": "symbols",
        "name": "cancer",
        "unicode": "264b",
        "unicode_alt": "264b-fe0f"
    },
    ":candle:": {
        "category": "objects",
        "name": "candle",
        "unicode": "1f56f",
        "unicode_alt": "1f56f-fe0f"
    },
    ":candy:": {
        "category": "food",
        "name": "candy",
        "unicode": "1f36c"
    },
    ":canoe:": {
        "category": "travel",
        "name": "canoe",
        "unicode": "1f6f6"
    },
    ":capital_abcd:": {
        "category": "symbols",
        "name": "input symbol for latin capital letters",
        "unicode": "1f520"
    },
    ":capricorn:": {
        "category": "symbols",
        "name": "capricorn",
        "unicode": "2651",
        "unicode_alt": "2651-fe0f"
    },
    ":card_box:": {
        "category": "objects",
        "name": "card file box",
        "unicode": "1f5c3",
        "unicode_alt": "1f5c3-fe0f"
    },
    ":card_index:": {
        "category": "objects",
        "name": "card index",
        "unicode": "1f4c7"
    },
    ":carousel_horse:": {
        "category": "travel",
        "name": "carousel horse",
        "unicode": "1f3a0"
    },
    ":carrot:": {
        "category": "food",
        "name": "carrot",
        "unicode": "1f955"
    },
    ":cartwheel:": {
        "category": "activity",
        "name": "person doing cartwheel",
        "unicode": "1f938"
    },
    ":cartwheel_tone1:": {
        "category": "activity",
        "name": "person doing cartwheel tone 1",
        "unicode": "1f938-1f3fb"
    },
    ":cartwheel_tone2:": {
        "category": "activity",
        "name": "person doing cartwheel tone 2",
        "unicode": "1f938-1f3fc"
    },
    ":cartwheel_tone3:": {
        "category": "activity",
        "name": "person doing cartwheel tone 3",
        "unicode": "1f938-1f3fd"
    },
    ":cartwheel_tone4:": {
        "category": "activity",
        "name": "person doing cartwheel tone 4",
        "unicode": "1f938-1f3fe"
    },
    ":cartwheel_tone5:": {
        "category": "activity",
        "name": "person doing cartwheel tone 5",
        "unicode": "1f938-1f3ff"
    },
    ":cat2:": {
        "category": "nature",
        "name": "cat",
        "unicode": "1f408"
    },
    ":cat:": {
        "category": "nature",
        "name": "cat face",
        "unicode": "1f431"
    },
    ":cd:": {
        "category": "objects",
        "name": "optical disc",
        "unicode": "1f4bf"
    },
    ":chains:": {
        "category": "objects",
        "name": "chains",
        "unicode": "26d3",
        "unicode_alt": "26d3-fe0f"
    },
    ":champagne:": {
        "category": "food",
        "name": "bottle with popping cork",
        "unicode": "1f37e"
    },
    ":champagne_glass:": {
        "category": "food",
        "name": "clinking glasses",
        "unicode": "1f942"
    },
    ":chart:": {
        "category": "symbols",
        "name": "chart with upwards trend and yen sign",
        "unicode": "1f4b9"
    },
    ":chart_with_downwards_trend:": {
        "category": "objects",
        "name": "chart with downwards trend",
        "unicode": "1f4c9"
    },
    ":chart_with_upwards_trend:": {
        "category": "objects",
        "name": "chart with upwards trend",
        "unicode": "1f4c8"
    },
    ":checkered_flag:": {
        "category": "travel",
        "name": "chequered flag",
        "unicode": "1f3c1"
    },
    ":cheese:": {
        "category": "food",
        "name": "cheese wedge",
        "unicode": "1f9c0"
    },
    ":cherries:": {
        "category": "food",
        "name": "cherries",
        "unicode": "1f352"
    },
    ":cherry_blossom:": {
        "category": "nature",
        "name": "cherry blossom",
        "unicode": "1f338"
    },
    ":chestnut:": {
        "category": "nature",
        "name": "chestnut",
        "unicode": "1f330"
    },
    ":chicken:": {
        "category": "nature",
        "name": "chicken",
        "unicode": "1f414"
    },
    ":children_crossing:": {
        "category": "symbols",
        "name": "children crossing",
        "unicode": "1f6b8"
    },
    ":chipmunk:": {
        "category": "nature",
        "name": "chipmunk",
        "unicode": "1f43f",
        "unicode_alt": "1f43f-fe0f"
    },
    ":chocolate_bar:": {
        "category": "food",
        "name": "chocolate bar",
        "unicode": "1f36b"
    },
    ":christmas_tree:": {
        "category": "nature",
        "name": "christmas tree",
        "unicode": "1f384"
    },
    ":church:": {
        "category": "travel",
        "name": "church",
        "unicode": "26ea",
        "unicode_alt": "26ea-fe0f"
    },
    ":cinema:": {
        "category": "symbols",
        "name": "cinema",
        "unicode": "1f3a6"
    },
    ":circus_tent:": {
        "category": "activity",
        "name": "circus tent",
        "unicode": "1f3aa"
    },
    ":city_dusk:": {
        "category": "travel",
        "name": "cityscape at dusk",
        "unicode": "1f306"
    },
    ":city_sunset:": {
        "category": "travel",
        "name": "sunset over buildings",
        "unicode": "1f307"
    },
    ":cityscape:": {
        "category": "travel",
        "name": "cityscape",
        "unicode": "1f3d9",
        "unicode_alt": "1f3d9-fe0f"
    },
    ":cl:": {
        "category": "symbols",
        "name": "squared cl",
        "unicode": "1f191"
    },
    ":clap:": {
        "category": "people",
        "name": "clapping hands sign",
        "unicode": "1f44f"
    },
    ":clap_tone1:": {
        "category": "people",
        "name": "clapping hands sign tone 1",
        "unicode": "1f44f-1f3fb"
    },
    ":clap_tone2:": {
        "category": "people",
        "name": "clapping hands sign tone 2",
        "unicode": "1f44f-1f3fc"
    },
    ":clap_tone3:": {
        "category": "people",
        "name": "clapping hands sign tone 3",
        "unicode": "1f44f-1f3fd"
    },
    ":clap_tone4:": {
        "category": "people",
        "name": "clapping hands sign tone 4",
        "unicode": "1f44f-1f3fe"
    },
    ":clap_tone5:": {
        "category": "people",
        "name": "clapping hands sign tone 5",
        "unicode": "1f44f-1f3ff"
    },
    ":clapper:": {
        "category": "activity",
        "name": "clapper board",
        "unicode": "1f3ac"
    },
    ":classical_building:": {
        "category": "travel",
        "name": "classical building",
        "unicode": "1f3db",
        "unicode_alt": "1f3db-fe0f"
    },
    ":clipboard:": {
        "category": "objects",
        "name": "clipboard",
        "unicode": "1f4cb"
    },
    ":clock1030:": {
        "category": "symbols",
        "name": "clock face ten-thirty",
        "unicode": "1f565"
    },
    ":clock10:": {
        "category": "symbols",
        "name": "clock face ten oclock",
        "unicode": "1f559"
    },
    ":clock1130:": {
        "category": "symbols",
        "name": "clock face eleven-thirty",
        "unicode": "1f566"
    },
    ":clock11:": {
        "category": "symbols",
        "name": "clock face eleven oclock",
        "unicode": "1f55a"
    },
    ":clock1230:": {
        "category": "symbols",
        "name": "clock face twelve-thirty",
        "unicode": "1f567"
    },
    ":clock12:": {
        "category": "symbols",
        "name": "clock face twelve oclock",
        "unicode": "1f55b"
    },
    ":clock130:": {
        "category": "symbols",
        "name": "clock face one-thirty",
        "unicode": "1f55c"
    },
    ":clock1:": {
        "category": "symbols",
        "name": "clock face one oclock",
        "unicode": "1f550"
    },
    ":clock230:": {
        "category": "symbols",
        "name": "clock face two-thirty",
        "unicode": "1f55d"
    },
    ":clock2:": {
        "category": "symbols",
        "name": "clock face two oclock",
        "unicode": "1f551"
    },
    ":clock330:": {
        "category": "symbols",
        "name": "clock face three-thirty",
        "unicode": "1f55e"
    },
    ":clock3:": {
        "category": "symbols",
        "name": "clock face three oclock",
        "unicode": "1f552"
    },
    ":clock430:": {
        "category": "symbols",
        "name": "clock face four-thirty",
        "unicode": "1f55f"
    },
    ":clock4:": {
        "category": "symbols",
        "name": "clock face four oclock",
        "unicode": "1f553"
    },
    ":clock530:": {
        "category": "symbols",
        "name": "clock face five-thirty",
        "unicode": "1f560"
    },
    ":clock5:": {
        "category": "symbols",
        "name": "clock face five oclock",
        "unicode": "1f554"
    },
    ":clock630:": {
        "category": "symbols",
        "name": "clock face six-thirty",
        "unicode": "1f561"
    },
    ":clock6:": {
        "category": "symbols",
        "name": "clock face six oclock",
        "unicode": "1f555"
    },
    ":clock730:": {
        "category": "symbols",
        "name": "clock face seven-thirty",
        "unicode": "1f562"
    },
    ":clock7:": {
        "category": "symbols",
        "name": "clock face seven oclock",
        "unicode": "1f556"
    },
    ":clock830:": {
        "category": "symbols",
        "name": "clock face eight-thirty",
        "unicode": "1f563"
    },
    ":clock8:": {
        "category": "symbols",
        "name": "clock face eight oclock",
        "unicode": "1f557"
    },
    ":clock930:": {
        "category": "symbols",
        "name": "clock face nine-thirty",
        "unicode": "1f564"
    },
    ":clock9:": {
        "category": "symbols",
        "name": "clock face nine oclock",
        "unicode": "1f558"
    },
    ":clock:": {
        "category": "objects",
        "name": "mantlepiece clock",
        "unicode": "1f570",
        "unicode_alt": "1f570-fe0f"
    },
    ":closed_book:": {
        "category": "objects",
        "name": "closed book",
        "unicode": "1f4d5"
    },
    ":closed_lock_with_key:": {
        "category": "objects",
        "name": "closed lock with key",
        "unicode": "1f510"
    },
    ":closed_umbrella:": {
        "category": "people",
        "name": "closed umbrella",
        "unicode": "1f302"
    },
    ":cloud:": {
        "category": "nature",
        "name": "cloud",
        "unicode": "2601",
        "unicode_alt": "2601-fe0f"
    },
    ":cloud_lightning:": {
        "category": "nature",
        "name": "cloud with lightning",
        "unicode": "1f329",
        "unicode_alt": "1f329-fe0f"
    },
    ":cloud_rain:": {
        "category": "nature",
        "name": "cloud with rain",
        "unicode": "1f327",
        "unicode_alt": "1f327-fe0f"
    },
    ":cloud_snow:": {
        "category": "nature",
        "name": "cloud with snow",
        "unicode": "1f328",
        "unicode_alt": "1f328-fe0f"
    },
    ":cloud_tornado:": {
        "category": "nature",
        "name": "cloud with tornado",
        "unicode": "1f32a",
        "unicode_alt": "1f32a-fe0f"
    },
    ":clown:": {
        "category": "people",
        "name": "clown face",
        "unicode": "1f921"
    },
    ":clubs:": {
        "category": "symbols",
        "name": "black club suit",
        "unicode": "2663",
        "unicode_alt": "2663-fe0f"
    },
    ":cocktail:": {
        "category": "food",
        "name": "cocktail glass",
        "unicode": "1f378"
    },
    ":coffee:": {
        "category": "food",
        "name": "hot beverage",
        "unicode": "2615",
        "unicode_alt": "2615-fe0f"
    },
    ":coffin:": {
        "category": "objects",
        "name": "coffin",
        "unicode": "26b0",
        "unicode_alt": "26b0-fe0f"
    },
    ":cold_sweat:": {
        "category": "people",
        "name": "face with open mouth and cold sweat",
        "unicode": "1f630"
    },
    ":comet:": {
        "category": "nature",
        "name": "comet",
        "unicode": "2604",
        "unicode_alt": "2604-fe0f"
    },
    ":compression:": {
        "category": "objects",
        "name": "compression",
        "unicode": "1f5dc",
        "unicode_alt": "1f5dc-fe0f"
    },
    ":computer:": {
        "category": "objects",
        "name": "personal computer",
        "unicode": "1f4bb"
    },
    ":confetti_ball:": {
        "category": "objects",
        "name": "confetti ball",
        "unicode": "1f38a"
    },
    ":confounded:": {
        "category": "people",
        "name": "confounded face",
        "unicode": "1f616"
    },
    ":confused:": {
        "category": "people",
        "name": "confused face",
        "unicode": "1f615"
    },
    ":congratulations:": {
        "category": "symbols",
        "name": "circled ideograph congratulation",
        "unicode": "3297",
        "unicode_alt": "3297-fe0f"
    },
    ":construction:": {
        "category": "travel",
        "name": "construction sign",
        "unicode": "1f6a7"
    },
    ":construction_site:": {
        "category": "travel",
        "name": "building construction",
        "unicode": "1f3d7",
        "unicode_alt": "1f3d7-fe0f"
    },
    ":construction_worker:": {
        "category": "people",
        "name": "construction worker",
        "unicode": "1f477"
    },
    ":construction_worker_tone1:": {
        "category": "people",
        "name": "construction worker tone 1",
        "unicode": "1f477-1f3fb"
    },
    ":construction_worker_tone2:": {
        "category": "people",
        "name": "construction worker tone 2",
        "unicode": "1f477-1f3fc"
    },
    ":construction_worker_tone3:": {
        "category": "people",
        "name": "construction worker tone 3",
        "unicode": "1f477-1f3fd"
    },
    ":construction_worker_tone4:": {
        "category": "people",
        "name": "construction worker tone 4",
        "unicode": "1f477-1f3fe"
    },
    ":construction_worker_tone5:": {
        "category": "people",
        "name": "construction worker tone 5",
        "unicode": "1f477-1f3ff"
    },
    ":control_knobs:": {
        "category": "objects",
        "name": "control knobs",
        "unicode": "1f39b",
        "unicode_alt": "1f39b-fe0f"
    },
    ":convenience_store:": {
        "category": "travel",
        "name": "convenience store",
        "unicode": "1f3ea"
    },
    ":cookie:": {
        "category": "food",
        "name": "cookie",
        "unicode": "1f36a"
    },
    ":cooking:": {
        "category": "food",
        "name": "cooking",
        "unicode": "1f373"
    },
    ":cool:": {
        "category": "symbols",
        "name": "squared cool",
        "unicode": "1f192"
    },
    ":cop:": {
        "category": "people",
        "name": "police officer",
        "unicode": "1f46e"
    },
    ":cop_tone1:": {
        "category": "people",
        "name": "police officer tone 1",
        "unicode": "1f46e-1f3fb"
    },
    ":cop_tone2:": {
        "category": "people",
        "name": "police officer tone 2",
        "unicode": "1f46e-1f3fc"
    },
    ":cop_tone3:": {
        "category": "people",
        "name": "police officer tone 3",
        "unicode": "1f46e-1f3fd"
    },
    ":cop_tone4:": {
        "category": "people",
        "name": "police officer tone 4",
        "unicode": "1f46e-1f3fe"
    },
    ":cop_tone5:": {
        "category": "people",
        "name": "police officer tone 5",
        "unicode": "1f46e-1f3ff"
    },
    ":copyright:": {
        "category": "symbols",
        "name": "copyright sign",
        "unicode": "00a9",
        "unicode_alt": "00a9-fe0f"
    },
    ":corn:": {
        "category": "food",
        "name": "ear of maize",
        "unicode": "1f33d"
    },
    ":couch:": {
        "category": "objects",
        "name": "couch and lamp",
        "unicode": "1f6cb",
        "unicode_alt": "1f6cb-fe0f"
    },
    ":couple:": {
        "category": "people",
        "name": "man and woman holding hands",
        "unicode": "1f46b"
    },
    ":couple_mm:": {
        "category": "people",
        "name": "couple (man,man)",
        "unicode": "1f468-2764-1f468",
        "unicode_alt": "1f468-200d-2764-fe0f-200d-1f468"
    },
    ":couple_with_heart:": {
        "category": "people",
        "name": "couple with heart",
        "unicode": "1f491"
    },
    ":couple_ww:": {
        "category": "people",
        "name": "couple (woman,woman)",
        "unicode": "1f469-2764-1f469",
        "unicode_alt": "1f469-200d-2764-fe0f-200d-1f469"
    },
    ":couplekiss:": {
        "category": "people",
        "name": "kiss",
        "unicode": "1f48f"
    },
    ":cow2:": {
        "category": "nature",
        "name": "cow",
        "unicode": "1f404"
    },
    ":cow:": {
        "category": "nature",
        "name": "cow face",
        "unicode": "1f42e"
    },
    ":cowboy:": {
        "category": "people",
        "name": "face with cowboy hat",
        "unicode": "1f920"
    },
    ":crab:": {
        "category": "nature",
        "name": "crab",
        "unicode": "1f980"
    },
    ":crayon:": {
        "category": "objects",
        "name": "lower left crayon",
        "unicode": "1f58d",
        "unicode_alt": "1f58d-fe0f"
    },
    ":credit_card:": {
        "category": "objects",
        "name": "credit card",
        "unicode": "1f4b3"
    },
    ":crescent_moon:": {
        "category": "nature",
        "name": "crescent moon",
        "unicode": "1f319"
    },
    ":cricket:": {
        "category": "activity",
        "name": "cricket bat and ball",
        "unicode": "1f3cf"
    },
    ":crocodile:": {
        "category": "nature",
        "name": "crocodile",
        "unicode": "1f40a"
    },
    ":croissant:": {
        "category": "food",
        "name": "croissant",
        "unicode": "1f950"
    },
    ":cross:": {
        "category": "symbols",
        "name": "latin cross",
        "unicode": "271d",
        "unicode_alt": "271d-fe0f"
    },
    ":crossed_flags:": {
        "category": "objects",
        "name": "crossed flags",
        "unicode": "1f38c"
    },
    ":crossed_swords:": {
        "category": "objects",
        "name": "crossed swords",
        "unicode": "2694",
        "unicode_alt": "2694-fe0f"
    },
    ":crown:": {
        "category": "people",
        "name": "crown",
        "unicode": "1f451"
    },
    ":cruise_ship:": {
        "category": "travel",
        "name": "passenger ship",
        "unicode": "1f6f3",
        "unicode_alt": "1f6f3-fe0f"
    },
    ":cry:": {
        "category": "people",
        "name": "crying face",
        "unicode": "1f622"
    },
    ":crying_cat_face:": {
        "category": "people",
        "name": "crying cat face",
        "unicode": "1f63f"
    },
    ":crystal_ball:": {
        "category": "objects",
        "name": "crystal ball",
        "unicode": "1f52e"
    },
    ":cucumber:": {
        "category": "food",
        "name": "cucumber",
        "unicode": "1f952"
    },
    ":cupid:": {
        "category": "symbols",
        "name": "heart with arrow",
        "unicode": "1f498"
    },
    ":curly_loop:": {
        "category": "symbols",
        "name": "curly loop",
        "unicode": "27b0"
    },
    ":currency_exchange:": {
        "category": "symbols",
        "name": "currency exchange",
        "unicode": "1f4b1"
    },
    ":curry:": {
        "category": "food",
        "name": "curry and rice",
        "unicode": "1f35b"
    },
    ":custard:": {
        "category": "food",
        "name": "custard",
        "unicode": "1f36e"
    },
    ":customs:": {
        "category": "symbols",
        "name": "customs",
        "unicode": "1f6c3"
    },
    ":cyclone:": {
        "category": "symbols",
        "name": "cyclone",
        "unicode": "1f300"
    },
    ":dagger:": {
        "category": "objects",
        "name": "dagger knife",
        "unicode": "1f5e1",
        "unicode_alt": "1f5e1-fe0f"
    },
    ":dancer:": {
        "category": "people",
        "name": "dancer",
        "unicode": "1f483"
    },
    ":dancer_tone1:": {
        "category": "people",
        "name": "dancer tone 1",
        "unicode": "1f483-1f3fb"
    },
    ":dancer_tone2:": {
        "category": "people",
        "name": "dancer tone 2",
        "unicode": "1f483-1f3fc"
    },
    ":dancer_tone3:": {
        "category": "people",
        "name": "dancer tone 3",
        "unicode": "1f483-1f3fd"
    },
    ":dancer_tone4:": {
        "category": "people",
        "name": "dancer tone 4",
        "unicode": "1f483-1f3fe"
    },
    ":dancer_tone5:": {
        "category": "people",
        "name": "dancer tone 5",
        "unicode": "1f483-1f3ff"
    },
    ":dancers:": {
        "category": "people",
        "name": "woman with bunny ears",
        "unicode": "1f46f"
    },
    ":dango:": {
        "category": "food",
        "name": "dango",
        "unicode": "1f361"
    },
    ":dark_sunglasses:": {
        "category": "people",
        "name": "dark sunglasses",
        "unicode": "1f576",
        "unicode_alt": "1f576-fe0f"
    },
    ":dart:": {
        "category": "activity",
        "name": "direct hit",
        "unicode": "1f3af"
    },
    ":dash:": {
        "category": "nature",
        "name": "dash symbol",
        "unicode": "1f4a8"
    },
    ":date:": {
        "category": "objects",
        "name": "calendar",
        "unicode": "1f4c5"
    },
    ":deciduous_tree:": {
        "category": "nature",
        "name": "deciduous tree",
        "unicode": "1f333"
    },
    ":deer:": {
        "category": "nature",
        "name": "deer",
        "unicode": "1f98c"
    },
    ":department_store:": {
        "category": "travel",
        "name": "department store",
        "unicode": "1f3ec"
    },
    ":desert:": {
        "category": "travel",
        "name": "desert",
        "unicode": "1f3dc",
        "unicode_alt": "1f3dc-fe0f"
    },
    ":desktop:": {
        "category": "objects",
        "name": "desktop computer",
        "unicode": "1f5a5",
        "unicode_alt": "1f5a5-fe0f"
    },
    ":diamond_shape_with_a_dot_inside:": {
        "category": "symbols",
        "name": "diamond shape with a dot inside",
        "unicode": "1f4a0"
    },
    ":diamonds:": {
        "category": "symbols",
        "name": "black diamond suit",
        "unicode": "2666",
        "unicode_alt": "2666-fe0f"
    },
    ":disappointed:": {
        "category": "people",
        "name": "disappointed face",
        "unicode": "1f61e"
    },
    ":disappointed_relieved:": {
        "category": "people",
        "name": "disappointed but relieved face",
        "unicode": "1f625"
    },
    ":dividers:": {
        "category": "objects",
        "name": "card index dividers",
        "unicode": "1f5c2",
        "unicode_alt": "1f5c2-fe0f"
    },
    ":dizzy:": {
        "category": "symbols",
        "name": "dizzy symbol",
        "unicode": "1f4ab"
    },
    ":dizzy_face:": {
        "category": "people",
        "name": "dizzy face",
        "unicode": "1f635"
    },
    ":do_not_litter:": {
        "category": "symbols",
        "name": "do not litter symbol",
        "unicode": "1f6af"
    },
    ":dog2:": {
        "category": "nature",
        "name": "dog",
        "unicode": "1f415"
    },
    ":dog:": {
        "category": "nature",
        "name": "dog face",
        "unicode": "1f436"
    },
    ":dollar:": {
        "category": "objects",
        "name": "banknote with dollar sign",
        "unicode": "1f4b5"
    },
    ":dolls:": {
        "category": "objects",
        "name": "japanese dolls",
        "unicode": "1f38e"
    },
    ":dolphin:": {
        "category": "nature",
        "name": "dolphin",
        "unicode": "1f42c"
    },
    ":door:": {
        "category": "objects",
        "name": "door",
        "unicode": "1f6aa"
    },
    ":doughnut:": {
        "category": "food",
        "name": "doughnut",
        "unicode": "1f369"
    },
    ":dove:": {
        "category": "nature",
        "name": "dove of peace",
        "unicode": "1f54a",
        "unicode_alt": "1f54a-fe0f"
    },
    ":dragon:": {
        "category": "nature",
        "name": "dragon",
        "unicode": "1f409"
    },
    ":dragon_face:": {
        "category": "nature",
        "name": "dragon face",
        "unicode": "1f432"
    },
    ":dress:": {
        "category": "people",
        "name": "dress",
        "unicode": "1f457"
    },
    ":dromedary_camel:": {
        "category": "nature",
        "name": "dromedary camel",
        "unicode": "1f42a"
    },
    ":drooling_face:": {
        "category": "people",
        "name": "drooling face",
        "unicode": "1f924"
    },
    ":droplet:": {
        "category": "nature",
        "name": "droplet",
        "unicode": "1f4a7"
    },
    ":drum:": {
        "category": "activity",
        "name": "drum with drumsticks",
        "unicode": "1f941"
    },
    ":duck:": {
        "category": "nature",
        "name": "duck",
        "unicode": "1f986"
    },
    ":dvd:": {
        "category": "objects",
        "name": "dvd",
        "unicode": "1f4c0"
    },
    ":e-mail:": {
        "category": "objects",
        "name": "e-mail symbol",
        "unicode": "1f4e7"
    },
    ":eagle:": {
        "category": "nature",
        "name": "eagle",
        "unicode": "1f985"
    },
    ":ear:": {
        "category": "people",
        "name": "ear",
        "unicode": "1f442"
    },
    ":ear_of_rice:": {
        "category": "nature",
        "name": "ear of rice",
        "unicode": "1f33e"
    },
    ":ear_tone1:": {
        "category": "people",
        "name": "ear tone 1",
        "unicode": "1f442-1f3fb"
    },
    ":ear_tone2:": {
        "category": "people",
        "name": "ear tone 2",
        "unicode": "1f442-1f3fc"
    },
    ":ear_tone3:": {
        "category": "people",
        "name": "ear tone 3",
        "unicode": "1f442-1f3fd"
    },
    ":ear_tone4:": {
        "category": "people",
        "name": "ear tone 4",
        "unicode": "1f442-1f3fe"
    },
    ":ear_tone5:": {
        "category": "people",
        "name": "ear tone 5",
        "unicode": "1f442-1f3ff"
    },
    ":earth_africa:": {
        "category": "nature",
        "name": "earth globe europe-africa",
        "unicode": "1f30d"
    },
    ":earth_americas:": {
        "category": "nature",
        "name": "earth globe americas",
        "unicode": "1f30e"
    },
    ":earth_asia:": {
        "category": "nature",
        "name": "earth globe asia-australia",
        "unicode": "1f30f"
    },
    ":egg:": {
        "category": "food",
        "name": "egg",
        "unicode": "1f95a"
    },
    ":eggplant:": {
        "category": "food",
        "name": "aubergine",
        "unicode": "1f346"
    },
    ":eight:": {
        "category": "symbols",
        "name": "keycap digit eight",
        "unicode": "0038-20e3",
        "unicode_alt": "0038-fe0f-20e3"
    },
    ":eight_pointed_black_star:": {
        "category": "symbols",
        "name": "eight pointed black star",
        "unicode": "2734",
        "unicode_alt": "2734-fe0f"
    },
    ":eight_spoked_asterisk:": {
        "category": "symbols",
        "name": "eight spoked asterisk",
        "unicode": "2733",
        "unicode_alt": "2733-fe0f"
    },
    ":eject:": {
        "category": "symbols",
        "name": "eject symbol",
        "unicode": "23cf",
        "unicode_alt": "23cf-fe0f"
    },
    ":electric_plug:": {
        "category": "objects",
        "name": "electric plug",
        "unicode": "1f50c"
    },
    ":elephant:": {
        "category": "nature",
        "name": "elephant",
        "unicode": "1f418"
    },
    ":end:": {
        "category": "symbols",
        "name": "end with leftwards arrow above",
        "unicode": "1f51a"
    },
    ":envelope:": {
        "category": "objects",
        "name": "envelope",
        "unicode": "2709",
        "unicode_alt": "2709-fe0f"
    },
    ":envelope_with_arrow:": {
        "category": "objects",
        "name": "envelope with downwards arrow above",
        "unicode": "1f4e9"
    },
    ":euro:": {
        "category": "objects",
        "name": "banknote with euro sign",
        "unicode": "1f4b6"
    },
    ":european_castle:": {
        "category": "travel",
        "name": "european castle",
        "unicode": "1f3f0"
    },
    ":european_post_office:": {
        "category": "travel",
        "name": "european post office",
        "unicode": "1f3e4"
    },
    ":evergreen_tree:": {
        "category": "nature",
        "name": "evergreen tree",
        "unicode": "1f332"
    },
    ":exclamation:": {
        "category": "symbols",
        "name": "heavy exclamation mark symbol",
        "unicode": "2757",
        "unicode_alt": "2757-fe0f"
    },
    ":expressionless:": {
        "category": "people",
        "name": "expressionless face",
        "unicode": "1f611"
    },
    ":eye:": {
        "category": "people",
        "name": "eye",
        "unicode": "1f441",
        "unicode_alt": "1f441-fe0f"
    },
    ":eye_in_speech_bubble:": {
        "category": "symbols",
        "name": "eye in speech bubble",
        "unicode": "1f441-1f5e8",
        "unicode_alt": "1f441-200d-1f5e8"
    },
    ":eyeglasses:": {
        "category": "people",
        "name": "eyeglasses",
        "unicode": "1f453"
    },
    ":eyes:": {
        "category": "people",
        "name": "eyes",
        "unicode": "1f440"
    },
    ":face_palm:": {
        "category": "people",
        "name": "face palm",
        "unicode": "1f926"
    },
    ":face_palm_tone1:": {
        "category": "people",
        "name": "face palm tone 1",
        "unicode": "1f926-1f3fb"
    },
    ":face_palm_tone2:": {
        "category": "people",
        "name": "face palm tone 2",
        "unicode": "1f926-1f3fc"
    },
    ":face_palm_tone3:": {
        "category": "people",
        "name": "face palm tone 3",
        "unicode": "1f926-1f3fd"
    },
    ":face_palm_tone4:": {
        "category": "people",
        "name": "face palm tone 4",
        "unicode": "1f926-1f3fe"
    },
    ":face_palm_tone5:": {
        "category": "people",
        "name": "face palm tone 5",
        "unicode": "1f926-1f3ff"
    },
    ":factory:": {
        "category": "travel",
        "name": "factory",
        "unicode": "1f3ed"
    },
    ":fallen_leaf:": {
        "category": "nature",
        "name": "fallen leaf",
        "unicode": "1f342"
    },
    ":family:": {
        "category": "people",
        "name": "family",
        "unicode": "1f46a"
    },
    ":family_mmb:": {
        "category": "people",
        "name": "family (man,man,boy)",
        "unicode": "1f468-1f468-1f466",
        "unicode_alt": "1f468-200d-1f468-200d-1f466"
    },
    ":family_mmbb:": {
        "category": "people",
        "name": "family (man,man,boy,boy)",
        "unicode": "1f468-1f468-1f466-1f466",
        "unicode_alt": "1f468-200d-1f468-200d-1f466-200d-1f466"
    },
    ":family_mmg:": {
        "category": "people",
        "name": "family (man,man,girl)",
        "unicode": "1f468-1f468-1f467",
        "unicode_alt": "1f468-200d-1f468-200d-1f467"
    },
    ":family_mmgb:": {
        "category": "people",
        "name": "family (man,man,girl,boy)",
        "unicode": "1f468-1f468-1f467-1f466",
        "unicode_alt": "1f468-200d-1f468-200d-1f467-200d-1f466"
    },
    ":family_mmgg:": {
        "category": "people",
        "name": "family (man,man,girl,girl)",
        "unicode": "1f468-1f468-1f467-1f467",
        "unicode_alt": "1f468-200d-1f468-200d-1f467-200d-1f467"
    },
    ":family_mwbb:": {
        "category": "people",
        "name": "family (man,woman,boy,boy)",
        "unicode": "1f468-1f469-1f466-1f466",
        "unicode_alt": "1f468-200d-1f469-200d-1f466-200d-1f466"
    },
    ":family_mwg:": {
        "category": "people",
        "name": "family (man,woman,girl)",
        "unicode": "1f468-1f469-1f467",
        "unicode_alt": "1f468-200d-1f469-200d-1f467"
    },
    ":family_mwgb:": {
        "category": "people",
        "name": "family (man,woman,girl,boy)",
        "unicode": "1f468-1f469-1f467-1f466",
        "unicode_alt": "1f468-200d-1f469-200d-1f467-200d-1f466"
    },
    ":family_mwgg:": {
        "category": "people",
        "name": "family (man,woman,girl,girl)",
        "unicode": "1f468-1f469-1f467-1f467",
        "unicode_alt": "1f468-200d-1f469-200d-1f467-200d-1f467"
    },
    ":family_wwb:": {
        "category": "people",
        "name": "family (woman,woman,boy)",
        "unicode": "1f469-1f469-1f466",
        "unicode_alt": "1f469-200d-1f469-200d-1f466"
    },
    ":family_wwbb:": {
        "category": "people",
        "name": "family (woman,woman,boy,boy)",
        "unicode": "1f469-1f469-1f466-1f466",
        "unicode_alt": "1f469-200d-1f469-200d-1f466-200d-1f466"
    },
    ":family_wwg:": {
        "category": "people",
        "name": "family (woman,woman,girl)",
        "unicode": "1f469-1f469-1f467",
        "unicode_alt": "1f469-200d-1f469-200d-1f467"
    },
    ":family_wwgb:": {
        "category": "people",
        "name": "family (woman,woman,girl,boy)",
        "unicode": "1f469-1f469-1f467-1f466",
        "unicode_alt": "1f469-200d-1f469-200d-1f467-200d-1f466"
    },
    ":family_wwgg:": {
        "category": "people",
        "name": "family (woman,woman,girl,girl)",
        "unicode": "1f469-1f469-1f467-1f467",
        "unicode_alt": "1f469-200d-1f469-200d-1f467-200d-1f467"
    },
    ":fast_forward:": {
        "category": "symbols",
        "name": "black right-pointing double triangle",
        "unicode": "23e9"
    },
    ":fax:": {
        "category": "objects",
        "name": "fax machine",
        "unicode": "1f4e0"
    },
    ":fearful:": {
        "category": "people",
        "name": "fearful face",
        "unicode": "1f628"
    },
    ":feet:": {
        "category": "nature",
        "name": "paw prints",
        "unicode": "1f43e"
    },
    ":fencer:": {
        "category": "activity",
        "name": "fencer",
        "unicode": "1f93a"
    },
    ":ferris_wheel:": {
        "category": "travel",
        "name": "ferris wheel",
        "unicode": "1f3a1"
    },
    ":ferry:": {
        "category": "travel",
        "name": "ferry",
        "unicode": "26f4",
        "unicode_alt": "26f4-fe0f"
    },
    ":field_hockey:": {
        "category": "activity",
        "name": "field hockey stick and ball",
        "unicode": "1f3d1"
    },
    ":file_cabinet:": {
        "category": "objects",
        "name": "file cabinet",
        "unicode": "1f5c4",
        "unicode_alt": "1f5c4-fe0f"
    },
    ":file_folder:": {
        "category": "objects",
        "name": "file folder",
        "unicode": "1f4c1"
    },
    ":film_frames:": {
        "category": "objects",
        "name": "film frames",
        "unicode": "1f39e",
        "unicode_alt": "1f39e-fe0f"
    },
    ":fingers_crossed:": {
        "category": "people",
        "name": "hand with first and index finger crossed",
        "unicode": "1f91e"
    },
    ":fingers_crossed_tone1:": {
        "category": "people",
        "name": "hand with index and middle fingers crossed tone 1",
        "unicode": "1f91e-1f3fb"
    },
    ":fingers_crossed_tone2:": {
        "category": "people",
        "name": "hand with index and middle fingers crossed tone 2",
        "unicode": "1f91e-1f3fc"
    },
    ":fingers_crossed_tone3:": {
        "category": "people",
        "name": "hand with index and middle fingers crossed tone 3",
        "unicode": "1f91e-1f3fd"
    },
    ":fingers_crossed_tone4:": {
        "category": "people",
        "name": "hand with index and middle fingers crossed tone 4",
        "unicode": "1f91e-1f3fe"
    },
    ":fingers_crossed_tone5:": {
        "category": "people",
        "name": "hand with index and middle fingers crossed tone 5",
        "unicode": "1f91e-1f3ff"
    },
    ":fire:": {
        "category": "nature",
        "name": "fire",
        "unicode": "1f525"
    },
    ":fire_engine:": {
        "category": "travel",
        "name": "fire engine",
        "unicode": "1f692"
    },
    ":fireworks:": {
        "category": "travel",
        "name": "fireworks",
        "unicode": "1f386"
    },
    ":first_place:": {
        "category": "activity",
        "name": "first place medal",
        "unicode": "1f947"
    },
    ":first_quarter_moon:": {
        "category": "nature",
        "name": "first quarter moon symbol",
        "unicode": "1f313"
    },
    ":first_quarter_moon_with_face:": {
        "category": "nature",
        "name": "first quarter moon with face",
        "unicode": "1f31b"
    },
    ":fish:": {
        "category": "nature",
        "name": "fish",
        "unicode": "1f41f"
    },
    ":fish_cake:": {
        "category": "food",
        "name": "fish cake with swirl design",
        "unicode": "1f365"
    },
    ":fishing_pole_and_fish:": {
        "category": "activity",
        "name": "fishing pole and fish",
        "unicode": "1f3a3"
    },
    ":fist:": {
        "category": "people",
        "name": "raised fist",
        "unicode": "270a"
    },
    ":fist_tone1:": {
        "category": "people",
        "name": "raised fist tone 1",
        "unicode": "270a-1f3fb"
    },
    ":fist_tone2:": {
        "category": "people",
        "name": "raised fist tone 2",
        "unicode": "270a-1f3fc"
    },
    ":fist_tone3:": {
        "category": "people",
        "name": "raised fist tone 3",
        "unicode": "270a-1f3fd"
    },
    ":fist_tone4:": {
        "category": "people",
        "name": "raised fist tone 4",
        "unicode": "270a-1f3fe"
    },
    ":fist_tone5:": {
        "category": "people",
        "name": "raised fist tone 5",
        "unicode": "270a-1f3ff"
    },
    ":five:": {
        "category": "symbols",
        "name": "keycap digit five",
        "unicode": "0035-20e3",
        "unicode_alt": "0035-fe0f-20e3"
    },
    ":flag_ac:": {
        "category": "flags",
        "name": "ascension",
        "unicode": "1f1e6-1f1e8"
    },
    ":flag_ad:": {
        "category": "flags",
        "name": "andorra",
        "unicode": "1f1e6-1f1e9"
    },
    ":flag_ae:": {
        "category": "flags",
        "name": "the united arab emirates",
        "unicode": "1f1e6-1f1ea"
    },
    ":flag_af:": {
        "category": "flags",
        "name": "afghanistan",
        "unicode": "1f1e6-1f1eb"
    },
    ":flag_ag:": {
        "category": "flags",
        "name": "antigua and barbuda",
        "unicode": "1f1e6-1f1ec"
    },
    ":flag_ai:": {
        "category": "flags",
        "name": "anguilla",
        "unicode": "1f1e6-1f1ee"
    },
    ":flag_al:": {
        "category": "flags",
        "name": "albania",
        "unicode": "1f1e6-1f1f1"
    },
    ":flag_am:": {
        "category": "flags",
        "name": "armenia",
        "unicode": "1f1e6-1f1f2"
    },
    ":flag_ao:": {
        "category": "flags",
        "name": "angola",
        "unicode": "1f1e6-1f1f4"
    },
    ":flag_aq:": {
        "category": "flags",
        "name": "antarctica",
        "unicode": "1f1e6-1f1f6"
    },
    ":flag_ar:": {
        "category": "flags",
        "name": "argentina",
        "unicode": "1f1e6-1f1f7"
    },
    ":flag_as:": {
        "category": "flags",
        "name": "american samoa",
        "unicode": "1f1e6-1f1f8"
    },
    ":flag_at:": {
        "category": "flags",
        "name": "austria",
        "unicode": "1f1e6-1f1f9"
    },
    ":flag_au:": {
        "category": "flags",
        "name": "australia",
        "unicode": "1f1e6-1f1fa"
    },
    ":flag_aw:": {
        "category": "flags",
        "name": "aruba",
        "unicode": "1f1e6-1f1fc"
    },
    ":flag_ax:": {
        "category": "flags",
        "name": "\u00e5land islands",
        "unicode": "1f1e6-1f1fd"
    },
    ":flag_az:": {
        "category": "flags",
        "name": "azerbaijan",
        "unicode": "1f1e6-1f1ff"
    },
    ":flag_ba:": {
        "category": "flags",
        "name": "bosnia and herzegovina",
        "unicode": "1f1e7-1f1e6"
    },
    ":flag_bb:": {
        "category": "flags",
        "name": "barbados",
        "unicode": "1f1e7-1f1e7"
    },
    ":flag_bd:": {
        "category": "flags",
        "name": "bangladesh",
        "unicode": "1f1e7-1f1e9"
    },
    ":flag_be:": {
        "category": "flags",
        "name": "belgium",
        "unicode": "1f1e7-1f1ea"
    },
    ":flag_bf:": {
        "category": "flags",
        "name": "burkina faso",
        "unicode": "1f1e7-1f1eb"
    },
    ":flag_bg:": {
        "category": "flags",
        "name": "bulgaria",
        "unicode": "1f1e7-1f1ec"
    },
    ":flag_bh:": {
        "category": "flags",
        "name": "bahrain",
        "unicode": "1f1e7-1f1ed"
    },
    ":flag_bi:": {
        "category": "flags",
        "name": "burundi",
        "unicode": "1f1e7-1f1ee"
    },
    ":flag_bj:": {
        "category": "flags",
        "name": "benin",
        "unicode": "1f1e7-1f1ef"
    },
    ":flag_bl:": {
        "category": "flags",
        "name": "saint barth\u00e9lemy",
        "unicode": "1f1e7-1f1f1"
    },
    ":flag_black:": {
        "category": "objects",
        "name": "waving black flag",
        "unicode": "1f3f4"
    },
    ":flag_bm:": {
        "category": "flags",
        "name": "bermuda",
        "unicode": "1f1e7-1f1f2"
    },
    ":flag_bn:": {
        "category": "flags",
        "name": "brunei",
        "unicode": "1f1e7-1f1f3"
    },
    ":flag_bo:": {
        "category": "flags",
        "name": "bolivia",
        "unicode": "1f1e7-1f1f4"
    },
    ":flag_bq:": {
        "category": "flags",
        "name": "caribbean netherlands",
        "unicode": "1f1e7-1f1f6"
    },
    ":flag_br:": {
        "category": "flags",
        "name": "brazil",
        "unicode": "1f1e7-1f1f7"
    },
    ":flag_bs:": {
        "category": "flags",
        "name": "the bahamas",
        "unicode": "1f1e7-1f1f8"
    },
    ":flag_bt:": {
        "category": "flags",
        "name": "bhutan",
        "unicode": "1f1e7-1f1f9"
    },
    ":flag_bv:": {
        "category": "flags",
        "name": "bouvet island",
        "unicode": "1f1e7-1f1fb"
    },
    ":flag_bw:": {
        "category": "flags",
        "name": "botswana",
        "unicode": "1f1e7-1f1fc"
    },
    ":flag_by:": {
        "category": "flags",
        "name": "belarus",
        "unicode": "1f1e7-1f1fe"
    },
    ":flag_bz:": {
        "category": "flags",
        "name": "belize",
        "unicode": "1f1e7-1f1ff"
    },
    ":flag_ca:": {
        "category": "flags",
        "name": "canada",
        "unicode": "1f1e8-1f1e6"
    },
    ":flag_cc:": {
        "category": "flags",
        "name": "cocos (keeling) islands",
        "unicode": "1f1e8-1f1e8"
    },
    ":flag_cd:": {
        "category": "flags",
        "name": "the democratic republic of the congo",
        "unicode": "1f1e8-1f1e9"
    },
    ":flag_cf:": {
        "category": "flags",
        "name": "central african republic",
        "unicode": "1f1e8-1f1eb"
    },
    ":flag_cg:": {
        "category": "flags",
        "name": "the republic of the congo",
        "unicode": "1f1e8-1f1ec"
    },
    ":flag_ch:": {
        "category": "flags",
        "name": "switzerland",
        "unicode": "1f1e8-1f1ed"
    },
    ":flag_ci:": {
        "category": "flags",
        "name": "c\u00f4te d\u2019ivoire",
        "unicode": "1f1e8-1f1ee"
    },
    ":flag_ck:": {
        "category": "flags",
        "name": "cook islands",
        "unicode": "1f1e8-1f1f0"
    },
    ":flag_cl:": {
        "category": "flags",
        "name": "chile",
        "unicode": "1f1e8-1f1f1"
    },
    ":flag_cm:": {
        "category": "flags",
        "name": "cameroon",
        "unicode": "1f1e8-1f1f2"
    },
    ":flag_cn:": {
        "category": "flags",
        "name": "china",
        "unicode": "1f1e8-1f1f3"
    },
    ":flag_co:": {
        "category": "flags",
        "name": "colombia",
        "unicode": "1f1e8-1f1f4"
    },
    ":flag_cp:": {
        "category": "flags",
        "name": "clipperton island",
        "unicode": "1f1e8-1f1f5"
    },
    ":flag_cr:": {
        "category": "flags",
        "name": "costa rica",
        "unicode": "1f1e8-1f1f7"
    },
    ":flag_cu:": {
        "category": "flags",
        "name": "cuba",
        "unicode": "1f1e8-1f1fa"
    },
    ":flag_cv:": {
        "category": "flags",
        "name": "cape verde",
        "unicode": "1f1e8-1f1fb"
    },
    ":flag_cw:": {
        "category": "flags",
        "name": "cura\u00e7ao",
        "unicode": "1f1e8-1f1fc"
    },
    ":flag_cx:": {
        "category": "flags",
        "name": "christmas island",
        "unicode": "1f1e8-1f1fd"
    },
    ":flag_cy:": {
        "category": "flags",
        "name": "cyprus",
        "unicode": "1f1e8-1f1fe"
    },
    ":flag_cz:": {
        "category": "flags",
        "name": "the czech republic",
        "unicode": "1f1e8-1f1ff"
    },
    ":flag_de:": {
        "category": "flags",
        "name": "germany",
        "unicode": "1f1e9-1f1ea"
    },
    ":flag_dg:": {
        "category": "flags",
        "name": "diego garcia",
        "unicode": "1f1e9-1f1ec"
    },
    ":flag_dj:": {
        "category": "flags",
        "name": "djibouti",
        "unicode": "1f1e9-1f1ef"
    },
    ":flag_dk:": {
        "category": "flags",
        "name": "denmark",
        "unicode": "1f1e9-1f1f0"
    },
    ":flag_dm:": {
        "category": "flags",
        "name": "dominica",
        "unicode": "1f1e9-1f1f2"
    },
    ":flag_do:": {
        "category": "flags",
        "name": "the dominican republic",
        "unicode": "1f1e9-1f1f4"
    },
    ":flag_dz:": {
        "category": "flags",
        "name": "algeria",
        "unicode": "1f1e9-1f1ff"
    },
    ":flag_ea:": {
        "category": "flags",
        "name": "ceuta, melilla",
        "unicode": "1f1ea-1f1e6"
    },
    ":flag_ec:": {
        "category": "flags",
        "name": "ecuador",
        "unicode": "1f1ea-1f1e8"
    },
    ":flag_ee:": {
        "category": "flags",
        "name": "estonia",
        "unicode": "1f1ea-1f1ea"
    },
    ":flag_eg:": {
        "category": "flags",
        "name": "egypt",
        "unicode": "1f1ea-1f1ec"
    },
    ":flag_eh:": {
        "category": "flags",
        "name": "western sahara",
        "unicode": "1f1ea-1f1ed"
    },
    ":flag_er:": {
        "category": "flags",
        "name": "eritrea",
        "unicode": "1f1ea-1f1f7"
    },
    ":flag_es:": {
        "category": "flags",
        "name": "spain",
        "unicode": "1f1ea-1f1f8"
    },
    ":flag_et:": {
        "category": "flags",
        "name": "ethiopia",
        "unicode": "1f1ea-1f1f9"
    },
    ":flag_eu:": {
        "category": "flags",
        "name": "european union",
        "unicode": "1f1ea-1f1fa"
    },
    ":flag_fi:": {
        "category": "flags",
        "name": "finland",
        "unicode": "1f1eb-1f1ee"
    },
    ":flag_fj:": {
        "category": "flags",
        "name": "fiji",
        "unicode": "1f1eb-1f1ef"
    },
    ":flag_fk:": {
        "category": "flags",
        "name": "falkland islands",
        "unicode": "1f1eb-1f1f0"
    },
    ":flag_fm:": {
        "category": "flags",
        "name": "micronesia",
        "unicode": "1f1eb-1f1f2"
    },
    ":flag_fo:": {
        "category": "flags",
        "name": "faroe islands",
        "unicode": "1f1eb-1f1f4"
    },
    ":flag_fr:": {
        "category": "flags",
        "name": "france",
        "unicode": "1f1eb-1f1f7"
    },
    ":flag_ga:": {
        "category": "flags",
        "name": "gabon",
        "unicode": "1f1ec-1f1e6"
    },
    ":flag_gb:": {
        "category": "flags",
        "name": "great britain",
        "unicode": "1f1ec-1f1e7"
    },
    ":flag_gd:": {
        "category": "flags",
        "name": "grenada",
        "unicode": "1f1ec-1f1e9"
    },
    ":flag_ge:": {
        "category": "flags",
        "name": "georgia",
        "unicode": "1f1ec-1f1ea"
    },
    ":flag_gf:": {
        "category": "flags",
        "name": "french guiana",
        "unicode": "1f1ec-1f1eb"
    },
    ":flag_gg:": {
        "category": "flags",
        "name": "guernsey",
        "unicode": "1f1ec-1f1ec"
    },
    ":flag_gh:": {
        "category": "flags",
        "name": "ghana",
        "unicode": "1f1ec-1f1ed"
    },
    ":flag_gi:": {
        "category": "flags",
        "name": "gibraltar",
        "unicode": "1f1ec-1f1ee"
    },
    ":flag_gl:": {
        "category": "flags",
        "name": "greenland",
        "unicode": "1f1ec-1f1f1"
    },
    ":flag_gm:": {
        "category": "flags",
        "name": "the gambia",
        "unicode": "1f1ec-1f1f2"
    },
    ":flag_gn:": {
        "category": "flags",
        "name": "guinea",
        "unicode": "1f1ec-1f1f3"
    },
    ":flag_gp:": {
        "category": "flags",
        "name": "guadeloupe",
        "unicode": "1f1ec-1f1f5"
    },
    ":flag_gq:": {
        "category": "flags",
        "name": "equatorial guinea",
        "unicode": "1f1ec-1f1f6"
    },
    ":flag_gr:": {
        "category": "flags",
        "name": "greece",
        "unicode": "1f1ec-1f1f7"
    },
    ":flag_gs:": {
        "category": "flags",
        "name": "south georgia",
        "unicode": "1f1ec-1f1f8"
    },
    ":flag_gt:": {
        "category": "flags",
        "name": "guatemala",
        "unicode": "1f1ec-1f1f9"
    },
    ":flag_gu:": {
        "category": "flags",
        "name": "guam",
        "unicode": "1f1ec-1f1fa"
    },
    ":flag_gw:": {
        "category": "flags",
        "name": "guinea-bissau",
        "unicode": "1f1ec-1f1fc"
    },
    ":flag_gy:": {
        "category": "flags",
        "name": "guyana",
        "unicode": "1f1ec-1f1fe"
    },
    ":flag_hk:": {
        "category": "flags",
        "name": "hong kong",
        "unicode": "1f1ed-1f1f0"
    },
    ":flag_hm:": {
        "category": "flags",
        "name": "heard island and mcdonald islands",
        "unicode": "1f1ed-1f1f2"
    },
    ":flag_hn:": {
        "category": "flags",
        "name": "honduras",
        "unicode": "1f1ed-1f1f3"
    },
    ":flag_hr:": {
        "category": "flags",
        "name": "croatia",
        "unicode": "1f1ed-1f1f7"
    },
    ":flag_ht:": {
        "category": "flags",
        "name": "haiti",
        "unicode": "1f1ed-1f1f9"
    },
    ":flag_hu:": {
        "category": "flags",
        "name": "hungary",
        "unicode": "1f1ed-1f1fa"
    },
    ":flag_ic:": {
        "category": "flags",
        "name": "canary islands",
        "unicode": "1f1ee-1f1e8"
    },
    ":flag_id:": {
        "category": "flags",
        "name": "indonesia",
        "unicode": "1f1ee-1f1e9"
    },
    ":flag_ie:": {
        "category": "flags",
        "name": "ireland",
        "unicode": "1f1ee-1f1ea"
    },
    ":flag_il:": {
        "category": "flags",
        "name": "israel",
        "unicode": "1f1ee-1f1f1"
    },
    ":flag_im:": {
        "category": "flags",
        "name": "isle of man",
        "unicode": "1f1ee-1f1f2"
    },
    ":flag_in:": {
        "category": "flags",
        "name": "india",
        "unicode": "1f1ee-1f1f3"
    },
    ":flag_io:": {
        "category": "flags",
        "name": "british indian ocean territory",
        "unicode": "1f1ee-1f1f4"
    },
    ":flag_iq:": {
        "category": "flags",
        "name": "iraq",
        "unicode": "1f1ee-1f1f6"
    },
    ":flag_ir:": {
        "category": "flags",
        "name": "iran",
        "unicode": "1f1ee-1f1f7"
    },
    ":flag_is:": {
        "category": "flags",
        "name": "iceland",
        "unicode": "1f1ee-1f1f8"
    },
    ":flag_it:": {
        "category": "flags",
        "name": "italy",
        "unicode": "1f1ee-1f1f9"
    },
    ":flag_je:": {
        "category": "flags",
        "name": "jersey",
        "unicode": "1f1ef-1f1ea"
    },
    ":flag_jm:": {
        "category": "flags",
        "name": "jamaica",
        "unicode": "1f1ef-1f1f2"
    },
    ":flag_jo:": {
        "category": "flags",
        "name": "jordan",
        "unicode": "1f1ef-1f1f4"
    },
    ":flag_jp:": {
        "category": "flags",
        "name": "japan",
        "unicode": "1f1ef-1f1f5"
    },
    ":flag_ke:": {
        "category": "flags",
        "name": "kenya",
        "unicode": "1f1f0-1f1ea"
    },
    ":flag_kg:": {
        "category": "flags",
        "name": "kyrgyzstan",
        "unicode": "1f1f0-1f1ec"
    },
    ":flag_kh:": {
        "category": "flags",
        "name": "cambodia",
        "unicode": "1f1f0-1f1ed"
    },
    ":flag_ki:": {
        "category": "flags",
        "name": "kiribati",
        "unicode": "1f1f0-1f1ee"
    },
    ":flag_km:": {
        "category": "flags",
        "name": "the comoros",
        "unicode": "1f1f0-1f1f2"
    },
    ":flag_kn:": {
        "category": "flags",
        "name": "saint kitts and nevis",
        "unicode": "1f1f0-1f1f3"
    },
    ":flag_kp:": {
        "category": "flags",
        "name": "north korea",
        "unicode": "1f1f0-1f1f5"
    },
    ":flag_kr:": {
        "category": "flags",
        "name": "korea",
        "unicode": "1f1f0-1f1f7"
    },
    ":flag_kw:": {
        "category": "flags",
        "name": "kuwait",
        "unicode": "1f1f0-1f1fc"
    },
    ":flag_ky:": {
        "category": "flags",
        "name": "cayman islands",
        "unicode": "1f1f0-1f1fe"
    },
    ":flag_kz:": {
        "category": "flags",
        "name": "kazakhstan",
        "unicode": "1f1f0-1f1ff"
    },
    ":flag_la:": {
        "category": "flags",
        "name": "laos",
        "unicode": "1f1f1-1f1e6"
    },
    ":flag_lb:": {
        "category": "flags",
        "name": "lebanon",
        "unicode": "1f1f1-1f1e7"
    },
    ":flag_lc:": {
        "category": "flags",
        "name": "saint lucia",
        "unicode": "1f1f1-1f1e8"
    },
    ":flag_li:": {
        "category": "flags",
        "name": "liechtenstein",
        "unicode": "1f1f1-1f1ee"
    },
    ":flag_lk:": {
        "category": "flags",
        "name": "sri lanka",
        "unicode": "1f1f1-1f1f0"
    },
    ":flag_lr:": {
        "category": "flags",
        "name": "liberia",
        "unicode": "1f1f1-1f1f7"
    },
    ":flag_ls:": {
        "category": "flags",
        "name": "lesotho",
        "unicode": "1f1f1-1f1f8"
    },
    ":flag_lt:": {
        "category": "flags",
        "name": "lithuania",
        "unicode": "1f1f1-1f1f9"
    },
    ":flag_lu:": {
        "category": "flags",
        "name": "luxembourg",
        "unicode": "1f1f1-1f1fa"
    },
    ":flag_lv:": {
        "category": "flags",
        "name": "latvia",
        "unicode": "1f1f1-1f1fb"
    },
    ":flag_ly:": {
        "category": "flags",
        "name": "libya",
        "unicode": "1f1f1-1f1fe"
    },
    ":flag_ma:": {
        "category": "flags",
        "name": "morocco",
        "unicode": "1f1f2-1f1e6"
    },
    ":flag_mc:": {
        "category": "flags",
        "name": "monaco",
        "unicode": "1f1f2-1f1e8"
    },
    ":flag_md:": {
        "category": "flags",
        "name": "moldova",
        "unicode": "1f1f2-1f1e9"
    },
    ":flag_me:": {
        "category": "flags",
        "name": "montenegro",
        "unicode": "1f1f2-1f1ea"
    },
    ":flag_mf:": {
        "category": "flags",
        "name": "saint martin",
        "unicode": "1f1f2-1f1eb"
    },
    ":flag_mg:": {
        "category": "flags",
        "name": "madagascar",
        "unicode": "1f1f2-1f1ec"
    },
    ":flag_mh:": {
        "category": "flags",
        "name": "the marshall islands",
        "unicode": "1f1f2-1f1ed"
    },
    ":flag_mk:": {
        "category": "flags",
        "name": "macedonia",
        "unicode": "1f1f2-1f1f0"
    },
    ":flag_ml:": {
        "category": "flags",
        "name": "mali",
        "unicode": "1f1f2-1f1f1"
    },
    ":flag_mm:": {
        "category": "flags",
        "name": "myanmar",
        "unicode": "1f1f2-1f1f2"
    },
    ":flag_mn:": {
        "category": "flags",
        "name": "mongolia",
        "unicode": "1f1f2-1f1f3"
    },
    ":flag_mo:": {
        "category": "flags",
        "name": "macau",
        "unicode": "1f1f2-1f1f4"
    },
    ":flag_mp:": {
        "category": "flags",
        "name": "northern mariana islands",
        "unicode": "1f1f2-1f1f5"
    },
    ":flag_mq:": {
        "category": "flags",
        "name": "martinique",
        "unicode": "1f1f2-1f1f6"
    },
    ":flag_mr:": {
        "category": "flags",
        "name": "mauritania",
        "unicode": "1f1f2-1f1f7"
    },
    ":flag_ms:": {
        "category": "flags",
        "name": "montserrat",
        "unicode": "1f1f2-1f1f8"
    },
    ":flag_mt:": {
        "category": "flags",
        "name": "malta",
        "unicode": "1f1f2-1f1f9"
    },
    ":flag_mu:": {
        "category": "flags",
        "name": "mauritius",
        "unicode": "1f1f2-1f1fa"
    },
    ":flag_mv:": {
        "category": "flags",
        "name": "maldives",
        "unicode": "1f1f2-1f1fb"
    },
    ":flag_mw:": {
        "category": "flags",
        "name": "malawi",
        "unicode": "1f1f2-1f1fc"
    },
    ":flag_mx:": {
        "category": "flags",
        "name": "mexico",
        "unicode": "1f1f2-1f1fd"
    },
    ":flag_my:": {
        "category": "flags",
        "name": "malaysia",
        "unicode": "1f1f2-1f1fe"
    },
    ":flag_mz:": {
        "category": "flags",
        "name": "mozambique",
        "unicode": "1f1f2-1f1ff"
    },
    ":flag_na:": {
        "category": "flags",
        "name": "namibia",
        "unicode": "1f1f3-1f1e6"
    },
    ":flag_nc:": {
        "category": "flags",
        "name": "new caledonia",
        "unicode": "1f1f3-1f1e8"
    },
    ":flag_ne:": {
        "category": "flags",
        "name": "niger",
        "unicode": "1f1f3-1f1ea"
    },
    ":flag_nf:": {
        "category": "flags",
        "name": "norfolk island",
        "unicode": "1f1f3-1f1eb"
    },
    ":flag_ng:": {
        "category": "flags",
        "name": "nigeria",
        "unicode": "1f1f3-1f1ec"
    },
    ":flag_ni:": {
        "category": "flags",
        "name": "nicaragua",
        "unicode": "1f1f3-1f1ee"
    },
    ":flag_nl:": {
        "category": "flags",
        "name": "the netherlands",
        "unicode": "1f1f3-1f1f1"
    },
    ":flag_no:": {
        "category": "flags",
        "name": "norway",
        "unicode": "1f1f3-1f1f4"
    },
    ":flag_np:": {
        "category": "flags",
        "name": "nepal",
        "unicode": "1f1f3-1f1f5"
    },
    ":flag_nr:": {
        "category": "flags",
        "name": "nauru",
        "unicode": "1f1f3-1f1f7"
    },
    ":flag_nu:": {
        "category": "flags",
        "name": "niue",
        "unicode": "1f1f3-1f1fa"
    },
    ":flag_nz:": {
        "category": "flags",
        "name": "new zealand",
        "unicode": "1f1f3-1f1ff"
    },
    ":flag_om:": {
        "category": "flags",
        "name": "oman",
        "unicode": "1f1f4-1f1f2"
    },
    ":flag_pa:": {
        "category": "flags",
        "name": "panama",
        "unicode": "1f1f5-1f1e6"
    },
    ":flag_pe:": {
        "category": "flags",
        "name": "peru",
        "unicode": "1f1f5-1f1ea"
    },
    ":flag_pf:": {
        "category": "flags",
        "name": "french polynesia",
        "unicode": "1f1f5-1f1eb"
    },
    ":flag_pg:": {
        "category": "flags",
        "name": "papua new guinea",
        "unicode": "1f1f5-1f1ec"
    },
    ":flag_ph:": {
        "category": "flags",
        "name": "the philippines",
        "unicode": "1f1f5-1f1ed"
    },
    ":flag_pk:": {
        "category": "flags",
        "name": "pakistan",
        "unicode": "1f1f5-1f1f0"
    },
    ":flag_pl:": {
        "category": "flags",
        "name": "poland",
        "unicode": "1f1f5-1f1f1"
    },
    ":flag_pm:": {
        "category": "flags",
        "name": "saint pierre and miquelon",
        "unicode": "1f1f5-1f1f2"
    },
    ":flag_pn:": {
        "category": "flags",
        "name": "pitcairn",
        "unicode": "1f1f5-1f1f3"
    },
    ":flag_pr:": {
        "category": "flags",
        "name": "puerto rico",
        "unicode": "1f1f5-1f1f7"
    },
    ":flag_ps:": {
        "category": "flags",
        "name": "palestinian authority",
        "unicode": "1f1f5-1f1f8"
    },
    ":flag_pt:": {
        "category": "flags",
        "name": "portugal",
        "unicode": "1f1f5-1f1f9"
    },
    ":flag_pw:": {
        "category": "flags",
        "name": "palau",
        "unicode": "1f1f5-1f1fc"
    },
    ":flag_py:": {
        "category": "flags",
        "name": "paraguay",
        "unicode": "1f1f5-1f1fe"
    },
    ":flag_qa:": {
        "category": "flags",
        "name": "qatar",
        "unicode": "1f1f6-1f1e6"
    },
    ":flag_re:": {
        "category": "flags",
        "name": "r\u00e9union",
        "unicode": "1f1f7-1f1ea"
    },
    ":flag_ro:": {
        "category": "flags",
        "name": "romania",
        "unicode": "1f1f7-1f1f4"
    },
    ":flag_rs:": {
        "category": "flags",
        "name": "serbia",
        "unicode": "1f1f7-1f1f8"
    },
    ":flag_ru:": {
        "category": "flags",
        "name": "russia",
        "unicode": "1f1f7-1f1fa"
    },
    ":flag_rw:": {
        "category": "flags",
        "name": "rwanda",
        "unicode": "1f1f7-1f1fc"
    },
    ":flag_sa:": {
        "category": "flags",
        "name": "saudi arabia",
        "unicode": "1f1f8-1f1e6"
    },
    ":flag_sb:": {
        "category": "flags",
        "name": "the solomon islands",
        "unicode": "1f1f8-1f1e7"
    },
    ":flag_sc:": {
        "category": "flags",
        "name": "the seychelles",
        "unicode": "1f1f8-1f1e8"
    },
    ":flag_sd:": {
        "category": "flags",
        "name": "sudan",
        "unicode": "1f1f8-1f1e9"
    },
    ":flag_se:": {
        "category": "flags",
        "name": "sweden",
        "unicode": "1f1f8-1f1ea"
    },
    ":flag_sg:": {
        "category": "flags",
        "name": "singapore",
        "unicode": "1f1f8-1f1ec"
    },
    ":flag_sh:": {
        "category": "flags",
        "name": "saint helena",
        "unicode": "1f1f8-1f1ed"
    },
    ":flag_si:": {
        "category": "flags",
        "name": "slovenia",
        "unicode": "1f1f8-1f1ee"
    },
    ":flag_sj:": {
        "category": "flags",
        "name": "svalbard and jan mayen",
        "unicode": "1f1f8-1f1ef"
    },
    ":flag_sk:": {
        "category": "flags",
        "name": "slovakia",
        "unicode": "1f1f8-1f1f0"
    },
    ":flag_sl:": {
        "category": "flags",
        "name": "sierra leone",
        "unicode": "1f1f8-1f1f1"
    },
    ":flag_sm:": {
        "category": "flags",
        "name": "san marino",
        "unicode": "1f1f8-1f1f2"
    },
    ":flag_sn:": {
        "category": "flags",
        "name": "senegal",
        "unicode": "1f1f8-1f1f3"
    },
    ":flag_so:": {
        "category": "flags",
        "name": "somalia",
        "unicode": "1f1f8-1f1f4"
    },
    ":flag_sr:": {
        "category": "flags",
        "name": "suriname",
        "unicode": "1f1f8-1f1f7"
    },
    ":flag_ss:": {
        "category": "flags",
        "name": "south sudan",
        "unicode": "1f1f8-1f1f8"
    },
    ":flag_st:": {
        "category": "flags",
        "name": "s\u00e3o tom\u00e9 and pr\u00edncipe",
        "unicode": "1f1f8-1f1f9"
    },
    ":flag_sv:": {
        "category": "flags",
        "name": "el salvador",
        "unicode": "1f1f8-1f1fb"
    },
    ":flag_sx:": {
        "category": "flags",
        "name": "sint maarten",
        "unicode": "1f1f8-1f1fd"
    },
    ":flag_sy:": {
        "category": "flags",
        "name": "syria",
        "unicode": "1f1f8-1f1fe"
    },
    ":flag_sz:": {
        "category": "flags",
        "name": "swaziland",
        "unicode": "1f1f8-1f1ff"
    },
    ":flag_ta:": {
        "category": "flags",
        "name": "tristan da cunha",
        "unicode": "1f1f9-1f1e6"
    },
    ":flag_tc:": {
        "category": "flags",
        "name": "turks and caicos islands",
        "unicode": "1f1f9-1f1e8"
    },
    ":flag_td:": {
        "category": "flags",
        "name": "chad",
        "unicode": "1f1f9-1f1e9"
    },
    ":flag_tf:": {
        "category": "flags",
        "name": "french southern territories",
        "unicode": "1f1f9-1f1eb"
    },
    ":flag_tg:": {
        "category": "flags",
        "name": "togo",
        "unicode": "1f1f9-1f1ec"
    },
    ":flag_th:": {
        "category": "flags",
        "name": "thailand",
        "unicode": "1f1f9-1f1ed"
    },
    ":flag_tj:": {
        "category": "flags",
        "name": "tajikistan",
        "unicode": "1f1f9-1f1ef"
    },
    ":flag_tk:": {
        "category": "flags",
        "name": "tokelau",
        "unicode": "1f1f9-1f1f0"
    },
    ":flag_tl:": {
        "category": "flags",
        "name": "timor-leste",
        "unicode": "1f1f9-1f1f1"
    },
    ":flag_tm:": {
        "category": "flags",
        "name": "turkmenistan",
        "unicode": "1f1f9-1f1f2"
    },
    ":flag_tn:": {
        "category": "flags",
        "name": "tunisia",
        "unicode": "1f1f9-1f1f3"
    },
    ":flag_to:": {
        "category": "flags",
        "name": "tonga",
        "unicode": "1f1f9-1f1f4"
    },
    ":flag_tr:": {
        "category": "flags",
        "name": "turkey",
        "unicode": "1f1f9-1f1f7"
    },
    ":flag_tt:": {
        "category": "flags",
        "name": "trinidad and tobago",
        "unicode": "1f1f9-1f1f9"
    },
    ":flag_tv:": {
        "category": "flags",
        "name": "tuvalu",
        "unicode": "1f1f9-1f1fb"
    },
    ":flag_tw:": {
        "category": "flags",
        "name": "the republic of china",
        "unicode": "1f1f9-1f1fc"
    },
    ":flag_tz:": {
        "category": "flags",
        "name": "tanzania",
        "unicode": "1f1f9-1f1ff"
    },
    ":flag_ua:": {
        "category": "flags",
        "name": "ukraine",
        "unicode": "1f1fa-1f1e6"
    },
    ":flag_ug:": {
        "category": "flags",
        "name": "uganda",
        "unicode": "1f1fa-1f1ec"
    },
    ":flag_um:": {
        "category": "flags",
        "name": "united states minor outlying islands",
        "unicode": "1f1fa-1f1f2"
    },
    ":flag_us:": {
        "category": "flags",
        "name": "united states",
        "unicode": "1f1fa-1f1f8"
    },
    ":flag_uy:": {
        "category": "flags",
        "name": "uruguay",
        "unicode": "1f1fa-1f1fe"
    },
    ":flag_uz:": {
        "category": "flags",
        "name": "uzbekistan",
        "unicode": "1f1fa-1f1ff"
    },
    ":flag_va:": {
        "category": "flags",
        "name": "the vatican city",
        "unicode": "1f1fb-1f1e6"
    },
    ":flag_vc:": {
        "category": "flags",
        "name": "saint vincent and the grenadines",
        "unicode": "1f1fb-1f1e8"
    },
    ":flag_ve:": {
        "category": "flags",
        "name": "venezuela",
        "unicode": "1f1fb-1f1ea"
    },
    ":flag_vg:": {
        "category": "flags",
        "name": "british virgin islands",
        "unicode": "1f1fb-1f1ec"
    },
    ":flag_vi:": {
        "category": "flags",
        "name": "u.s. virgin islands",
        "unicode": "1f1fb-1f1ee"
    },
    ":flag_vn:": {
        "category": "flags",
        "name": "vietnam",
        "unicode": "1f1fb-1f1f3"
    },
    ":flag_vu:": {
        "category": "flags",
        "name": "vanuatu",
        "unicode": "1f1fb-1f1fa"
    },
    ":flag_wf:": {
        "category": "flags",
        "name": "wallis and futuna",
        "unicode": "1f1fc-1f1eb"
    },
    ":flag_white:": {
        "category": "objects",
        "name": "waving white flag",
        "unicode": "1f3f3",
        "unicode_alt": "1f3f3-fe0f"
    },
    ":flag_ws:": {
        "category": "flags",
        "name": "samoa",
        "unicode": "1f1fc-1f1f8"
    },
    ":flag_xk:": {
        "category": "flags",
        "name": "kosovo",
        "unicode": "1f1fd-1f1f0"
    },
    ":flag_ye:": {
        "category": "flags",
        "name": "yemen",
        "unicode": "1f1fe-1f1ea"
    },
    ":flag_yt:": {
        "category": "flags",
        "name": "mayotte",
        "unicode": "1f1fe-1f1f9"
    },
    ":flag_za:": {
        "category": "flags",
        "name": "south africa",
        "unicode": "1f1ff-1f1e6"
    },
    ":flag_zm:": {
        "category": "flags",
        "name": "zambia",
        "unicode": "1f1ff-1f1f2"
    },
    ":flag_zw:": {
        "category": "flags",
        "name": "zimbabwe",
        "unicode": "1f1ff-1f1fc"
    },
    ":flags:": {
        "category": "objects",
        "name": "carp streamer",
        "unicode": "1f38f"
    },
    ":flashlight:": {
        "category": "objects",
        "name": "electric torch",
        "unicode": "1f526"
    },
    ":fleur-de-lis:": {
        "category": "symbols",
        "name": "fleur-de-lis",
        "unicode": "269c",
        "unicode_alt": "269c-fe0f"
    },
    ":floppy_disk:": {
        "category": "objects",
        "name": "floppy disk",
        "unicode": "1f4be"
    },
    ":flower_playing_cards:": {
        "category": "symbols",
        "name": "flower playing cards",
        "unicode": "1f3b4"
    },
    ":flushed:": {
        "category": "people",
        "name": "flushed face",
        "unicode": "1f633"
    },
    ":fog:": {
        "category": "nature",
        "name": "fog",
        "unicode": "1f32b",
        "unicode_alt": "1f32b-fe0f"
    },
    ":foggy:": {
        "category": "travel",
        "name": "foggy",
        "unicode": "1f301"
    },
    ":football:": {
        "category": "activity",
        "name": "american football",
        "unicode": "1f3c8"
    },
    ":footprints:": {
        "category": "people",
        "name": "footprints",
        "unicode": "1f463"
    },
    ":fork_and_knife:": {
        "category": "food",
        "name": "fork and knife",
        "unicode": "1f374"
    },
    ":fork_knife_plate:": {
        "category": "food",
        "name": "fork and knife with plate",
        "unicode": "1f37d",
        "unicode_alt": "1f37d-fe0f"
    },
    ":fountain:": {
        "category": "travel",
        "name": "fountain",
        "unicode": "26f2",
        "unicode_alt": "26f2-fe0f"
    },
    ":four:": {
        "category": "symbols",
        "name": "keycap digit four",
        "unicode": "0034-20e3",
        "unicode_alt": "0034-fe0f-20e3"
    },
    ":four_leaf_clover:": {
        "category": "nature",
        "name": "four leaf clover",
        "unicode": "1f340"
    },
    ":fox:": {
        "category": "nature",
        "name": "fox face",
        "unicode": "1f98a"
    },
    ":frame_photo:": {
        "category": "objects",
        "name": "frame with picture",
        "unicode": "1f5bc",
        "unicode_alt": "1f5bc-fe0f"
    },
    ":free:": {
        "category": "symbols",
        "name": "squared free",
        "unicode": "1f193"
    },
    ":french_bread:": {
        "category": "food",
        "name": "baguette bread",
        "unicode": "1f956"
    },
    ":fried_shrimp:": {
        "category": "food",
        "name": "fried shrimp",
        "unicode": "1f364"
    },
    ":fries:": {
        "category": "food",
        "name": "french fries",
        "unicode": "1f35f"
    },
    ":frog:": {
        "category": "nature",
        "name": "frog face",
        "unicode": "1f438"
    },
    ":frowning2:": {
        "category": "people",
        "name": "white frowning face",
        "unicode": "2639",
        "unicode_alt": "2639-fe0f"
    },
    ":frowning:": {
        "category": "people",
        "name": "frowning face with open mouth",
        "unicode": "1f626"
    },
    ":fuelpump:": {
        "category": "travel",
        "name": "fuel pump",
        "unicode": "26fd",
        "unicode_alt": "26fd-fe0f"
    },
    ":full_moon:": {
        "category": "nature",
        "name": "full moon symbol",
        "unicode": "1f315"
    },
    ":full_moon_with_face:": {
        "category": "nature",
        "name": "full moon with face",
        "unicode": "1f31d"
    },
    ":game_die:": {
        "category": "activity",
        "name": "game die",
        "unicode": "1f3b2"
    },
    ":gear:": {
        "category": "objects",
        "name": "gear",
        "unicode": "2699",
        "unicode_alt": "2699-fe0f"
    },
    ":gem:": {
        "category": "objects",
        "name": "gem stone",
        "unicode": "1f48e"
    },
    ":gemini:": {
        "category": "symbols",
        "name": "gemini",
        "unicode": "264a",
        "unicode_alt": "264a-fe0f"
    },
    ":ghost:": {
        "category": "people",
        "name": "ghost",
        "unicode": "1f47b"
    },
    ":gift:": {
        "category": "objects",
        "name": "wrapped present",
        "unicode": "1f381"
    },
    ":gift_heart:": {
        "category": "symbols",
        "name": "heart with ribbon",
        "unicode": "1f49d"
    },
    ":girl:": {
        "category": "people",
        "name": "girl",
        "unicode": "1f467"
    },
    ":girl_tone1:": {
        "category": "people",
        "name": "girl tone 1",
        "unicode": "1f467-1f3fb"
    },
    ":girl_tone2:": {
        "category": "people",
        "name": "girl tone 2",
        "unicode": "1f467-1f3fc"
    },
    ":girl_tone3:": {
        "category": "people",
        "name": "girl tone 3",
        "unicode": "1f467-1f3fd"
    },
    ":girl_tone4:": {
        "category": "people",
        "name": "girl tone 4",
        "unicode": "1f467-1f3fe"
    },
    ":girl_tone5:": {
        "category": "people",
        "name": "girl tone 5",
        "unicode": "1f467-1f3ff"
    },
    ":globe_with_meridians:": {
        "category": "symbols",
        "name": "globe with meridians",
        "unicode": "1f310"
    },
    ":goal:": {
        "category": "activity",
        "name": "goal net",
        "unicode": "1f945"
    },
    ":goat:": {
        "category": "nature",
        "name": "goat",
        "unicode": "1f410"
    },
    ":golf:": {
        "category": "activity",
        "name": "flag in hole",
        "unicode": "26f3",
        "unicode_alt": "26f3-fe0f"
    },
    ":golfer:": {
        "category": "activity",
        "name": "golfer",
        "unicode": "1f3cc",
        "unicode_alt": "1f3cc-fe0f"
    },
    ":gorilla:": {
        "category": "nature",
        "name": "gorilla",
        "unicode": "1f98d"
    },
    ":grapes:": {
        "category": "food",
        "name": "grapes",
        "unicode": "1f347"
    },
    ":green_apple:": {
        "category": "food",
        "name": "green apple",
        "unicode": "1f34f"
    },
    ":green_book:": {
        "category": "objects",
        "name": "green book",
        "unicode": "1f4d7"
    },
    ":green_heart:": {
        "category": "symbols",
        "name": "green heart",
        "unicode": "1f49a"
    },
    ":grey_exclamation:": {
        "category": "symbols",
        "name": "white exclamation mark ornament",
        "unicode": "2755"
    },
    ":grey_question:": {
        "category": "symbols",
        "name": "white question mark ornament",
        "unicode": "2754"
    },
    ":grimacing:": {
        "category": "people",
        "name": "grimacing face",
        "unicode": "1f62c"
    },
    ":grin:": {
        "category": "people",
        "name": "grinning face with smiling eyes",
        "unicode": "1f601"
    },
    ":grinning:": {
        "category": "people",
        "name": "grinning face",
        "unicode": "1f600"
    },
    ":guardsman:": {
        "category": "people",
        "name": "guardsman",
        "unicode": "1f482"
    },
    ":guardsman_tone1:": {
        "category": "people",
        "name": "guardsman tone 1",
        "unicode": "1f482-1f3fb"
    },
    ":guardsman_tone2:": {
        "category": "people",
        "name": "guardsman tone 2",
        "unicode": "1f482-1f3fc"
    },
    ":guardsman_tone3:": {
        "category": "people",
        "name": "guardsman tone 3",
        "unicode": "1f482-1f3fd"
    },
    ":guardsman_tone4:": {
        "category": "people",
        "name": "guardsman tone 4",
        "unicode": "1f482-1f3fe"
    },
    ":guardsman_tone5:": {
        "category": "people",
        "name": "guardsman tone 5",
        "unicode": "1f482-1f3ff"
    },
    ":guitar:": {
        "category": "activity",
        "name": "guitar",
        "unicode": "1f3b8"
    },
    ":gun:": {
        "category": "objects",
        "name": "pistol",
        "unicode": "1f52b"
    },
    ":haircut:": {
        "category": "people",
        "name": "haircut",
        "unicode": "1f487"
    },
    ":haircut_tone1:": {
        "category": "people",
        "name": "haircut tone 1",
        "unicode": "1f487-1f3fb"
    },
    ":haircut_tone2:": {
        "category": "people",
        "name": "haircut tone 2",
        "unicode": "1f487-1f3fc"
    },
    ":haircut_tone3:": {
        "category": "people",
        "name": "haircut tone 3",
        "unicode": "1f487-1f3fd"
    },
    ":haircut_tone4:": {
        "category": "people",
        "name": "haircut tone 4",
        "unicode": "1f487-1f3fe"
    },
    ":haircut_tone5:": {
        "category": "people",
        "name": "haircut tone 5",
        "unicode": "1f487-1f3ff"
    },
    ":hamburger:": {
        "category": "food",
        "name": "hamburger",
        "unicode": "1f354"
    },
    ":hammer:": {
        "category": "objects",
        "name": "hammer",
        "unicode": "1f528"
    },
    ":hammer_pick:": {
        "category": "objects",
        "name": "hammer and pick",
        "unicode": "2692",
        "unicode_alt": "2692-fe0f"
    },
    ":hamster:": {
        "category": "nature",
        "name": "hamster face",
        "unicode": "1f439"
    },
    ":hand_splayed:": {
        "category": "people",
        "name": "raised hand with fingers splayed",
        "unicode": "1f590",
        "unicode_alt": "1f590-fe0f"
    },
    ":hand_splayed_tone1:": {
        "category": "people",
        "name": "raised hand with fingers splayed tone 1",
        "unicode": "1f590-1f3fb"
    },
    ":hand_splayed_tone2:": {
        "category": "people",
        "name": "raised hand with fingers splayed tone 2",
        "unicode": "1f590-1f3fc"
    },
    ":hand_splayed_tone3:": {
        "category": "people",
        "name": "raised hand with fingers splayed tone 3",
        "unicode": "1f590-1f3fd"
    },
    ":hand_splayed_tone4:": {
        "category": "people",
        "name": "raised hand with fingers splayed tone 4",
        "unicode": "1f590-1f3fe"
    },
    ":hand_splayed_tone5:": {
        "category": "people",
        "name": "raised hand with fingers splayed tone 5",
        "unicode": "1f590-1f3ff"
    },
    ":handbag:": {
        "category": "people",
        "name": "handbag",
        "unicode": "1f45c"
    },
    ":handball:": {
        "category": "activity",
        "name": "handball",
        "unicode": "1f93e"
    },
    ":handball_tone1:": {
        "category": "activity",
        "name": "handball tone 1",
        "unicode": "1f93e-1f3fb"
    },
    ":handball_tone2:": {
        "category": "activity",
        "name": "handball tone 2",
        "unicode": "1f93e-1f3fc"
    },
    ":handball_tone3:": {
        "category": "activity",
        "name": "handball tone 3",
        "unicode": "1f93e-1f3fd"
    },
    ":handball_tone4:": {
        "category": "activity",
        "name": "handball tone 4",
        "unicode": "1f93e-1f3fe"
    },
    ":handball_tone5:": {
        "category": "activity",
        "name": "handball tone 5",
        "unicode": "1f93e-1f3ff"
    },
    ":handshake:": {
        "category": "people",
        "name": "handshake",
        "unicode": "1f91d"
    },
    ":handshake_tone1:": {
        "category": "people",
        "name": "handshake tone 1",
        "unicode": "1f91d-1f3fb"
    },
    ":handshake_tone2:": {
        "category": "people",
        "name": "handshake tone 2",
        "unicode": "1f91d-1f3fc"
    },
    ":handshake_tone3:": {
        "category": "people",
        "name": "handshake tone 3",
        "unicode": "1f91d-1f3fd"
    },
    ":handshake_tone4:": {
        "category": "people",
        "name": "handshake tone 4",
        "unicode": "1f91d-1f3fe"
    },
    ":handshake_tone5:": {
        "category": "people",
        "name": "handshake tone 5",
        "unicode": "1f91d-1f3ff"
    },
    ":hash:": {
        "category": "symbols",
        "name": "keycap number sign",
        "unicode": "0023-20e3",
        "unicode_alt": "0023-fe0f-20e3"
    },
    ":hatched_chick:": {
        "category": "nature",
        "name": "front-facing baby chick",
        "unicode": "1f425"
    },
    ":hatching_chick:": {
        "category": "nature",
        "name": "hatching chick",
        "unicode": "1f423"
    },
    ":head_bandage:": {
        "category": "people",
        "name": "face with head-bandage",
        "unicode": "1f915"
    },
    ":headphones:": {
        "category": "activity",
        "name": "headphone",
        "unicode": "1f3a7"
    },
    ":hear_no_evil:": {
        "category": "nature",
        "name": "hear-no-evil monkey",
        "unicode": "1f649"
    },
    ":heart:": {
        "category": "symbols",
        "name": "heavy black heart",
        "unicode": "2764",
        "unicode_alt": "2764-fe0f"
    },
    ":heart_decoration:": {
        "category": "symbols",
        "name": "heart decoration",
        "unicode": "1f49f"
    },
    ":heart_exclamation:": {
        "category": "symbols",
        "name": "heavy heart exclamation mark ornament",
        "unicode": "2763",
        "unicode_alt": "2763-fe0f"
    },
    ":heart_eyes:": {
        "category": "people",
        "name": "smiling face with heart-shaped eyes",
        "unicode": "1f60d"
    },
    ":heart_eyes_cat:": {
        "category": "people",
        "name": "smiling cat face with heart-shaped eyes",
        "unicode": "1f63b"
    },
    ":heartbeat:": {
        "category": "symbols",
        "name": "beating heart",
        "unicode": "1f493"
    },
    ":heartpulse:": {
        "category": "symbols",
        "name": "growing heart",
        "unicode": "1f497"
    },
    ":hearts:": {
        "category": "symbols",
        "name": "black heart suit",
        "unicode": "2665",
        "unicode_alt": "2665-fe0f"
    },
    ":heavy_check_mark:": {
        "category": "symbols",
        "name": "heavy check mark",
        "unicode": "2714",
        "unicode_alt": "2714-fe0f"
    },
    ":heavy_division_sign:": {
        "category": "symbols",
        "name": "heavy division sign",
        "unicode": "2797"
    },
    ":heavy_dollar_sign:": {
        "category": "symbols",
        "name": "heavy dollar sign",
        "unicode": "1f4b2"
    },
    ":heavy_minus_sign:": {
        "category": "symbols",
        "name": "heavy minus sign",
        "unicode": "2796"
    },
    ":heavy_multiplication_x:": {
        "category": "symbols",
        "name": "heavy multiplication x",
        "unicode": "2716",
        "unicode_alt": "2716-fe0f"
    },
    ":heavy_plus_sign:": {
        "category": "symbols",
        "name": "heavy plus sign",
        "unicode": "2795"
    },
    ":helicopter:": {
        "category": "travel",
        "name": "helicopter",
        "unicode": "1f681"
    },
    ":helmet_with_cross:": {
        "category": "people",
        "name": "helmet with white cross",
        "unicode": "26d1",
        "unicode_alt": "26d1-fe0f"
    },
    ":herb:": {
        "category": "nature",
        "name": "herb",
        "unicode": "1f33f"
    },
    ":hibiscus:": {
        "category": "nature",
        "name": "hibiscus",
        "unicode": "1f33a"
    },
    ":high_brightness:": {
        "category": "symbols",
        "name": "high brightness symbol",
        "unicode": "1f506"
    },
    ":high_heel:": {
        "category": "people",
        "name": "high-heeled shoe",
        "unicode": "1f460"
    },
    ":hockey:": {
        "category": "activity",
        "name": "ice hockey stick and puck",
        "unicode": "1f3d2"
    },
    ":hole:": {
        "category": "objects",
        "name": "hole",
        "unicode": "1f573",
        "unicode_alt": "1f573-fe0f"
    },
    ":homes:": {
        "category": "travel",
        "name": "house buildings",
        "unicode": "1f3d8",
        "unicode_alt": "1f3d8-fe0f"
    },
    ":honey_pot:": {
        "category": "food",
        "name": "honey pot",
        "unicode": "1f36f"
    },
    ":horse:": {
        "category": "nature",
        "name": "horse face",
        "unicode": "1f434"
    },
    ":horse_racing:": {
        "category": "activity",
        "name": "horse racing",
        "unicode": "1f3c7"
    },
    ":horse_racing_tone1:": {
        "category": "activity",
        "name": "horse racing tone 1",
        "unicode": "1f3c7-1f3fb"
    },
    ":horse_racing_tone2:": {
        "category": "activity",
        "name": "horse racing tone 2",
        "unicode": "1f3c7-1f3fc"
    },
    ":horse_racing_tone3:": {
        "category": "activity",
        "name": "horse racing tone 3",
        "unicode": "1f3c7-1f3fd"
    },
    ":horse_racing_tone4:": {
        "category": "activity",
        "name": "horse racing tone 4",
        "unicode": "1f3c7-1f3fe"
    },
    ":horse_racing_tone5:": {
        "category": "activity",
        "name": "horse racing tone 5",
        "unicode": "1f3c7-1f3ff"
    },
    ":hospital:": {
        "category": "travel",
        "name": "hospital",
        "unicode": "1f3e5"
    },
    ":hot_pepper:": {
        "category": "food",
        "name": "hot pepper",
        "unicode": "1f336",
        "unicode_alt": "1f336-fe0f"
    },
    ":hotdog:": {
        "category": "food",
        "name": "hot dog",
        "unicode": "1f32d"
    },
    ":hotel:": {
        "category": "travel",
        "name": "hotel",
        "unicode": "1f3e8"
    },
    ":hotsprings:": {
        "category": "symbols",
        "name": "hot springs",
        "unicode": "2668",
        "unicode_alt": "2668-fe0f"
    },
    ":hourglass:": {
        "category": "objects",
        "name": "hourglass",
        "unicode": "231b",
        "unicode_alt": "231b-fe0f"
    },
    ":hourglass_flowing_sand:": {
        "category": "objects",
        "name": "hourglass with flowing sand",
        "unicode": "23f3"
    },
    ":house:": {
        "category": "travel",
        "name": "house building",
        "unicode": "1f3e0"
    },
    ":house_abandoned:": {
        "category": "travel",
        "name": "derelict house building",
        "unicode": "1f3da",
        "unicode_alt": "1f3da-fe0f"
    },
    ":house_with_garden:": {
        "category": "travel",
        "name": "house with garden",
        "unicode": "1f3e1"
    },
    ":hugging:": {
        "category": "people",
        "name": "hugging face",
        "unicode": "1f917"
    },
    ":hushed:": {
        "category": "people",
        "name": "hushed face",
        "unicode": "1f62f"
    },
    ":ice_cream:": {
        "category": "food",
        "name": "ice cream",
        "unicode": "1f368"
    },
    ":ice_skate:": {
        "category": "activity",
        "name": "ice skate",
        "unicode": "26f8",
        "unicode_alt": "26f8-fe0f"
    },
    ":icecream:": {
        "category": "food",
        "name": "soft ice cream",
        "unicode": "1f366"
    },
    ":id:": {
        "category": "symbols",
        "name": "squared id",
        "unicode": "1f194"
    },
    ":ideograph_advantage:": {
        "category": "symbols",
        "name": "circled ideograph advantage",
        "unicode": "1f250"
    },
    ":imp:": {
        "category": "people",
        "name": "imp",
        "unicode": "1f47f"
    },
    ":inbox_tray:": {
        "category": "objects",
        "name": "inbox tray",
        "unicode": "1f4e5"
    },
    ":incoming_envelope:": {
        "category": "objects",
        "name": "incoming envelope",
        "unicode": "1f4e8"
    },
    ":information_desk_person:": {
        "category": "people",
        "name": "information desk person",
        "unicode": "1f481"
    },
    ":information_desk_person_tone1:": {
        "category": "people",
        "name": "information desk person tone 1",
        "unicode": "1f481-1f3fb"
    },
    ":information_desk_person_tone2:": {
        "category": "people",
        "name": "information desk person tone 2",
        "unicode": "1f481-1f3fc"
    },
    ":information_desk_person_tone3:": {
        "category": "people",
        "name": "information desk person tone 3",
        "unicode": "1f481-1f3fd"
    },
    ":information_desk_person_tone4:": {
        "category": "people",
        "name": "information desk person tone 4",
        "unicode": "1f481-1f3fe"
    },
    ":information_desk_person_tone5:": {
        "category": "people",
        "name": "information desk person tone 5",
        "unicode": "1f481-1f3ff"
    },
    ":information_source:": {
        "category": "symbols",
        "name": "information source",
        "unicode": "2139",
        "unicode_alt": "2139-fe0f"
    },
    ":innocent:": {
        "category": "people",
        "name": "smiling face with halo",
        "unicode": "1f607"
    },
    ":interrobang:": {
        "category": "symbols",
        "name": "exclamation question mark",
        "unicode": "2049",
        "unicode_alt": "2049-fe0f"
    },
    ":iphone:": {
        "category": "objects",
        "name": "mobile phone",
        "unicode": "1f4f1"
    },
    ":island:": {
        "category": "travel",
        "name": "desert island",
        "unicode": "1f3dd",
        "unicode_alt": "1f3dd-fe0f"
    },
    ":izakaya_lantern:": {
        "category": "objects",
        "name": "izakaya lantern",
        "unicode": "1f3ee"
    },
    ":jack_o_lantern:": {
        "category": "nature",
        "name": "jack-o-lantern",
        "unicode": "1f383"
    },
    ":japan:": {
        "category": "travel",
        "name": "silhouette of japan",
        "unicode": "1f5fe"
    },
    ":japanese_castle:": {
        "category": "travel",
        "name": "japanese castle",
        "unicode": "1f3ef"
    },
    ":japanese_goblin:": {
        "category": "people",
        "name": "japanese goblin",
        "unicode": "1f47a"
    },
    ":japanese_ogre:": {
        "category": "people",
        "name": "japanese ogre",
        "unicode": "1f479"
    },
    ":jeans:": {
        "category": "people",
        "name": "jeans",
        "unicode": "1f456"
    },
    ":joy:": {
        "category": "people",
        "name": "face with tears of joy",
        "unicode": "1f602"
    },
    ":joy_cat:": {
        "category": "people",
        "name": "cat face with tears of joy",
        "unicode": "1f639"
    },
    ":joystick:": {
        "category": "objects",
        "name": "joystick",
        "unicode": "1f579",
        "unicode_alt": "1f579-fe0f"
    },
    ":juggling:": {
        "category": "activity",
        "name": "juggling",
        "unicode": "1f939"
    },
    ":juggling_tone1:": {
        "category": "activity",
        "name": "juggling tone 1",
        "unicode": "1f939-1f3fb"
    },
    ":juggling_tone2:": {
        "category": "activity",
        "name": "juggling tone 2",
        "unicode": "1f939-1f3fc"
    },
    ":juggling_tone3:": {
        "category": "activity",
        "name": "juggling tone 3",
        "unicode": "1f939-1f3fd"
    },
    ":juggling_tone4:": {
        "category": "activity",
        "name": "juggling tone 4",
        "unicode": "1f939-1f3fe"
    },
    ":juggling_tone5:": {
        "category": "activity",
        "name": "juggling tone 5",
        "unicode": "1f939-1f3ff"
    },
    ":kaaba:": {
        "category": "travel",
        "name": "kaaba",
        "unicode": "1f54b"
    },
    ":key2:": {
        "category": "objects",
        "name": "old key",
        "unicode": "1f5dd",
        "unicode_alt": "1f5dd-fe0f"
    },
    ":key:": {
        "category": "objects",
        "name": "key",
        "unicode": "1f511"
    },
    ":keyboard:": {
        "category": "objects",
        "name": "keyboard",
        "unicode": "2328",
        "unicode_alt": "2328-fe0f"
    },
    ":keycap_ten:": {
        "category": "symbols",
        "name": "keycap ten",
        "unicode": "1f51f"
    },
    ":kimono:": {
        "category": "people",
        "name": "kimono",
        "unicode": "1f458"
    },
    ":kiss:": {
        "category": "people",
        "name": "kiss mark",
        "unicode": "1f48b"
    },
    ":kiss_mm:": {
        "category": "people",
        "name": "kiss (man,man)",
        "unicode": "1f468-2764-1f48b-1f468",
        "unicode_alt": "1f468-200d-2764-fe0f-200d-1f48b-200d-1f468"
    },
    ":kiss_ww:": {
        "category": "people",
        "name": "kiss (woman,woman)",
        "unicode": "1f469-2764-1f48b-1f469",
        "unicode_alt": "1f469-200d-2764-fe0f-200d-1f48b-200d-1f469"
    },
    ":kissing:": {
        "category": "people",
        "name": "kissing face",
        "unicode": "1f617"
    },
    ":kissing_cat:": {
        "category": "people",
        "name": "kissing cat face with closed eyes",
        "unicode": "1f63d"
    },
    ":kissing_closed_eyes:": {
        "category": "people",
        "name": "kissing face with closed eyes",
        "unicode": "1f61a"
    },
    ":kissing_heart:": {
        "category": "people",
        "name": "face throwing a kiss",
        "unicode": "1f618"
    },
    ":kissing_smiling_eyes:": {
        "category": "people",
        "name": "kissing face with smiling eyes",
        "unicode": "1f619"
    },
    ":kiwi:": {
        "category": "food",
        "name": "kiwifruit",
        "unicode": "1f95d"
    },
    ":knife:": {
        "category": "objects",
        "name": "hocho",
        "unicode": "1f52a"
    },
    ":koala:": {
        "category": "nature",
        "name": "koala",
        "unicode": "1f428"
    },
    ":koko:": {
        "category": "symbols",
        "name": "squared katakana koko",
        "unicode": "1f201"
    },
    ":label:": {
        "category": "objects",
        "name": "label",
        "unicode": "1f3f7",
        "unicode_alt": "1f3f7-fe0f"
    },
    ":large_blue_diamond:": {
        "category": "symbols",
        "name": "large blue diamond",
        "unicode": "1f537"
    },
    ":large_orange_diamond:": {
        "category": "symbols",
        "name": "large orange diamond",
        "unicode": "1f536"
    },
    ":last_quarter_moon:": {
        "category": "nature",
        "name": "last quarter moon symbol",
        "unicode": "1f317"
    },
    ":last_quarter_moon_with_face:": {
        "category": "nature",
        "name": "last quarter moon with face",
        "unicode": "1f31c"
    },
    ":laughing:": {
        "category": "people",
        "name": "smiling face with open mouth and tightly-closed eyes",
        "unicode": "1f606"
    },
    ":leaves:": {
        "category": "nature",
        "name": "leaf fluttering in wind",
        "unicode": "1f343"
    },
    ":ledger:": {
        "category": "objects",
        "name": "ledger",
        "unicode": "1f4d2"
    },
    ":left_facing_fist:": {
        "category": "people",
        "name": "left-facing fist",
        "unicode": "1f91b"
    },
    ":left_facing_fist_tone1:": {
        "category": "people",
        "name": "left facing fist tone 1",
        "unicode": "1f91b-1f3fb"
    },
    ":left_facing_fist_tone2:": {
        "category": "people",
        "name": "left facing fist tone 2",
        "unicode": "1f91b-1f3fc"
    },
    ":left_facing_fist_tone3:": {
        "category": "people",
        "name": "left facing fist tone 3",
        "unicode": "1f91b-1f3fd"
    },
    ":left_facing_fist_tone4:": {
        "category": "people",
        "name": "left facing fist tone 4",
        "unicode": "1f91b-1f3fe"
    },
    ":left_facing_fist_tone5:": {
        "category": "people",
        "name": "left facing fist tone 5",
        "unicode": "1f91b-1f3ff"
    },
    ":left_luggage:": {
        "category": "symbols",
        "name": "left luggage",
        "unicode": "1f6c5"
    },
    ":left_right_arrow:": {
        "category": "symbols",
        "name": "left right arrow",
        "unicode": "2194",
        "unicode_alt": "2194-fe0f"
    },
    ":leftwards_arrow_with_hook:": {
        "category": "symbols",
        "name": "leftwards arrow with hook",
        "unicode": "21a9",
        "unicode_alt": "21a9-fe0f"
    },
    ":lemon:": {
        "category": "food",
        "name": "lemon",
        "unicode": "1f34b"
    },
    ":leo:": {
        "category": "symbols",
        "name": "leo",
        "unicode": "264c",
        "unicode_alt": "264c-fe0f"
    },
    ":leopard:": {
        "category": "nature",
        "name": "leopard",
        "unicode": "1f406"
    },
    ":level_slider:": {
        "category": "objects",
        "name": "level slider",
        "unicode": "1f39a",
        "unicode_alt": "1f39a-fe0f"
    },
    ":levitate:": {
        "category": "activity",
        "name": "man in business suit levitating",
        "unicode": "1f574",
        "unicode_alt": "1f574-fe0f"
    },
    ":libra:": {
        "category": "symbols",
        "name": "libra",
        "unicode": "264e",
        "unicode_alt": "264e-fe0f"
    },
    ":lifter:": {
        "category": "activity",
        "name": "weight lifter",
        "unicode": "1f3cb",
        "unicode_alt": "1f3cb-fe0f"
    },
    ":lifter_tone1:": {
        "category": "activity",
        "name": "weight lifter tone 1",
        "unicode": "1f3cb-1f3fb"
    },
    ":lifter_tone2:": {
        "category": "activity",
        "name": "weight lifter tone 2",
        "unicode": "1f3cb-1f3fc"
    },
    ":lifter_tone3:": {
        "category": "activity",
        "name": "weight lifter tone 3",
        "unicode": "1f3cb-1f3fd"
    },
    ":lifter_tone4:": {
        "category": "activity",
        "name": "weight lifter tone 4",
        "unicode": "1f3cb-1f3fe"
    },
    ":lifter_tone5:": {
        "category": "activity",
        "name": "weight lifter tone 5",
        "unicode": "1f3cb-1f3ff"
    },
    ":light_rail:": {
        "category": "travel",
        "name": "light rail",
        "unicode": "1f688"
    },
    ":link:": {
        "category": "objects",
        "name": "link symbol",
        "unicode": "1f517"
    },
    ":lion_face:": {
        "category": "nature",
        "name": "lion face",
        "unicode": "1f981"
    },
    ":lips:": {
        "category": "people",
        "name": "mouth",
        "unicode": "1f444"
    },
    ":lipstick:": {
        "category": "people",
        "name": "lipstick",
        "unicode": "1f484"
    },
    ":lizard:": {
        "category": "nature",
        "name": "lizard",
        "unicode": "1f98e"
    },
    ":lock:": {
        "category": "objects",
        "name": "lock",
        "unicode": "1f512"
    },
    ":lock_with_ink_pen:": {
        "category": "objects",
        "name": "lock with ink pen",
        "unicode": "1f50f"
    },
    ":lollipop:": {
        "category": "food",
        "name": "lollipop",
        "unicode": "1f36d"
    },
    ":loop:": {
        "category": "symbols",
        "name": "double curly loop",
        "unicode": "27bf"
    },
    ":loud_sound:": {
        "category": "symbols",
        "name": "speaker with three sound waves",
        "unicode": "1f50a"
    },
    ":loudspeaker:": {
        "category": "symbols",
        "name": "public address loudspeaker",
        "unicode": "1f4e2"
    },
    ":love_hotel:": {
        "category": "travel",
        "name": "love hotel",
        "unicode": "1f3e9"
    },
    ":love_letter:": {
        "category": "objects",
        "name": "love letter",
        "unicode": "1f48c"
    },
    ":low_brightness:": {
        "category": "symbols",
        "name": "low brightness symbol",
        "unicode": "1f505"
    },
    ":lying_face:": {
        "category": "people",
        "name": "lying face",
        "unicode": "1f925"
    },
    ":m:": {
        "category": "symbols",
        "name": "circled latin capital letter m",
        "unicode": "24c2",
        "unicode_alt": "24c2-fe0f"
    },
    ":mag:": {
        "category": "objects",
        "name": "left-pointing magnifying glass",
        "unicode": "1f50d"
    },
    ":mag_right:": {
        "category": "objects",
        "name": "right-pointing magnifying glass",
        "unicode": "1f50e"
    },
    ":mahjong:": {
        "category": "symbols",
        "name": "mahjong tile red dragon",
        "unicode": "1f004",
        "unicode_alt": "1f004-fe0f"
    },
    ":mailbox:": {
        "category": "objects",
        "name": "closed mailbox with raised flag",
        "unicode": "1f4eb"
    },
    ":mailbox_closed:": {
        "category": "objects",
        "name": "closed mailbox with lowered flag",
        "unicode": "1f4ea"
    },
    ":mailbox_with_mail:": {
        "category": "objects",
        "name": "open mailbox with raised flag",
        "unicode": "1f4ec"
    },
    ":mailbox_with_no_mail:": {
        "category": "objects",
        "name": "open mailbox with lowered flag",
        "unicode": "1f4ed"
    },
    ":man:": {
        "category": "people",
        "name": "man",
        "unicode": "1f468"
    },
    ":man_dancing:": {
        "category": "people",
        "name": "man dancing",
        "unicode": "1f57a"
    },
    ":man_dancing_tone1:": {
        "category": "people",
        "name": "man dancing tone 1",
        "unicode": "1f57a-1f3fb"
    },
    ":man_dancing_tone2:": {
        "category": "people",
        "name": "man dancing tone 2",
        "unicode": "1f57a-1f3fc"
    },
    ":man_dancing_tone3:": {
        "category": "people",
        "name": "man dancing tone 3",
        "unicode": "1f57a-1f3fd"
    },
    ":man_dancing_tone4:": {
        "category": "people",
        "name": "man dancing tone 4",
        "unicode": "1f57a-1f3fe"
    },
    ":man_dancing_tone5:": {
        "category": "people",
        "name": "man dancing tone 5",
        "unicode": "1f57a-1f3ff"
    },
    ":man_in_tuxedo:": {
        "category": "people",
        "name": "man in tuxedo",
        "unicode": "1f935"
    },
    ":man_in_tuxedo_tone1:": {
        "category": "people",
        "name": "man in tuxedo tone 1",
        "unicode": "1f935-1f3fb"
    },
    ":man_in_tuxedo_tone2:": {
        "category": "people",
        "name": "man in tuxedo tone 2",
        "unicode": "1f935-1f3fc"
    },
    ":man_in_tuxedo_tone3:": {
        "category": "people",
        "name": "man in tuxedo tone 3",
        "unicode": "1f935-1f3fd"
    },
    ":man_in_tuxedo_tone4:": {
        "category": "people",
        "name": "man in tuxedo tone 4",
        "unicode": "1f935-1f3fe"
    },
    ":man_in_tuxedo_tone5:": {
        "category": "people",
        "name": "man in tuxedo tone 5",
        "unicode": "1f935-1f3ff"
    },
    ":man_tone1:": {
        "category": "people",
        "name": "man tone 1",
        "unicode": "1f468-1f3fb"
    },
    ":man_tone2:": {
        "category": "people",
        "name": "man tone 2",
        "unicode": "1f468-1f3fc"
    },
    ":man_tone3:": {
        "category": "people",
        "name": "man tone 3",
        "unicode": "1f468-1f3fd"
    },
    ":man_tone4:": {
        "category": "people",
        "name": "man tone 4",
        "unicode": "1f468-1f3fe"
    },
    ":man_tone5:": {
        "category": "people",
        "name": "man tone 5",
        "unicode": "1f468-1f3ff"
    },
    ":man_with_gua_pi_mao:": {
        "category": "people",
        "name": "man with gua pi mao",
        "unicode": "1f472"
    },
    ":man_with_gua_pi_mao_tone1:": {
        "category": "people",
        "name": "man with gua pi mao tone 1",
        "unicode": "1f472-1f3fb"
    },
    ":man_with_gua_pi_mao_tone2:": {
        "category": "people",
        "name": "man with gua pi mao tone 2",
        "unicode": "1f472-1f3fc"
    },
    ":man_with_gua_pi_mao_tone3:": {
        "category": "people",
        "name": "man with gua pi mao tone 3",
        "unicode": "1f472-1f3fd"
    },
    ":man_with_gua_pi_mao_tone4:": {
        "category": "people",
        "name": "man with gua pi mao tone 4",
        "unicode": "1f472-1f3fe"
    },
    ":man_with_gua_pi_mao_tone5:": {
        "category": "people",
        "name": "man with gua pi mao tone 5",
        "unicode": "1f472-1f3ff"
    },
    ":man_with_turban:": {
        "category": "people",
        "name": "man with turban",
        "unicode": "1f473"
    },
    ":man_with_turban_tone1:": {
        "category": "people",
        "name": "man with turban tone 1",
        "unicode": "1f473-1f3fb"
    },
    ":man_with_turban_tone2:": {
        "category": "people",
        "name": "man with turban tone 2",
        "unicode": "1f473-1f3fc"
    },
    ":man_with_turban_tone3:": {
        "category": "people",
        "name": "man with turban tone 3",
        "unicode": "1f473-1f3fd"
    },
    ":man_with_turban_tone4:": {
        "category": "people",
        "name": "man with turban tone 4",
        "unicode": "1f473-1f3fe"
    },
    ":man_with_turban_tone5:": {
        "category": "people",
        "name": "man with turban tone 5",
        "unicode": "1f473-1f3ff"
    },
    ":mans_shoe:": {
        "category": "people",
        "name": "mans shoe",
        "unicode": "1f45e"
    },
    ":map:": {
        "category": "objects",
        "name": "world map",
        "unicode": "1f5fa",
        "unicode_alt": "1f5fa-fe0f"
    },
    ":maple_leaf:": {
        "category": "nature",
        "name": "maple leaf",
        "unicode": "1f341"
    },
    ":martial_arts_uniform:": {
        "category": "activity",
        "name": "martial arts uniform",
        "unicode": "1f94b"
    },
    ":mask:": {
        "category": "people",
        "name": "face with medical mask",
        "unicode": "1f637"
    },
    ":massage:": {
        "category": "people",
        "name": "face massage",
        "unicode": "1f486"
    },
    ":massage_tone1:": {
        "category": "people",
        "name": "face massage tone 1",
        "unicode": "1f486-1f3fb"
    },
    ":massage_tone2:": {
        "category": "people",
        "name": "face massage tone 2",
        "unicode": "1f486-1f3fc"
    },
    ":massage_tone3:": {
        "category": "people",
        "name": "face massage tone 3",
        "unicode": "1f486-1f3fd"
    },
    ":massage_tone4:": {
        "category": "people",
        "name": "face massage tone 4",
        "unicode": "1f486-1f3fe"
    },
    ":massage_tone5:": {
        "category": "people",
        "name": "face massage tone 5",
        "unicode": "1f486-1f3ff"
    },
    ":meat_on_bone:": {
        "category": "food",
        "name": "meat on bone",
        "unicode": "1f356"
    },
    ":medal:": {
        "category": "activity",
        "name": "sports medal",
        "unicode": "1f3c5"
    },
    ":mega:": {
        "category": "symbols",
        "name": "cheering megaphone",
        "unicode": "1f4e3"
    },
    ":melon:": {
        "category": "food",
        "name": "melon",
        "unicode": "1f348"
    },
    ":menorah:": {
        "category": "symbols",
        "name": "menorah with nine branches",
        "unicode": "1f54e"
    },
    ":mens:": {
        "category": "symbols",
        "name": "mens symbol",
        "unicode": "1f6b9"
    },
    ":metal:": {
        "category": "people",
        "name": "sign of the horns",
        "unicode": "1f918"
    },
    ":metal_tone1:": {
        "category": "people",
        "name": "sign of the horns tone 1",
        "unicode": "1f918-1f3fb"
    },
    ":metal_tone2:": {
        "category": "people",
        "name": "sign of the horns tone 2",
        "unicode": "1f918-1f3fc"
    },
    ":metal_tone3:": {
        "category": "people",
        "name": "sign of the horns tone 3",
        "unicode": "1f918-1f3fd"
    },
    ":metal_tone4:": {
        "category": "people",
        "name": "sign of the horns tone 4",
        "unicode": "1f918-1f3fe"
    },
    ":metal_tone5:": {
        "category": "people",
        "name": "sign of the horns tone 5",
        "unicode": "1f918-1f3ff"
    },
    ":metro:": {
        "category": "travel",
        "name": "metro",
        "unicode": "1f687"
    },
    ":microphone2:": {
        "category": "objects",
        "name": "studio microphone",
        "unicode": "1f399",
        "unicode_alt": "1f399-fe0f"
    },
    ":microphone:": {
        "category": "activity",
        "name": "microphone",
        "unicode": "1f3a4"
    },
    ":microscope:": {
        "category": "objects",
        "name": "microscope",
        "unicode": "1f52c"
    },
    ":middle_finger:": {
        "category": "people",
        "name": "reversed hand with middle finger extended",
        "unicode": "1f595"
    },
    ":middle_finger_tone1:": {
        "category": "people",
        "name": "reversed hand with middle finger extended tone 1",
        "unicode": "1f595-1f3fb"
    },
    ":middle_finger_tone2:": {
        "category": "people",
        "name": "reversed hand with middle finger extended tone 2",
        "unicode": "1f595-1f3fc"
    },
    ":middle_finger_tone3:": {
        "category": "people",
        "name": "reversed hand with middle finger extended tone 3",
        "unicode": "1f595-1f3fd"
    },
    ":middle_finger_tone4:": {
        "category": "people",
        "name": "reversed hand with middle finger extended tone 4",
        "unicode": "1f595-1f3fe"
    },
    ":middle_finger_tone5:": {
        "category": "people",
        "name": "reversed hand with middle finger extended tone 5",
        "unicode": "1f595-1f3ff"
    },
    ":military_medal:": {
        "category": "activity",
        "name": "military medal",
        "unicode": "1f396",
        "unicode_alt": "1f396-fe0f"
    },
    ":milk:": {
        "category": "food",
        "name": "glass of milk",
        "unicode": "1f95b"
    },
    ":milky_way:": {
        "category": "travel",
        "name": "milky way",
        "unicode": "1f30c"
    },
    ":minibus:": {
        "category": "travel",
        "name": "minibus",
        "unicode": "1f690"
    },
    ":minidisc:": {
        "category": "objects",
        "name": "minidisc",
        "unicode": "1f4bd"
    },
    ":mobile_phone_off:": {
        "category": "symbols",
        "name": "mobile phone off",
        "unicode": "1f4f4"
    },
    ":money_mouth:": {
        "category": "people",
        "name": "money-mouth face",
        "unicode": "1f911"
    },
    ":money_with_wings:": {
        "category": "objects",
        "name": "money with wings",
        "unicode": "1f4b8"
    },
    ":moneybag:": {
        "category": "objects",
        "name": "money bag",
        "unicode": "1f4b0"
    },
    ":monkey:": {
        "category": "nature",
        "name": "monkey",
        "unicode": "1f412"
    },
    ":monkey_face:": {
        "category": "nature",
        "name": "monkey face",
        "unicode": "1f435"
    },
    ":monorail:": {
        "category": "travel",
        "name": "monorail",
        "unicode": "1f69d"
    },
    ":mortar_board:": {
        "category": "people",
        "name": "graduation cap",
        "unicode": "1f393"
    },
    ":mosque:": {
        "category": "travel",
        "name": "mosque",
        "unicode": "1f54c"
    },
    ":motor_scooter:": {
        "category": "travel",
        "name": "motor scooter",
        "unicode": "1f6f5"
    },
    ":motorboat:": {
        "category": "travel",
        "name": "motorboat",
        "unicode": "1f6e5",
        "unicode_alt": "1f6e5-fe0f"
    },
    ":motorcycle:": {
        "category": "travel",
        "name": "racing motorcycle",
        "unicode": "1f3cd",
        "unicode_alt": "1f3cd-fe0f"
    },
    ":motorway:": {
        "category": "travel",
        "name": "motorway",
        "unicode": "1f6e3",
        "unicode_alt": "1f6e3-fe0f"
    },
    ":mount_fuji:": {
        "category": "travel",
        "name": "mount fuji",
        "unicode": "1f5fb"
    },
    ":mountain:": {
        "category": "travel",
        "name": "mountain",
        "unicode": "26f0",
        "unicode_alt": "26f0-fe0f"
    },
    ":mountain_bicyclist:": {
        "category": "activity",
        "name": "mountain bicyclist",
        "unicode": "1f6b5"
    },
    ":mountain_bicyclist_tone1:": {
        "category": "activity",
        "name": "mountain bicyclist tone 1",
        "unicode": "1f6b5-1f3fb"
    },
    ":mountain_bicyclist_tone2:": {
        "category": "activity",
        "name": "mountain bicyclist tone 2",
        "unicode": "1f6b5-1f3fc"
    },
    ":mountain_bicyclist_tone3:": {
        "category": "activity",
        "name": "mountain bicyclist tone 3",
        "unicode": "1f6b5-1f3fd"
    },
    ":mountain_bicyclist_tone4:": {
        "category": "activity",
        "name": "mountain bicyclist tone 4",
        "unicode": "1f6b5-1f3fe"
    },
    ":mountain_bicyclist_tone5:": {
        "category": "activity",
        "name": "mountain bicyclist tone 5",
        "unicode": "1f6b5-1f3ff"
    },
    ":mountain_cableway:": {
        "category": "travel",
        "name": "mountain cableway",
        "unicode": "1f6a0"
    },
    ":mountain_railway:": {
        "category": "travel",
        "name": "mountain railway",
        "unicode": "1f69e"
    },
    ":mountain_snow:": {
        "category": "travel",
        "name": "snow capped mountain",
        "unicode": "1f3d4",
        "unicode_alt": "1f3d4-fe0f"
    },
    ":mouse2:": {
        "category": "nature",
        "name": "mouse",
        "unicode": "1f401"
    },
    ":mouse:": {
        "category": "nature",
        "name": "mouse face",
        "unicode": "1f42d"
    },
    ":mouse_three_button:": {
        "category": "objects",
        "name": "three button mouse",
        "unicode": "1f5b1",
        "unicode_alt": "1f5b1-fe0f"
    },
    ":movie_camera:": {
        "category": "objects",
        "name": "movie camera",
        "unicode": "1f3a5"
    },
    ":moyai:": {
        "category": "objects",
        "name": "moyai",
        "unicode": "1f5ff"
    },
    ":mrs_claus:": {
        "category": "people",
        "name": "mother christmas",
        "unicode": "1f936"
    },
    ":mrs_claus_tone1:": {
        "category": "people",
        "name": "mother christmas tone 1",
        "unicode": "1f936-1f3fb"
    },
    ":mrs_claus_tone2:": {
        "category": "people",
        "name": "mother christmas tone 2",
        "unicode": "1f936-1f3fc"
    },
    ":mrs_claus_tone3:": {
        "category": "people",
        "name": "mother christmas tone 3",
        "unicode": "1f936-1f3fd"
    },
    ":mrs_claus_tone4:": {
        "category": "people",
        "name": "mother christmas tone 4",
        "unicode": "1f936-1f3fe"
    },
    ":mrs_claus_tone5:": {
        "category": "people",
        "name": "mother christmas tone 5",
        "unicode": "1f936-1f3ff"
    },
    ":muscle:": {
        "category": "people",
        "name": "flexed biceps",
        "unicode": "1f4aa"
    },
    ":muscle_tone1:": {
        "category": "people",
        "name": "flexed biceps tone 1",
        "unicode": "1f4aa-1f3fb"
    },
    ":muscle_tone2:": {
        "category": "people",
        "name": "flexed biceps tone 2",
        "unicode": "1f4aa-1f3fc"
    },
    ":muscle_tone3:": {
        "category": "people",
        "name": "flexed biceps tone 3",
        "unicode": "1f4aa-1f3fd"
    },
    ":muscle_tone4:": {
        "category": "people",
        "name": "flexed biceps tone 4",
        "unicode": "1f4aa-1f3fe"
    },
    ":muscle_tone5:": {
        "category": "people",
        "name": "flexed biceps tone 5",
        "unicode": "1f4aa-1f3ff"
    },
    ":mushroom:": {
        "category": "nature",
        "name": "mushroom",
        "unicode": "1f344"
    },
    ":musical_keyboard:": {
        "category": "activity",
        "name": "musical keyboard",
        "unicode": "1f3b9"
    },
    ":musical_note:": {
        "category": "symbols",
        "name": "musical note",
        "unicode": "1f3b5"
    },
    ":musical_score:": {
        "category": "activity",
        "name": "musical score",
        "unicode": "1f3bc"
    },
    ":mute:": {
        "category": "symbols",
        "name": "speaker with cancellation stroke",
        "unicode": "1f507"
    },
    ":nail_care:": {
        "category": "people",
        "name": "nail polish",
        "unicode": "1f485"
    },
    ":nail_care_tone1:": {
        "category": "people",
        "name": "nail polish tone 1",
        "unicode": "1f485-1f3fb"
    },
    ":nail_care_tone2:": {
        "category": "people",
        "name": "nail polish tone 2",
        "unicode": "1f485-1f3fc"
    },
    ":nail_care_tone3:": {
        "category": "people",
        "name": "nail polish tone 3",
        "unicode": "1f485-1f3fd"
    },
    ":nail_care_tone4:": {
        "category": "people",
        "name": "nail polish tone 4",
        "unicode": "1f485-1f3fe"
    },
    ":nail_care_tone5:": {
        "category": "people",
        "name": "nail polish tone 5",
        "unicode": "1f485-1f3ff"
    },
    ":name_badge:": {
        "category": "symbols",
        "name": "name badge",
        "unicode": "1f4db"
    },
    ":nauseated_face:": {
        "category": "people",
        "name": "nauseated face",
        "unicode": "1f922"
    },
    ":necktie:": {
        "category": "people",
        "name": "necktie",
        "unicode": "1f454"
    },
    ":negative_squared_cross_mark:": {
        "category": "symbols",
        "name": "negative squared cross mark",
        "unicode": "274e"
    },
    ":nerd:": {
        "category": "people",
        "name": "nerd face",
        "unicode": "1f913"
    },
    ":neutral_face:": {
        "category": "people",
        "name": "neutral face",
        "unicode": "1f610"
    },
    ":new:": {
        "category": "symbols",
        "name": "squared new",
        "unicode": "1f195"
    },
    ":new_moon:": {
        "category": "nature",
        "name": "new moon symbol",
        "unicode": "1f311"
    },
    ":new_moon_with_face:": {
        "category": "nature",
        "name": "new moon with face",
        "unicode": "1f31a"
    },
    ":newspaper2:": {
        "category": "objects",
        "name": "rolled-up newspaper",
        "unicode": "1f5de",
        "unicode_alt": "1f5de-fe0f"
    },
    ":newspaper:": {
        "category": "objects",
        "name": "newspaper",
        "unicode": "1f4f0"
    },
    ":ng:": {
        "category": "symbols",
        "name": "squared ng",
        "unicode": "1f196"
    },
    ":night_with_stars:": {
        "category": "travel",
        "name": "night with stars",
        "unicode": "1f303"
    },
    ":nine:": {
        "category": "symbols",
        "name": "keycap digit nine",
        "unicode": "0039-20e3",
        "unicode_alt": "0039-fe0f-20e3"
    },
    ":no_bell:": {
        "category": "symbols",
        "name": "bell with cancellation stroke",
        "unicode": "1f515"
    },
    ":no_bicycles:": {
        "category": "symbols",
        "name": "no bicycles",
        "unicode": "1f6b3"
    },
    ":no_entry:": {
        "category": "symbols",
        "name": "no entry",
        "unicode": "26d4",
        "unicode_alt": "26d4-fe0f"
    },
    ":no_entry_sign:": {
        "category": "symbols",
        "name": "no entry sign",
        "unicode": "1f6ab"
    },
    ":no_good:": {
        "category": "people",
        "name": "face with no good gesture",
        "unicode": "1f645"
    },
    ":no_good_tone1:": {
        "category": "people",
        "name": "face with no good gesture tone 1",
        "unicode": "1f645-1f3fb"
    },
    ":no_good_tone2:": {
        "category": "people",
        "name": "face with no good gesture tone 2",
        "unicode": "1f645-1f3fc"
    },
    ":no_good_tone3:": {
        "category": "people",
        "name": "face with no good gesture tone 3",
        "unicode": "1f645-1f3fd"
    },
    ":no_good_tone4:": {
        "category": "people",
        "name": "face with no good gesture tone 4",
        "unicode": "1f645-1f3fe"
    },
    ":no_good_tone5:": {
        "category": "people",
        "name": "face with no good gesture tone 5",
        "unicode": "1f645-1f3ff"
    },
    ":no_mobile_phones:": {
        "category": "symbols",
        "name": "no mobile phones",
        "unicode": "1f4f5"
    },
    ":no_mouth:": {
        "category": "people",
        "name": "face without mouth",
        "unicode": "1f636"
    },
    ":no_pedestrians:": {
        "category": "symbols",
        "name": "no pedestrians",
        "unicode": "1f6b7"
    },
    ":no_smoking:": {
        "category": "symbols",
        "name": "no smoking symbol",
        "unicode": "1f6ad"
    },
    ":non-potable_water:": {
        "category": "symbols",
        "name": "non-potable water symbol",
        "unicode": "1f6b1"
    },
    ":nose:": {
        "category": "people",
        "name": "nose",
        "unicode": "1f443"
    },
    ":nose_tone1:": {
        "category": "people",
        "name": "nose tone 1",
        "unicode": "1f443-1f3fb"
    },
    ":nose_tone2:": {
        "category": "people",
        "name": "nose tone 2",
        "unicode": "1f443-1f3fc"
    },
    ":nose_tone3:": {
        "category": "people",
        "name": "nose tone 3",
        "unicode": "1f443-1f3fd"
    },
    ":nose_tone4:": {
        "category": "people",
        "name": "nose tone 4",
        "unicode": "1f443-1f3fe"
    },
    ":nose_tone5:": {
        "category": "people",
        "name": "nose tone 5",
        "unicode": "1f443-1f3ff"
    },
    ":notebook:": {
        "category": "objects",
        "name": "notebook",
        "unicode": "1f4d3"
    },
    ":notebook_with_decorative_cover:": {
        "category": "objects",
        "name": "notebook with decorative cover",
        "unicode": "1f4d4"
    },
    ":notepad_spiral:": {
        "category": "objects",
        "name": "spiral note pad",
        "unicode": "1f5d2",
        "unicode_alt": "1f5d2-fe0f"
    },
    ":notes:": {
        "category": "symbols",
        "name": "multiple musical notes",
        "unicode": "1f3b6"
    },
    ":nut_and_bolt:": {
        "category": "objects",
        "name": "nut and bolt",
        "unicode": "1f529"
    },
    ":o2:": {
        "category": "symbols",
        "name": "negative squared latin capital letter o",
        "unicode": "1f17e"
    },
    ":o:": {
        "category": "symbols",
        "name": "heavy large circle",
        "unicode": "2b55",
        "unicode_alt": "2b55-fe0f"
    },
    ":ocean:": {
        "category": "nature",
        "name": "water wave",
        "unicode": "1f30a"
    },
    ":octagonal_sign:": {
        "category": "symbols",
        "name": "octagonal sign",
        "unicode": "1f6d1"
    },
    ":octopus:": {
        "category": "nature",
        "name": "octopus",
        "unicode": "1f419"
    },
    ":oden:": {
        "category": "food",
        "name": "oden",
        "unicode": "1f362"
    },
    ":office:": {
        "category": "travel",
        "name": "office building",
        "unicode": "1f3e2"
    },
    ":oil:": {
        "category": "objects",
        "name": "oil drum",
        "unicode": "1f6e2",
        "unicode_alt": "1f6e2-fe0f"
    },
    ":ok:": {
        "category": "symbols",
        "name": "squared ok",
        "unicode": "1f197"
    },
    ":ok_hand:": {
        "category": "people",
        "name": "ok hand sign",
        "unicode": "1f44c"
    },
    ":ok_hand_tone1:": {
        "category": "people",
        "name": "ok hand sign tone 1",
        "unicode": "1f44c-1f3fb"
    },
    ":ok_hand_tone2:": {
        "category": "people",
        "name": "ok hand sign tone 2",
        "unicode": "1f44c-1f3fc"
    },
    ":ok_hand_tone3:": {
        "category": "people",
        "name": "ok hand sign tone 3",
        "unicode": "1f44c-1f3fd"
    },
    ":ok_hand_tone4:": {
        "category": "people",
        "name": "ok hand sign tone 4",
        "unicode": "1f44c-1f3fe"
    },
    ":ok_hand_tone5:": {
        "category": "people",
        "name": "ok hand sign tone 5",
        "unicode": "1f44c-1f3ff"
    },
    ":ok_woman:": {
        "category": "people",
        "name": "face with ok gesture",
        "unicode": "1f646"
    },
    ":ok_woman_tone1:": {
        "category": "people",
        "name": "face with ok gesture tone1",
        "unicode": "1f646-1f3fb"
    },
    ":ok_woman_tone2:": {
        "category": "people",
        "name": "face with ok gesture tone2",
        "unicode": "1f646-1f3fc"
    },
    ":ok_woman_tone3:": {
        "category": "people",
        "name": "face with ok gesture tone3",
        "unicode": "1f646-1f3fd"
    },
    ":ok_woman_tone4:": {
        "category": "people",
        "name": "face with ok gesture tone4",
        "unicode": "1f646-1f3fe"
    },
    ":ok_woman_tone5:": {
        "category": "people",
        "name": "face with ok gesture tone5",
        "unicode": "1f646-1f3ff"
    },
    ":older_man:": {
        "category": "people",
        "name": "older man",
        "unicode": "1f474"
    },
    ":older_man_tone1:": {
        "category": "people",
        "name": "older man tone 1",
        "unicode": "1f474-1f3fb"
    },
    ":older_man_tone2:": {
        "category": "people",
        "name": "older man tone 2",
        "unicode": "1f474-1f3fc"
    },
    ":older_man_tone3:": {
        "category": "people",
        "name": "older man tone 3",
        "unicode": "1f474-1f3fd"
    },
    ":older_man_tone4:": {
        "category": "people",
        "name": "older man tone 4",
        "unicode": "1f474-1f3fe"
    },
    ":older_man_tone5:": {
        "category": "people",
        "name": "older man tone 5",
        "unicode": "1f474-1f3ff"
    },
    ":older_woman:": {
        "category": "people",
        "name": "older woman",
        "unicode": "1f475"
    },
    ":older_woman_tone1:": {
        "category": "people",
        "name": "older woman tone 1",
        "unicode": "1f475-1f3fb"
    },
    ":older_woman_tone2:": {
        "category": "people",
        "name": "older woman tone 2",
        "unicode": "1f475-1f3fc"
    },
    ":older_woman_tone3:": {
        "category": "people",
        "name": "older woman tone 3",
        "unicode": "1f475-1f3fd"
    },
    ":older_woman_tone4:": {
        "category": "people",
        "name": "older woman tone 4",
        "unicode": "1f475-1f3fe"
    },
    ":older_woman_tone5:": {
        "category": "people",
        "name": "older woman tone 5",
        "unicode": "1f475-1f3ff"
    },
    ":om_symbol:": {
        "category": "symbols",
        "name": "om symbol",
        "unicode": "1f549",
        "unicode_alt": "1f549-fe0f"
    },
    ":on:": {
        "category": "symbols",
        "name": "on with exclamation mark with left right arrow abo",
        "unicode": "1f51b"
    },
    ":oncoming_automobile:": {
        "category": "travel",
        "name": "oncoming automobile",
        "unicode": "1f698"
    },
    ":oncoming_bus:": {
        "category": "travel",
        "name": "oncoming bus",
        "unicode": "1f68d"
    },
    ":oncoming_police_car:": {
        "category": "travel",
        "name": "oncoming police car",
        "unicode": "1f694"
    },
    ":oncoming_taxi:": {
        "category": "travel",
        "name": "oncoming taxi",
        "unicode": "1f696"
    },
    ":one:": {
        "category": "symbols",
        "name": "keycap digit one",
        "unicode": "0031-20e3",
        "unicode_alt": "0031-fe0f-20e3"
    },
    ":open_file_folder:": {
        "category": "objects",
        "name": "open file folder",
        "unicode": "1f4c2"
    },
    ":open_hands:": {
        "category": "people",
        "name": "open hands sign",
        "unicode": "1f450"
    },
    ":open_hands_tone1:": {
        "category": "people",
        "name": "open hands sign tone 1",
        "unicode": "1f450-1f3fb"
    },
    ":open_hands_tone2:": {
        "category": "people",
        "name": "open hands sign tone 2",
        "unicode": "1f450-1f3fc"
    },
    ":open_hands_tone3:": {
        "category": "people",
        "name": "open hands sign tone 3",
        "unicode": "1f450-1f3fd"
    },
    ":open_hands_tone4:": {
        "category": "people",
        "name": "open hands sign tone 4",
        "unicode": "1f450-1f3fe"
    },
    ":open_hands_tone5:": {
        "category": "people",
        "name": "open hands sign tone 5",
        "unicode": "1f450-1f3ff"
    },
    ":open_mouth:": {
        "category": "people",
        "name": "face with open mouth",
        "unicode": "1f62e"
    },
    ":ophiuchus:": {
        "category": "symbols",
        "name": "ophiuchus",
        "unicode": "26ce"
    },
    ":orange_book:": {
        "category": "objects",
        "name": "orange book",
        "unicode": "1f4d9"
    },
    ":orthodox_cross:": {
        "category": "symbols",
        "name": "orthodox cross",
        "unicode": "2626",
        "unicode_alt": "2626-fe0f"
    },
    ":outbox_tray:": {
        "category": "objects",
        "name": "outbox tray",
        "unicode": "1f4e4"
    },
    ":owl:": {
        "category": "nature",
        "name": "owl",
        "unicode": "1f989"
    },
    ":ox:": {
        "category": "nature",
        "name": "ox",
        "unicode": "1f402"
    },
    ":package:": {
        "category": "objects",
        "name": "package",
        "unicode": "1f4e6"
    },
    ":page_facing_up:": {
        "category": "objects",
        "name": "page facing up",
        "unicode": "1f4c4"
    },
    ":page_with_curl:": {
        "category": "objects",
        "name": "page with curl",
        "unicode": "1f4c3"
    },
    ":pager:": {
        "category": "objects",
        "name": "pager",
        "unicode": "1f4df"
    },
    ":paintbrush:": {
        "category": "objects",
        "name": "lower left paintbrush",
        "unicode": "1f58c",
        "unicode_alt": "1f58c-fe0f"
    },
    ":palm_tree:": {
        "category": "nature",
        "name": "palm tree",
        "unicode": "1f334"
    },
    ":pancakes:": {
        "category": "food",
        "name": "pancakes",
        "unicode": "1f95e"
    },
    ":panda_face:": {
        "category": "nature",
        "name": "panda face",
        "unicode": "1f43c"
    },
    ":paperclip:": {
        "category": "objects",
        "name": "paperclip",
        "unicode": "1f4ce"
    },
    ":paperclips:": {
        "category": "objects",
        "name": "linked paperclips",
        "unicode": "1f587",
        "unicode_alt": "1f587-fe0f"
    },
    ":park:": {
        "category": "travel",
        "name": "national park",
        "unicode": "1f3de",
        "unicode_alt": "1f3de-fe0f"
    },
    ":parking:": {
        "category": "symbols",
        "name": "negative squared latin capital letter p",
        "unicode": "1f17f",
        "unicode_alt": "1f17f-fe0f"
    },
    ":part_alternation_mark:": {
        "category": "symbols",
        "name": "part alternation mark",
        "unicode": "303d",
        "unicode_alt": "303d-fe0f"
    },
    ":partly_sunny:": {
        "category": "nature",
        "name": "sun behind cloud",
        "unicode": "26c5",
        "unicode_alt": "26c5-fe0f"
    },
    ":passport_control:": {
        "category": "symbols",
        "name": "passport control",
        "unicode": "1f6c2"
    },
    ":pause_button:": {
        "category": "symbols",
        "name": "double vertical bar",
        "unicode": "23f8",
        "unicode_alt": "23f8-fe0f"
    },
    ":peace:": {
        "category": "symbols",
        "name": "peace symbol",
        "unicode": "262e",
        "unicode_alt": "262e-fe0f"
    },
    ":peach:": {
        "category": "food",
        "name": "peach",
        "unicode": "1f351"
    },
    ":peanuts:": {
        "category": "food",
        "name": "peanuts",
        "unicode": "1f95c"
    },
    ":pear:": {
        "category": "food",
        "name": "pear",
        "unicode": "1f350"
    },
    ":pen_ballpoint:": {
        "category": "objects",
        "name": "lower left ballpoint pen",
        "unicode": "1f58a",
        "unicode_alt": "1f58a-fe0f"
    },
    ":pen_fountain:": {
        "category": "objects",
        "name": "lower left fountain pen",
        "unicode": "1f58b",
        "unicode_alt": "1f58b-fe0f"
    },
    ":pencil2:": {
        "category": "objects",
        "name": "pencil",
        "unicode": "270f",
        "unicode_alt": "270f-fe0f"
    },
    ":pencil:": {
        "category": "objects",
        "name": "memo",
        "unicode": "1f4dd"
    },
    ":penguin:": {
        "category": "nature",
        "name": "penguin",
        "unicode": "1f427"
    },
    ":pensive:": {
        "category": "people",
        "name": "pensive face",
        "unicode": "1f614"
    },
    ":performing_arts:": {
        "category": "activity",
        "name": "performing arts",
        "unicode": "1f3ad"
    },
    ":persevere:": {
        "category": "people",
        "name": "persevering face",
        "unicode": "1f623"
    },
    ":person_frowning:": {
        "category": "people",
        "name": "person frowning",
        "unicode": "1f64d"
    },
    ":person_frowning_tone1:": {
        "category": "people",
        "name": "person frowning tone 1",
        "unicode": "1f64d-1f3fb"
    },
    ":person_frowning_tone2:": {
        "category": "people",
        "name": "person frowning tone 2",
        "unicode": "1f64d-1f3fc"
    },
    ":person_frowning_tone3:": {
        "category": "people",
        "name": "person frowning tone 3",
        "unicode": "1f64d-1f3fd"
    },
    ":person_frowning_tone4:": {
        "category": "people",
        "name": "person frowning tone 4",
        "unicode": "1f64d-1f3fe"
    },
    ":person_frowning_tone5:": {
        "category": "people",
        "name": "person frowning tone 5",
        "unicode": "1f64d-1f3ff"
    },
    ":person_with_blond_hair:": {
        "category": "people",
        "name": "person with blond hair",
        "unicode": "1f471"
    },
    ":person_with_blond_hair_tone1:": {
        "category": "people",
        "name": "person with blond hair tone 1",
        "unicode": "1f471-1f3fb"
    },
    ":person_with_blond_hair_tone2:": {
        "category": "people",
        "name": "person with blond hair tone 2",
        "unicode": "1f471-1f3fc"
    },
    ":person_with_blond_hair_tone3:": {
        "category": "people",
        "name": "person with blond hair tone 3",
        "unicode": "1f471-1f3fd"
    },
    ":person_with_blond_hair_tone4:": {
        "category": "people",
        "name": "person with blond hair tone 4",
        "unicode": "1f471-1f3fe"
    },
    ":person_with_blond_hair_tone5:": {
        "category": "people",
        "name": "person with blond hair tone 5",
        "unicode": "1f471-1f3ff"
    },
    ":person_with_pouting_face:": {
        "category": "people",
        "name": "person with pouting face",
        "unicode": "1f64e"
    },
    ":person_with_pouting_face_tone1:": {
        "category": "people",
        "name": "person with pouting face tone1",
        "unicode": "1f64e-1f3fb"
    },
    ":person_with_pouting_face_tone2:": {
        "category": "people",
        "name": "person with pouting face tone2",
        "unicode": "1f64e-1f3fc"
    },
    ":person_with_pouting_face_tone3:": {
        "category": "people",
        "name": "person with pouting face tone3",
        "unicode": "1f64e-1f3fd"
    },
    ":person_with_pouting_face_tone4:": {
        "category": "people",
        "name": "person with pouting face tone4",
        "unicode": "1f64e-1f3fe"
    },
    ":person_with_pouting_face_tone5:": {
        "category": "people",
        "name": "person with pouting face tone5",
        "unicode": "1f64e-1f3ff"
    },
    ":pick:": {
        "category": "objects",
        "name": "pick",
        "unicode": "26cf",
        "unicode_alt": "26cf-fe0f"
    },
    ":pig2:": {
        "category": "nature",
        "name": "pig",
        "unicode": "1f416"
    },
    ":pig:": {
        "category": "nature",
        "name": "pig face",
        "unicode": "1f437"
    },
    ":pig_nose:": {
        "category": "nature",
        "name": "pig nose",
        "unicode": "1f43d"
    },
    ":pill:": {
        "category": "objects",
        "name": "pill",
        "unicode": "1f48a"
    },
    ":pineapple:": {
        "category": "food",
        "name": "pineapple",
        "unicode": "1f34d"
    },
    ":ping_pong:": {
        "category": "activity",
        "name": "table tennis paddle and ball",
        "unicode": "1f3d3"
    },
    ":pisces:": {
        "category": "symbols",
        "name": "pisces",
        "unicode": "2653",
        "unicode_alt": "2653-fe0f"
    },
    ":pizza:": {
        "category": "food",
        "name": "slice of pizza",
        "unicode": "1f355"
    },
    ":place_of_worship:": {
        "category": "symbols",
        "name": "place of worship",
        "unicode": "1f6d0"
    },
    ":play_pause:": {
        "category": "symbols",
        "name": "black right-pointing double triangle with double vertical bar",
        "unicode": "23ef",
        "unicode_alt": "23ef-fe0f"
    },
    ":point_down:": {
        "category": "people",
        "name": "white down pointing backhand index",
        "unicode": "1f447"
    },
    ":point_down_tone1:": {
        "category": "people",
        "name": "white down pointing backhand index tone 1",
        "unicode": "1f447-1f3fb"
    },
    ":point_down_tone2:": {
        "category": "people",
        "name": "white down pointing backhand index tone 2",
        "unicode": "1f447-1f3fc"
    },
    ":point_down_tone3:": {
        "category": "people",
        "name": "white down pointing backhand index tone 3",
        "unicode": "1f447-1f3fd"
    },
    ":point_down_tone4:": {
        "category": "people",
        "name": "white down pointing backhand index tone 4",
        "unicode": "1f447-1f3fe"
    },
    ":point_down_tone5:": {
        "category": "people",
        "name": "white down pointing backhand index tone 5",
        "unicode": "1f447-1f3ff"
    },
    ":point_left:": {
        "category": "people",
        "name": "white left pointing backhand index",
        "unicode": "1f448"
    },
    ":point_left_tone1:": {
        "category": "people",
        "name": "white left pointing backhand index tone 1",
        "unicode": "1f448-1f3fb"
    },
    ":point_left_tone2:": {
        "category": "people",
        "name": "white left pointing backhand index tone 2",
        "unicode": "1f448-1f3fc"
    },
    ":point_left_tone3:": {
        "category": "people",
        "name": "white left pointing backhand index tone 3",
        "unicode": "1f448-1f3fd"
    },
    ":point_left_tone4:": {
        "category": "people",
        "name": "white left pointing backhand index tone 4",
        "unicode": "1f448-1f3fe"
    },
    ":point_left_tone5:": {
        "category": "people",
        "name": "white left pointing backhand index tone 5",
        "unicode": "1f448-1f3ff"
    },
    ":point_right:": {
        "category": "people",
        "name": "white right pointing backhand index",
        "unicode": "1f449"
    },
    ":point_right_tone1:": {
        "category": "people",
        "name": "white right pointing backhand index tone 1",
        "unicode": "1f449-1f3fb"
    },
    ":point_right_tone2:": {
        "category": "people",
        "name": "white right pointing backhand index tone 2",
        "unicode": "1f449-1f3fc"
    },
    ":point_right_tone3:": {
        "category": "people",
        "name": "white right pointing backhand index tone 3",
        "unicode": "1f449-1f3fd"
    },
    ":point_right_tone4:": {
        "category": "people",
        "name": "white right pointing backhand index tone 4",
        "unicode": "1f449-1f3fe"
    },
    ":point_right_tone5:": {
        "category": "people",
        "name": "white right pointing backhand index tone 5",
        "unicode": "1f449-1f3ff"
    },
    ":point_up:": {
        "category": "people",
        "name": "white up pointing index",
        "unicode": "261d",
        "unicode_alt": "261d-fe0f"
    },
    ":point_up_2:": {
        "category": "people",
        "name": "white up pointing backhand index",
        "unicode": "1f446"
    },
    ":point_up_2_tone1:": {
        "category": "people",
        "name": "white up pointing backhand index tone 1",
        "unicode": "1f446-1f3fb"
    },
    ":point_up_2_tone2:": {
        "category": "people",
        "name": "white up pointing backhand index tone 2",
        "unicode": "1f446-1f3fc"
    },
    ":point_up_2_tone3:": {
        "category": "people",
        "name": "white up pointing backhand index tone 3",
        "unicode": "1f446-1f3fd"
    },
    ":point_up_2_tone4:": {
        "category": "people",
        "name": "white up pointing backhand index tone 4",
        "unicode": "1f446-1f3fe"
    },
    ":point_up_2_tone5:": {
        "category": "people",
        "name": "white up pointing backhand index tone 5",
        "unicode": "1f446-1f3ff"
    },
    ":point_up_tone1:": {
        "category": "people",
        "name": "white up pointing index tone 1",
        "unicode": "261d-1f3fb"
    },
    ":point_up_tone2:": {
        "category": "people",
        "name": "white up pointing index tone 2",
        "unicode": "261d-1f3fc"
    },
    ":point_up_tone3:": {
        "category": "people",
        "name": "white up pointing index tone 3",
        "unicode": "261d-1f3fd"
    },
    ":point_up_tone4:": {
        "category": "people",
        "name": "white up pointing index tone 4",
        "unicode": "261d-1f3fe"
    },
    ":point_up_tone5:": {
        "category": "people",
        "name": "white up pointing index tone 5",
        "unicode": "261d-1f3ff"
    },
    ":police_car:": {
        "category": "travel",
        "name": "police car",
        "unicode": "1f693"
    },
    ":poodle:": {
        "category": "nature",
        "name": "poodle",
        "unicode": "1f429"
    },
    ":poop:": {
        "category": "people",
        "name": "pile of poo",
        "unicode": "1f4a9"
    },
    ":popcorn:": {
        "category": "food",
        "name": "popcorn",
        "unicode": "1f37f"
    },
    ":post_office:": {
        "category": "travel",
        "name": "japanese post office",
        "unicode": "1f3e3"
    },
    ":postal_horn:": {
        "category": "objects",
        "name": "postal horn",
        "unicode": "1f4ef"
    },
    ":postbox:": {
        "category": "objects",
        "name": "postbox",
        "unicode": "1f4ee"
    },
    ":potable_water:": {
        "category": "symbols",
        "name": "potable water symbol",
        "unicode": "1f6b0"
    },
    ":potato:": {
        "category": "food",
        "name": "potato",
        "unicode": "1f954"
    },
    ":pouch:": {
        "category": "people",
        "name": "pouch",
        "unicode": "1f45d"
    },
    ":poultry_leg:": {
        "category": "food",
        "name": "poultry leg",
        "unicode": "1f357"
    },
    ":pound:": {
        "category": "objects",
        "name": "banknote with pound sign",
        "unicode": "1f4b7"
    },
    ":pouting_cat:": {
        "category": "people",
        "name": "pouting cat face",
        "unicode": "1f63e"
    },
    ":pray:": {
        "category": "people",
        "name": "person with folded hands",
        "unicode": "1f64f"
    },
    ":pray_tone1:": {
        "category": "people",
        "name": "person with folded hands tone 1",
        "unicode": "1f64f-1f3fb"
    },
    ":pray_tone2:": {
        "category": "people",
        "name": "person with folded hands tone 2",
        "unicode": "1f64f-1f3fc"
    },
    ":pray_tone3:": {
        "category": "people",
        "name": "person with folded hands tone 3",
        "unicode": "1f64f-1f3fd"
    },
    ":pray_tone4:": {
        "category": "people",
        "name": "person with folded hands tone 4",
        "unicode": "1f64f-1f3fe"
    },
    ":pray_tone5:": {
        "category": "people",
        "name": "person with folded hands tone 5",
        "unicode": "1f64f-1f3ff"
    },
    ":prayer_beads:": {
        "category": "objects",
        "name": "prayer beads",
        "unicode": "1f4ff"
    },
    ":pregnant_woman:": {
        "category": "people",
        "name": "pregnant woman",
        "unicode": "1f930"
    },
    ":pregnant_woman_tone1:": {
        "category": "people",
        "name": "pregnant woman tone 1",
        "unicode": "1f930-1f3fb"
    },
    ":pregnant_woman_tone2:": {
        "category": "people",
        "name": "pregnant woman tone 2",
        "unicode": "1f930-1f3fc"
    },
    ":pregnant_woman_tone3:": {
        "category": "people",
        "name": "pregnant woman tone 3",
        "unicode": "1f930-1f3fd"
    },
    ":pregnant_woman_tone4:": {
        "category": "people",
        "name": "pregnant woman tone 4",
        "unicode": "1f930-1f3fe"
    },
    ":pregnant_woman_tone5:": {
        "category": "people",
        "name": "pregnant woman tone 5",
        "unicode": "1f930-1f3ff"
    },
    ":prince:": {
        "category": "people",
        "name": "prince",
        "unicode": "1f934"
    },
    ":prince_tone1:": {
        "category": "people",
        "name": "prince tone 1",
        "unicode": "1f934-1f3fb"
    },
    ":prince_tone2:": {
        "category": "people",
        "name": "prince tone 2",
        "unicode": "1f934-1f3fc"
    },
    ":prince_tone3:": {
        "category": "people",
        "name": "prince tone 3",
        "unicode": "1f934-1f3fd"
    },
    ":prince_tone4:": {
        "category": "people",
        "name": "prince tone 4",
        "unicode": "1f934-1f3fe"
    },
    ":prince_tone5:": {
        "category": "people",
        "name": "prince tone 5",
        "unicode": "1f934-1f3ff"
    },
    ":princess:": {
        "category": "people",
        "name": "princess",
        "unicode": "1f478"
    },
    ":princess_tone1:": {
        "category": "people",
        "name": "princess tone 1",
        "unicode": "1f478-1f3fb"
    },
    ":princess_tone2:": {
        "category": "people",
        "name": "princess tone 2",
        "unicode": "1f478-1f3fc"
    },
    ":princess_tone3:": {
        "category": "people",
        "name": "princess tone 3",
        "unicode": "1f478-1f3fd"
    },
    ":princess_tone4:": {
        "category": "people",
        "name": "princess tone 4",
        "unicode": "1f478-1f3fe"
    },
    ":princess_tone5:": {
        "category": "people",
        "name": "princess tone 5",
        "unicode": "1f478-1f3ff"
    },
    ":printer:": {
        "category": "objects",
        "name": "printer",
        "unicode": "1f5a8",
        "unicode_alt": "1f5a8-fe0f"
    },
    ":projector:": {
        "category": "objects",
        "name": "film projector",
        "unicode": "1f4fd",
        "unicode_alt": "1f4fd-fe0f"
    },
    ":punch:": {
        "category": "people",
        "name": "fisted hand sign",
        "unicode": "1f44a"
    },
    ":punch_tone1:": {
        "category": "people",
        "name": "fisted hand sign tone 1",
        "unicode": "1f44a-1f3fb"
    },
    ":punch_tone2:": {
        "category": "people",
        "name": "fisted hand sign tone 2",
        "unicode": "1f44a-1f3fc"
    },
    ":punch_tone3:": {
        "category": "people",
        "name": "fisted hand sign tone 3",
        "unicode": "1f44a-1f3fd"
    },
    ":punch_tone4:": {
        "category": "people",
        "name": "fisted hand sign tone 4",
        "unicode": "1f44a-1f3fe"
    },
    ":punch_tone5:": {
        "category": "people",
        "name": "fisted hand sign tone 5",
        "unicode": "1f44a-1f3ff"
    },
    ":purple_heart:": {
        "category": "symbols",
        "name": "purple heart",
        "unicode": "1f49c"
    },
    ":purse:": {
        "category": "people",
        "name": "purse",
        "unicode": "1f45b"
    },
    ":pushpin:": {
        "category": "objects",
        "name": "pushpin",
        "unicode": "1f4cc"
    },
    ":put_litter_in_its_place:": {
        "category": "symbols",
        "name": "put litter in its place symbol",
        "unicode": "1f6ae"
    },
    ":question:": {
        "category": "symbols",
        "name": "black question mark ornament",
        "unicode": "2753"
    },
    ":rabbit2:": {
        "category": "nature",
        "name": "rabbit",
        "unicode": "1f407"
    },
    ":rabbit:": {
        "category": "nature",
        "name": "rabbit face",
        "unicode": "1f430"
    },
    ":race_car:": {
        "category": "travel",
        "name": "racing car",
        "unicode": "1f3ce",
        "unicode_alt": "1f3ce-fe0f"
    },
    ":racehorse:": {
        "category": "nature",
        "name": "horse",
        "unicode": "1f40e"
    },
    ":radio:": {
        "category": "objects",
        "name": "radio",
        "unicode": "1f4fb"
    },
    ":radio_button:": {
        "category": "symbols",
        "name": "radio button",
        "unicode": "1f518"
    },
    ":radioactive:": {
        "category": "symbols",
        "name": "radioactive sign",
        "unicode": "2622",
        "unicode_alt": "2622-fe0f"
    },
    ":rage:": {
        "category": "people",
        "name": "pouting face",
        "unicode": "1f621"
    },
    ":railway_car:": {
        "category": "travel",
        "name": "railway car",
        "unicode": "1f683"
    },
    ":railway_track:": {
        "category": "travel",
        "name": "railway track",
        "unicode": "1f6e4",
        "unicode_alt": "1f6e4-fe0f"
    },
    ":rainbow:": {
        "category": "travel",
        "name": "rainbow",
        "unicode": "1f308"
    },
    ":rainbow_flag:": {
        "category": "objects",
        "name": "rainbow_flag",
        "unicode": "1f3f3-1f308"
    },
    ":raised_back_of_hand:": {
        "category": "people",
        "name": "raised back of hand",
        "unicode": "1f91a"
    },
    ":raised_back_of_hand_tone1:": {
        "category": "people",
        "name": "raised back of hand tone 1",
        "unicode": "1f91a-1f3fb"
    },
    ":raised_back_of_hand_tone2:": {
        "category": "people",
        "name": "raised back of hand tone 2",
        "unicode": "1f91a-1f3fc"
    },
    ":raised_back_of_hand_tone3:": {
        "category": "people",
        "name": "raised back of hand tone 3",
        "unicode": "1f91a-1f3fd"
    },
    ":raised_back_of_hand_tone4:": {
        "category": "people",
        "name": "raised back of hand tone 4",
        "unicode": "1f91a-1f3fe"
    },
    ":raised_back_of_hand_tone5:": {
        "category": "people",
        "name": "raised back of hand tone 5",
        "unicode": "1f91a-1f3ff"
    },
    ":raised_hand:": {
        "category": "people",
        "name": "raised hand",
        "unicode": "270b"
    },
    ":raised_hand_tone1:": {
        "category": "people",
        "name": "raised hand tone 1",
        "unicode": "270b-1f3fb"
    },
    ":raised_hand_tone2:": {
        "category": "people",
        "name": "raised hand tone 2",
        "unicode": "270b-1f3fc"
    },
    ":raised_hand_tone3:": {
        "category": "people",
        "name": "raised hand tone 3",
        "unicode": "270b-1f3fd"
    },
    ":raised_hand_tone4:": {
        "category": "people",
        "name": "raised hand tone 4",
        "unicode": "270b-1f3fe"
    },
    ":raised_hand_tone5:": {
        "category": "people",
        "name": "raised hand tone 5",
        "unicode": "270b-1f3ff"
    },
    ":raised_hands:": {
        "category": "people",
        "name": "person raising both hands in celebration",
        "unicode": "1f64c"
    },
    ":raised_hands_tone1:": {
        "category": "people",
        "name": "person raising both hands in celebration tone 1",
        "unicode": "1f64c-1f3fb"
    },
    ":raised_hands_tone2:": {
        "category": "people",
        "name": "person raising both hands in celebration tone 2",
        "unicode": "1f64c-1f3fc"
    },
    ":raised_hands_tone3:": {
        "category": "people",
        "name": "person raising both hands in celebration tone 3",
        "unicode": "1f64c-1f3fd"
    },
    ":raised_hands_tone4:": {
        "category": "people",
        "name": "person raising both hands in celebration tone 4",
        "unicode": "1f64c-1f3fe"
    },
    ":raised_hands_tone5:": {
        "category": "people",
        "name": "person raising both hands in celebration tone 5",
        "unicode": "1f64c-1f3ff"
    },
    ":raising_hand:": {
        "category": "people",
        "name": "happy person raising one hand",
        "unicode": "1f64b"
    },
    ":raising_hand_tone1:": {
        "category": "people",
        "name": "happy person raising one hand tone1",
        "unicode": "1f64b-1f3fb"
    },
    ":raising_hand_tone2:": {
        "category": "people",
        "name": "happy person raising one hand tone2",
        "unicode": "1f64b-1f3fc"
    },
    ":raising_hand_tone3:": {
        "category": "people",
        "name": "happy person raising one hand tone3",
        "unicode": "1f64b-1f3fd"
    },
    ":raising_hand_tone4:": {
        "category": "people",
        "name": "happy person raising one hand tone4",
        "unicode": "1f64b-1f3fe"
    },
    ":raising_hand_tone5:": {
        "category": "people",
        "name": "happy person raising one hand tone5",
        "unicode": "1f64b-1f3ff"
    },
    ":ram:": {
        "category": "nature",
        "name": "ram",
        "unicode": "1f40f"
    },
    ":ramen:": {
        "category": "food",
        "name": "steaming bowl",
        "unicode": "1f35c"
    },
    ":rat:": {
        "category": "nature",
        "name": "rat",
        "unicode": "1f400"
    },
    ":record_button:": {
        "category": "symbols",
        "name": "black circle for record",
        "unicode": "23fa",
        "unicode_alt": "23fa-fe0f"
    },
    ":recycle:": {
        "category": "symbols",
        "name": "black universal recycling symbol",
        "unicode": "267b",
        "unicode_alt": "267b-fe0f"
    },
    ":red_car:": {
        "category": "travel",
        "name": "automobile",
        "unicode": "1f697"
    },
    ":red_circle:": {
        "category": "symbols",
        "name": "red circle",
        "unicode": "1f534"
    },
    ":regional_indicator_a:": {
        "category": "regional",
        "name": "regional indicator symbol letter a",
        "unicode": "1f1e6"
    },
    ":regional_indicator_b:": {
        "category": "regional",
        "name": "regional indicator symbol letter b",
        "unicode": "1f1e7"
    },
    ":regional_indicator_c:": {
        "category": "regional",
        "name": "regional indicator symbol letter c",
        "unicode": "1f1e8"
    },
    ":regional_indicator_d:": {
        "category": "regional",
        "name": "regional indicator symbol letter d",
        "unicode": "1f1e9"
    },
    ":regional_indicator_e:": {
        "category": "regional",
        "name": "regional indicator symbol letter e",
        "unicode": "1f1ea"
    },
    ":regional_indicator_f:": {
        "category": "regional",
        "name": "regional indicator symbol letter f",
        "unicode": "1f1eb"
    },
    ":regional_indicator_g:": {
        "category": "regional",
        "name": "regional indicator symbol letter g",
        "unicode": "1f1ec"
    },
    ":regional_indicator_h:": {
        "category": "regional",
        "name": "regional indicator symbol letter h",
        "unicode": "1f1ed"
    },
    ":regional_indicator_i:": {
        "category": "regional",
        "name": "regional indicator symbol letter i",
        "unicode": "1f1ee"
    },
    ":regional_indicator_j:": {
        "category": "regional",
        "name": "regional indicator symbol letter j",
        "unicode": "1f1ef"
    },
    ":regional_indicator_k:": {
        "category": "regional",
        "name": "regional indicator symbol letter k",
        "unicode": "1f1f0"
    },
    ":regional_indicator_l:": {
        "category": "regional",
        "name": "regional indicator symbol letter l",
        "unicode": "1f1f1"
    },
    ":regional_indicator_m:": {
        "category": "regional",
        "name": "regional indicator symbol letter m",
        "unicode": "1f1f2"
    },
    ":regional_indicator_n:": {
        "category": "regional",
        "name": "regional indicator symbol letter n",
        "unicode": "1f1f3"
    },
    ":regional_indicator_o:": {
        "category": "regional",
        "name": "regional indicator symbol letter o",
        "unicode": "1f1f4"
    },
    ":regional_indicator_p:": {
        "category": "regional",
        "name": "regional indicator symbol letter p",
        "unicode": "1f1f5"
    },
    ":regional_indicator_q:": {
        "category": "regional",
        "name": "regional indicator symbol letter q",
        "unicode": "1f1f6"
    },
    ":regional_indicator_r:": {
        "category": "regional",
        "name": "regional indicator symbol letter r",
        "unicode": "1f1f7"
    },
    ":regional_indicator_s:": {
        "category": "regional",
        "name": "regional indicator symbol letter s",
        "unicode": "1f1f8"
    },
    ":regional_indicator_t:": {
        "category": "regional",
        "name": "regional indicator symbol letter t",
        "unicode": "1f1f9"
    },
    ":regional_indicator_u:": {
        "category": "regional",
        "name": "regional indicator symbol letter u",
        "unicode": "1f1fa"
    },
    ":regional_indicator_v:": {
        "category": "regional",
        "name": "regional indicator symbol letter v",
        "unicode": "1f1fb"
    },
    ":regional_indicator_w:": {
        "category": "regional",
        "name": "regional indicator symbol letter w",
        "unicode": "1f1fc"
    },
    ":regional_indicator_x:": {
        "category": "regional",
        "name": "regional indicator symbol letter x",
        "unicode": "1f1fd"
    },
    ":regional_indicator_y:": {
        "category": "regional",
        "name": "regional indicator symbol letter y",
        "unicode": "1f1fe"
    },
    ":regional_indicator_z:": {
        "category": "regional",
        "name": "regional indicator symbol letter z",
        "unicode": "1f1ff"
    },
    ":registered:": {
        "category": "symbols",
        "name": "registered sign",
        "unicode": "00ae",
        "unicode_alt": "00ae-fe0f"
    },
    ":relaxed:": {
        "category": "people",
        "name": "white smiling face",
        "unicode": "263a",
        "unicode_alt": "263a-fe0f"
    },
    ":relieved:": {
        "category": "people",
        "name": "relieved face",
        "unicode": "1f60c"
    },
    ":reminder_ribbon:": {
        "category": "activity",
        "name": "reminder ribbon",
        "unicode": "1f397",
        "unicode_alt": "1f397-fe0f"
    },
    ":repeat:": {
        "category": "symbols",
        "name": "clockwise rightwards and leftwards open circle arrows",
        "unicode": "1f501"
    },
    ":repeat_one:": {
        "category": "symbols",
        "name": "clockwise rightwards and leftwards open circle arrows with circled one overlay",
        "unicode": "1f502"
    },
    ":restroom:": {
        "category": "symbols",
        "name": "restroom",
        "unicode": "1f6bb"
    },
    ":revolving_hearts:": {
        "category": "symbols",
        "name": "revolving hearts",
        "unicode": "1f49e"
    },
    ":rewind:": {
        "category": "symbols",
        "name": "black left-pointing double triangle",
        "unicode": "23ea"
    },
    ":rhino:": {
        "category": "nature",
        "name": "rhinoceros",
        "unicode": "1f98f"
    },
    ":ribbon:": {
        "category": "objects",
        "name": "ribbon",
        "unicode": "1f380"
    },
    ":rice:": {
        "category": "food",
        "name": "cooked rice",
        "unicode": "1f35a"
    },
    ":rice_ball:": {
        "category": "food",
        "name": "rice ball",
        "unicode": "1f359"
    },
    ":rice_cracker:": {
        "category": "food",
        "name": "rice cracker",
        "unicode": "1f358"
    },
    ":rice_scene:": {
        "category": "travel",
        "name": "moon viewing ceremony",
        "unicode": "1f391"
    },
    ":right_facing_fist:": {
        "category": "people",
        "name": "right-facing fist",
        "unicode": "1f91c"
    },
    ":right_facing_fist_tone1:": {
        "category": "people",
        "name": "right facing fist tone 1",
        "unicode": "1f91c-1f3fb"
    },
    ":right_facing_fist_tone2:": {
        "category": "people",
        "name": "right facing fist tone 2",
        "unicode": "1f91c-1f3fc"
    },
    ":right_facing_fist_tone3:": {
        "category": "people",
        "name": "right facing fist tone 3",
        "unicode": "1f91c-1f3fd"
    },
    ":right_facing_fist_tone4:": {
        "category": "people",
        "name": "right facing fist tone 4",
        "unicode": "1f91c-1f3fe"
    },
    ":right_facing_fist_tone5:": {
        "category": "people",
        "name": "right facing fist tone 5",
        "unicode": "1f91c-1f3ff"
    },
    ":ring:": {
        "category": "people",
        "name": "ring",
        "unicode": "1f48d"
    },
    ":robot:": {
        "category": "people",
        "name": "robot face",
        "unicode": "1f916"
    },
    ":rocket:": {
        "category": "travel",
        "name": "rocket",
        "unicode": "1f680"
    },
    ":rofl:": {
        "category": "people",
        "name": "rolling on the floor laughing",
        "unicode": "1f923"
    },
    ":roller_coaster:": {
        "category": "travel",
        "name": "roller coaster",
        "unicode": "1f3a2"
    },
    ":rolling_eyes:": {
        "category": "people",
        "name": "face with rolling eyes",
        "unicode": "1f644"
    },
    ":rooster:": {
        "category": "nature",
        "name": "rooster",
        "unicode": "1f413"
    },
    ":rose:": {
        "category": "nature",
        "name": "rose",
        "unicode": "1f339"
    },
    ":rosette:": {
        "category": "nature",
        "name": "rosette",
        "unicode": "1f3f5",
        "unicode_alt": "1f3f5-fe0f"
    },
    ":rotating_light:": {
        "category": "travel",
        "name": "police cars revolving light",
        "unicode": "1f6a8"
    },
    ":round_pushpin:": {
        "category": "objects",
        "name": "round pushpin",
        "unicode": "1f4cd"
    },
    ":rowboat:": {
        "category": "activity",
        "name": "rowboat",
        "unicode": "1f6a3"
    },
    ":rowboat_tone1:": {
        "category": "activity",
        "name": "rowboat tone 1",
        "unicode": "1f6a3-1f3fb"
    },
    ":rowboat_tone2:": {
        "category": "activity",
        "name": "rowboat tone 2",
        "unicode": "1f6a3-1f3fc"
    },
    ":rowboat_tone3:": {
        "category": "activity",
        "name": "rowboat tone 3",
        "unicode": "1f6a3-1f3fd"
    },
    ":rowboat_tone4:": {
        "category": "activity",
        "name": "rowboat tone 4",
        "unicode": "1f6a3-1f3fe"
    },
    ":rowboat_tone5:": {
        "category": "activity",
        "name": "rowboat tone 5",
        "unicode": "1f6a3-1f3ff"
    },
    ":rugby_football:": {
        "category": "activity",
        "name": "rugby football",
        "unicode": "1f3c9"
    },
    ":runner:": {
        "category": "people",
        "name": "runner",
        "unicode": "1f3c3"
    },
    ":runner_tone1:": {
        "category": "people",
        "name": "runner tone 1",
        "unicode": "1f3c3-1f3fb"
    },
    ":runner_tone2:": {
        "category": "people",
        "name": "runner tone 2",
        "unicode": "1f3c3-1f3fc"
    },
    ":runner_tone3:": {
        "category": "people",
        "name": "runner tone 3",
        "unicode": "1f3c3-1f3fd"
    },
    ":runner_tone4:": {
        "category": "people",
        "name": "runner tone 4",
        "unicode": "1f3c3-1f3fe"
    },
    ":runner_tone5:": {
        "category": "people",
        "name": "runner tone 5",
        "unicode": "1f3c3-1f3ff"
    },
    ":running_shirt_with_sash:": {
        "category": "activity",
        "name": "running shirt with sash",
        "unicode": "1f3bd"
    },
    ":sa:": {
        "category": "symbols",
        "name": "squared katakana sa",
        "unicode": "1f202",
        "unicode_alt": "1f202-fe0f"
    },
    ":sagittarius:": {
        "category": "symbols",
        "name": "sagittarius",
        "unicode": "2650",
        "unicode_alt": "2650-fe0f"
    },
    ":sailboat:": {
        "category": "travel",
        "name": "sailboat",
        "unicode": "26f5",
        "unicode_alt": "26f5-fe0f"
    },
    ":sake:": {
        "category": "food",
        "name": "sake bottle and cup",
        "unicode": "1f376"
    },
    ":salad:": {
        "category": "food",
        "name": "green salad",
        "unicode": "1f957"
    },
    ":sandal:": {
        "category": "people",
        "name": "womans sandal",
        "unicode": "1f461"
    },
    ":santa:": {
        "category": "people",
        "name": "father christmas",
        "unicode": "1f385"
    },
    ":santa_tone1:": {
        "category": "people",
        "name": "father christmas tone 1",
        "unicode": "1f385-1f3fb"
    },
    ":santa_tone2:": {
        "category": "people",
        "name": "father christmas tone 2",
        "unicode": "1f385-1f3fc"
    },
    ":santa_tone3:": {
        "category": "people",
        "name": "father christmas tone 3",
        "unicode": "1f385-1f3fd"
    },
    ":santa_tone4:": {
        "category": "people",
        "name": "father christmas tone 4",
        "unicode": "1f385-1f3fe"
    },
    ":santa_tone5:": {
        "category": "people",
        "name": "father christmas tone 5",
        "unicode": "1f385-1f3ff"
    },
    ":satellite:": {
        "category": "objects",
        "name": "satellite antenna",
        "unicode": "1f4e1"
    },
    ":satellite_orbital:": {
        "category": "travel",
        "name": "satellite",
        "unicode": "1f6f0",
        "unicode_alt": "1f6f0-fe0f"
    },
    ":saxophone:": {
        "category": "activity",
        "name": "saxophone",
        "unicode": "1f3b7"
    },
    ":scales:": {
        "category": "objects",
        "name": "scales",
        "unicode": "2696",
        "unicode_alt": "2696-fe0f"
    },
    ":school:": {
        "category": "travel",
        "name": "school",
        "unicode": "1f3eb"
    },
    ":school_satchel:": {
        "category": "people",
        "name": "school satchel",
        "unicode": "1f392"
    },
    ":scissors:": {
        "category": "objects",
        "name": "black scissors",
        "unicode": "2702",
        "unicode_alt": "2702-fe0f"
    },
    ":scooter:": {
        "category": "travel",
        "name": "scooter",
        "unicode": "1f6f4"
    },
    ":scorpion:": {
        "category": "nature",
        "name": "scorpion",
        "unicode": "1f982"
    },
    ":scorpius:": {
        "category": "symbols",
        "name": "scorpius",
        "unicode": "264f",
        "unicode_alt": "264f-fe0f"
    },
    ":scream:": {
        "category": "people",
        "name": "face screaming in fear",
        "unicode": "1f631"
    },
    ":scream_cat:": {
        "category": "people",
        "name": "weary cat face",
        "unicode": "1f640"
    },
    ":scroll:": {
        "category": "objects",
        "name": "scroll",
        "unicode": "1f4dc"
    },
    ":seat:": {
        "category": "travel",
        "name": "seat",
        "unicode": "1f4ba"
    },
    ":second_place:": {
        "category": "activity",
        "name": "second place medal",
        "unicode": "1f948"
    },
    ":secret:": {
        "category": "symbols",
        "name": "circled ideograph secret",
        "unicode": "3299",
        "unicode_alt": "3299-fe0f"
    },
    ":see_no_evil:": {
        "category": "nature",
        "name": "see-no-evil monkey",
        "unicode": "1f648"
    },
    ":seedling:": {
        "category": "nature",
        "name": "seedling",
        "unicode": "1f331"
    },
    ":selfie:": {
        "category": "people",
        "name": "selfie",
        "unicode": "1f933"
    },
    ":selfie_tone1:": {
        "category": "people",
        "name": "selfie tone 1",
        "unicode": "1f933-1f3fb"
    },
    ":selfie_tone2:": {
        "category": "people",
        "name": "selfie tone 2",
        "unicode": "1f933-1f3fc"
    },
    ":selfie_tone3:": {
        "category": "people",
        "name": "selfie tone 3",
        "unicode": "1f933-1f3fd"
    },
    ":selfie_tone4:": {
        "category": "people",
        "name": "selfie tone 4",
        "unicode": "1f933-1f3fe"
    },
    ":selfie_tone5:": {
        "category": "people",
        "name": "selfie tone 5",
        "unicode": "1f933-1f3ff"
    },
    ":seven:": {
        "category": "symbols",
        "name": "keycap digit seven",
        "unicode": "0037-20e3",
        "unicode_alt": "0037-fe0f-20e3"
    },
    ":shallow_pan_of_food:": {
        "category": "food",
        "name": "shallow pan of food",
        "unicode": "1f958"
    },
    ":shamrock:": {
        "category": "nature",
        "name": "shamrock",
        "unicode": "2618",
        "unicode_alt": "2618-fe0f"
    },
    ":shark:": {
        "category": "nature",
        "name": "shark",
        "unicode": "1f988"
    },
    ":shaved_ice:": {
        "category": "food",
        "name": "shaved ice",
        "unicode": "1f367"
    },
    ":sheep:": {
        "category": "nature",
        "name": "sheep",
        "unicode": "1f411"
    },
    ":shell:": {
        "category": "nature",
        "name": "spiral shell",
        "unicode": "1f41a"
    },
    ":shield:": {
        "category": "objects",
        "name": "shield",
        "unicode": "1f6e1",
        "unicode_alt": "1f6e1-fe0f"
    },
    ":shinto_shrine:": {
        "category": "travel",
        "name": "shinto shrine",
        "unicode": "26e9",
        "unicode_alt": "26e9-fe0f"
    },
    ":ship:": {
        "category": "travel",
        "name": "ship",
        "unicode": "1f6a2"
    },
    ":shirt:": {
        "category": "people",
        "name": "t-shirt",
        "unicode": "1f455"
    },
    ":shopping_bags:": {
        "category": "objects",
        "name": "shopping bags",
        "unicode": "1f6cd",
        "unicode_alt": "1f6cd-fe0f"
    },
    ":shopping_cart:": {
        "category": "objects",
        "name": "shopping trolley",
        "unicode": "1f6d2"
    },
    ":shower:": {
        "category": "objects",
        "name": "shower",
        "unicode": "1f6bf"
    },
    ":shrimp:": {
        "category": "nature",
        "name": "shrimp",
        "unicode": "1f990"
    },
    ":shrug:": {
        "category": "people",
        "name": "shrug",
        "unicode": "1f937"
    },
    ":shrug_tone1:": {
        "category": "people",
        "name": "shrug tone 1",
        "unicode": "1f937-1f3fb"
    },
    ":shrug_tone2:": {
        "category": "people",
        "name": "shrug tone 2",
        "unicode": "1f937-1f3fc"
    },
    ":shrug_tone3:": {
        "category": "people",
        "name": "shrug tone 3",
        "unicode": "1f937-1f3fd"
    },
    ":shrug_tone4:": {
        "category": "people",
        "name": "shrug tone 4",
        "unicode": "1f937-1f3fe"
    },
    ":shrug_tone5:": {
        "category": "people",
        "name": "shrug tone 5",
        "unicode": "1f937-1f3ff"
    },
    ":signal_strength:": {
        "category": "symbols",
        "name": "antenna with bars",
        "unicode": "1f4f6"
    },
    ":six:": {
        "category": "symbols",
        "name": "keycap digit six",
        "unicode": "0036-20e3",
        "unicode_alt": "0036-fe0f-20e3"
    },
    ":six_pointed_star:": {
        "category": "symbols",
        "name": "six pointed star with middle dot",
        "unicode": "1f52f"
    },
    ":ski:": {
        "category": "activity",
        "name": "ski and ski boot",
        "unicode": "1f3bf"
    },
    ":skier:": {
        "category": "activity",
        "name": "skier",
        "unicode": "26f7",
        "unicode_alt": "26f7-fe0f"
    },
    ":skull:": {
        "category": "people",
        "name": "skull",
        "unicode": "1f480"
    },
    ":skull_crossbones:": {
        "category": "objects",
        "name": "skull and crossbones",
        "unicode": "2620",
        "unicode_alt": "2620-fe0f"
    },
    ":sleeping:": {
        "category": "people",
        "name": "sleeping face",
        "unicode": "1f634"
    },
    ":sleeping_accommodation:": {
        "category": "objects",
        "name": "sleeping accommodation",
        "unicode": "1f6cc"
    },
    ":sleepy:": {
        "category": "people",
        "name": "sleepy face",
        "unicode": "1f62a"
    },
    ":slight_frown:": {
        "category": "people",
        "name": "slightly frowning face",
        "unicode": "1f641"
    },
    ":slight_smile:": {
        "category": "people",
        "name": "slightly smiling face",
        "unicode": "1f642"
    },
    ":slot_machine:": {
        "category": "activity",
        "name": "slot machine",
        "unicode": "1f3b0"
    },
    ":small_blue_diamond:": {
        "category": "symbols",
        "name": "small blue diamond",
        "unicode": "1f539"
    },
    ":small_orange_diamond:": {
        "category": "symbols",
        "name": "small orange diamond",
        "unicode": "1f538"
    },
    ":small_red_triangle:": {
        "category": "symbols",
        "name": "up-pointing red triangle",
        "unicode": "1f53a"
    },
    ":small_red_triangle_down:": {
        "category": "symbols",
        "name": "down-pointing red triangle",
        "unicode": "1f53b"
    },
    ":smile:": {
        "category": "people",
        "name": "smiling face with open mouth and smiling eyes",
        "unicode": "1f604"
    },
    ":smile_cat:": {
        "category": "people",
        "name": "grinning cat face with smiling eyes",
        "unicode": "1f638"
    },
    ":smiley:": {
        "category": "people",
        "name": "smiling face with open mouth",
        "unicode": "1f603"
    },
    ":smiley_cat:": {
        "category": "people",
        "name": "smiling cat face with open mouth",
        "unicode": "1f63a"
    },
    ":smiling_imp:": {
        "category": "people",
        "name": "smiling face with horns",
        "unicode": "1f608"
    },
    ":smirk:": {
        "category": "people",
        "name": "smirking face",
        "unicode": "1f60f"
    },
    ":smirk_cat:": {
        "category": "people",
        "name": "cat face with wry smile",
        "unicode": "1f63c"
    },
    ":smoking:": {
        "category": "objects",
        "name": "smoking symbol",
        "unicode": "1f6ac"
    },
    ":snail:": {
        "category": "nature",
        "name": "snail",
        "unicode": "1f40c"
    },
    ":snake:": {
        "category": "nature",
        "name": "snake",
        "unicode": "1f40d"
    },
    ":sneezing_face:": {
        "category": "people",
        "name": "sneezing face",
        "unicode": "1f927"
    },
    ":snowboarder:": {
        "category": "activity",
        "name": "snowboarder",
        "unicode": "1f3c2"
    },
    ":snowflake:": {
        "category": "nature",
        "name": "snowflake",
        "unicode": "2744",
        "unicode_alt": "2744-fe0f"
    },
    ":snowman2:": {
        "category": "nature",
        "name": "snowman",
        "unicode": "2603",
        "unicode_alt": "2603-fe0f"
    },
    ":snowman:": {
        "category": "nature",
        "name": "snowman without snow",
        "unicode": "26c4",
        "unicode_alt": "26c4-fe0f"
    },
    ":sob:": {
        "category": "people",
        "name": "loudly crying face",
        "unicode": "1f62d"
    },
    ":soccer:": {
        "category": "activity",
        "name": "soccer ball",
        "unicode": "26bd",
        "unicode_alt": "26bd-fe0f"
    },
    ":soon:": {
        "category": "symbols",
        "name": "soon with rightwards arrow above",
        "unicode": "1f51c"
    },
    ":sos:": {
        "category": "symbols",
        "name": "squared sos",
        "unicode": "1f198"
    },
    ":sound:": {
        "category": "symbols",
        "name": "speaker with one sound wave",
        "unicode": "1f509"
    },
    ":space_invader:": {
        "category": "activity",
        "name": "alien monster",
        "unicode": "1f47e"
    },
    ":spades:": {
        "category": "symbols",
        "name": "black spade suit",
        "unicode": "2660",
        "unicode_alt": "2660-fe0f"
    },
    ":spaghetti:": {
        "category": "food",
        "name": "spaghetti",
        "unicode": "1f35d"
    },
    ":sparkle:": {
        "category": "symbols",
        "name": "sparkle",
        "unicode": "2747",
        "unicode_alt": "2747-fe0f"
    },
    ":sparkler:": {
        "category": "travel",
        "name": "firework sparkler",
        "unicode": "1f387"
    },
    ":sparkles:": {
        "category": "nature",
        "name": "sparkles",
        "unicode": "2728"
    },
    ":sparkling_heart:": {
        "category": "symbols",
        "name": "sparkling heart",
        "unicode": "1f496"
    },
    ":speak_no_evil:": {
        "category": "nature",
        "name": "speak-no-evil monkey",
        "unicode": "1f64a"
    },
    ":speaker:": {
        "category": "symbols",
        "name": "speaker",
        "unicode": "1f508"
    },
    ":speaking_head:": {
        "category": "people",
        "name": "speaking head in silhouette",
        "unicode": "1f5e3",
        "unicode_alt": "1f5e3-fe0f"
    },
    ":speech_balloon:": {
        "category": "symbols",
        "name": "speech balloon",
        "unicode": "1f4ac"
    },
    ":speech_left:": {
        "category": "symbols",
        "name": "left speech bubble",
        "unicode": "1f5e8",
        "unicode_alt": "1f5e8-fe0f"
    },
    ":speedboat:": {
        "category": "travel",
        "name": "speedboat",
        "unicode": "1f6a4"
    },
    ":spider:": {
        "category": "nature",
        "name": "spider",
        "unicode": "1f577",
        "unicode_alt": "1f577-fe0f"
    },
    ":spider_web:": {
        "category": "nature",
        "name": "spider web",
        "unicode": "1f578",
        "unicode_alt": "1f578-fe0f"
    },
    ":spoon:": {
        "category": "food",
        "name": "spoon",
        "unicode": "1f944"
    },
    ":spy:": {
        "category": "people",
        "name": "sleuth or spy",
        "unicode": "1f575",
        "unicode_alt": "1f575-fe0f"
    },
    ":spy_tone1:": {
        "category": "people",
        "name": "sleuth or spy tone 1",
        "unicode": "1f575-1f3fb"
    },
    ":spy_tone2:": {
        "category": "people",
        "name": "sleuth or spy tone 2",
        "unicode": "1f575-1f3fc"
    },
    ":spy_tone3:": {
        "category": "people",
        "name": "sleuth or spy tone 3",
        "unicode": "1f575-1f3fd"
    },
    ":spy_tone4:": {
        "category": "people",
        "name": "sleuth or spy tone 4",
        "unicode": "1f575-1f3fe"
    },
    ":spy_tone5:": {
        "category": "people",
        "name": "sleuth or spy tone 5",
        "unicode": "1f575-1f3ff"
    },
    ":squid:": {
        "category": "nature",
        "name": "squid",
        "unicode": "1f991"
    },
    ":stadium:": {
        "category": "travel",
        "name": "stadium",
        "unicode": "1f3df",
        "unicode_alt": "1f3df-fe0f"
    },
    ":star2:": {
        "category": "nature",
        "name": "glowing star",
        "unicode": "1f31f"
    },
    ":star:": {
        "category": "nature",
        "name": "white medium star",
        "unicode": "2b50",
        "unicode_alt": "2b50-fe0f"
    },
    ":star_and_crescent:": {
        "category": "symbols",
        "name": "star and crescent",
        "unicode": "262a",
        "unicode_alt": "262a-fe0f"
    },
    ":star_of_david:": {
        "category": "symbols",
        "name": "star of david",
        "unicode": "2721",
        "unicode_alt": "2721-fe0f"
    },
    ":stars:": {
        "category": "travel",
        "name": "shooting star",
        "unicode": "1f320"
    },
    ":station:": {
        "category": "travel",
        "name": "station",
        "unicode": "1f689"
    },
    ":statue_of_liberty:": {
        "category": "travel",
        "name": "statue of liberty",
        "unicode": "1f5fd"
    },
    ":steam_locomotive:": {
        "category": "travel",
        "name": "steam locomotive",
        "unicode": "1f682"
    },
    ":stew:": {
        "category": "food",
        "name": "pot of food",
        "unicode": "1f372"
    },
    ":stop_button:": {
        "category": "symbols",
        "name": "black square for stop",
        "unicode": "23f9",
        "unicode_alt": "23f9-fe0f"
    },
    ":stopwatch:": {
        "category": "objects",
        "name": "stopwatch",
        "unicode": "23f1",
        "unicode_alt": "23f1-fe0f"
    },
    ":straight_ruler:": {
        "category": "objects",
        "name": "straight ruler",
        "unicode": "1f4cf"
    },
    ":strawberry:": {
        "category": "food",
        "name": "strawberry",
        "unicode": "1f353"
    },
    ":stuck_out_tongue:": {
        "category": "people",
        "name": "face with stuck-out tongue",
        "unicode": "1f61b"
    },
    ":stuck_out_tongue_closed_eyes:": {
        "category": "people",
        "name": "face with stuck-out tongue and tightly-closed eyes",
        "unicode": "1f61d"
    },
    ":stuck_out_tongue_winking_eye:": {
        "category": "people",
        "name": "face with stuck-out tongue and winking eye",
        "unicode": "1f61c"
    },
    ":stuffed_flatbread:": {
        "category": "food",
        "name": "stuffed flatbread",
        "unicode": "1f959"
    },
    ":sun_with_face:": {
        "category": "nature",
        "name": "sun with face",
        "unicode": "1f31e"
    },
    ":sunflower:": {
        "category": "nature",
        "name": "sunflower",
        "unicode": "1f33b"
    },
    ":sunglasses:": {
        "category": "people",
        "name": "smiling face with sunglasses",
        "unicode": "1f60e"
    },
    ":sunny:": {
        "category": "nature",
        "name": "black sun with rays",
        "unicode": "2600",
        "unicode_alt": "2600-fe0f"
    },
    ":sunrise:": {
        "category": "travel",
        "name": "sunrise",
        "unicode": "1f305"
    },
    ":sunrise_over_mountains:": {
        "category": "travel",
        "name": "sunrise over mountains",
        "unicode": "1f304"
    },
    ":surfer:": {
        "category": "activity",
        "name": "surfer",
        "unicode": "1f3c4"
    },
    ":surfer_tone1:": {
        "category": "activity",
        "name": "surfer tone 1",
        "unicode": "1f3c4-1f3fb"
    },
    ":surfer_tone2:": {
        "category": "activity",
        "name": "surfer tone 2",
        "unicode": "1f3c4-1f3fc"
    },
    ":surfer_tone3:": {
        "category": "activity",
        "name": "surfer tone 3",
        "unicode": "1f3c4-1f3fd"
    },
    ":surfer_tone4:": {
        "category": "activity",
        "name": "surfer tone 4",
        "unicode": "1f3c4-1f3fe"
    },
    ":surfer_tone5:": {
        "category": "activity",
        "name": "surfer tone 5",
        "unicode": "1f3c4-1f3ff"
    },
    ":sushi:": {
        "category": "food",
        "name": "sushi",
        "unicode": "1f363"
    },
    ":suspension_railway:": {
        "category": "travel",
        "name": "suspension railway",
        "unicode": "1f69f"
    },
    ":sweat:": {
        "category": "people",
        "name": "face with cold sweat",
        "unicode": "1f613"
    },
    ":sweat_drops:": {
        "category": "nature",
        "name": "splashing sweat symbol",
        "unicode": "1f4a6"
    },
    ":sweat_smile:": {
        "category": "people",
        "name": "smiling face with open mouth and cold sweat",
        "unicode": "1f605"
    },
    ":sweet_potato:": {
        "category": "food",
        "name": "roasted sweet potato",
        "unicode": "1f360"
    },
    ":swimmer:": {
        "category": "activity",
        "name": "swimmer",
        "unicode": "1f3ca"
    },
    ":swimmer_tone1:": {
        "category": "activity",
        "name": "swimmer tone 1",
        "unicode": "1f3ca-1f3fb"
    },
    ":swimmer_tone2:": {
        "category": "activity",
        "name": "swimmer tone 2",
        "unicode": "1f3ca-1f3fc"
    },
    ":swimmer_tone3:": {
        "category": "activity",
        "name": "swimmer tone 3",
        "unicode": "1f3ca-1f3fd"
    },
    ":swimmer_tone4:": {
        "category": "activity",
        "name": "swimmer tone 4",
        "unicode": "1f3ca-1f3fe"
    },
    ":swimmer_tone5:": {
        "category": "activity",
        "name": "swimmer tone 5",
        "unicode": "1f3ca-1f3ff"
    },
    ":symbols:": {
        "category": "symbols",
        "name": "input symbol for symbols",
        "unicode": "1f523"
    },
    ":synagogue:": {
        "category": "travel",
        "name": "synagogue",
        "unicode": "1f54d"
    },
    ":syringe:": {
        "category": "objects",
        "name": "syringe",
        "unicode": "1f489"
    },
    ":taco:": {
        "category": "food",
        "name": "taco",
        "unicode": "1f32e"
    },
    ":tada:": {
        "category": "objects",
        "name": "party popper",
        "unicode": "1f389"
    },
    ":tanabata_tree:": {
        "category": "nature",
        "name": "tanabata tree",
        "unicode": "1f38b"
    },
    ":tangerine:": {
        "category": "food",
        "name": "tangerine",
        "unicode": "1f34a"
    },
    ":taurus:": {
        "category": "symbols",
        "name": "taurus",
        "unicode": "2649",
        "unicode_alt": "2649-fe0f"
    },
    ":taxi:": {
        "category": "travel",
        "name": "taxi",
        "unicode": "1f695"
    },
    ":tea:": {
        "category": "food",
        "name": "teacup without handle",
        "unicode": "1f375"
    },
    ":telephone:": {
        "category": "objects",
        "name": "black telephone",
        "unicode": "260e",
        "unicode_alt": "260e-fe0f"
    },
    ":telephone_receiver:": {
        "category": "objects",
        "name": "telephone receiver",
        "unicode": "1f4de"
    },
    ":telescope:": {
        "category": "objects",
        "name": "telescope",
        "unicode": "1f52d"
    },
    ":tennis:": {
        "category": "activity",
        "name": "tennis racquet and ball",
        "unicode": "1f3be"
    },
    ":tent:": {
        "category": "travel",
        "name": "tent",
        "unicode": "26fa",
        "unicode_alt": "26fa-fe0f"
    },
    ":thermometer:": {
        "category": "objects",
        "name": "thermometer",
        "unicode": "1f321",
        "unicode_alt": "1f321-fe0f"
    },
    ":thermometer_face:": {
        "category": "people",
        "name": "face with thermometer",
        "unicode": "1f912"
    },
    ":thinking:": {
        "category": "people",
        "name": "thinking face",
        "unicode": "1f914"
    },
    ":third_place:": {
        "category": "activity",
        "name": "third place medal",
        "unicode": "1f949"
    },
    ":thought_balloon:": {
        "category": "symbols",
        "name": "thought balloon",
        "unicode": "1f4ad"
    },
    ":three:": {
        "category": "symbols",
        "name": "keycap digit three",
        "unicode": "0033-20e3",
        "unicode_alt": "0033-fe0f-20e3"
    },
    ":thumbsdown:": {
        "category": "people",
        "name": "thumbs down sign",
        "unicode": "1f44e"
    },
    ":thumbsdown_tone1:": {
        "category": "people",
        "name": "thumbs down sign tone 1",
        "unicode": "1f44e-1f3fb"
    },
    ":thumbsdown_tone2:": {
        "category": "people",
        "name": "thumbs down sign tone 2",
        "unicode": "1f44e-1f3fc"
    },
    ":thumbsdown_tone3:": {
        "category": "people",
        "name": "thumbs down sign tone 3",
        "unicode": "1f44e-1f3fd"
    },
    ":thumbsdown_tone4:": {
        "category": "people",
        "name": "thumbs down sign tone 4",
        "unicode": "1f44e-1f3fe"
    },
    ":thumbsdown_tone5:": {
        "category": "people",
        "name": "thumbs down sign tone 5",
        "unicode": "1f44e-1f3ff"
    },
    ":thumbsup:": {
        "category": "people",
        "name": "thumbs up sign",
        "unicode": "1f44d"
    },
    ":thumbsup_tone1:": {
        "category": "people",
        "name": "thumbs up sign tone 1",
        "unicode": "1f44d-1f3fb"
    },
    ":thumbsup_tone2:": {
        "category": "people",
        "name": "thumbs up sign tone 2",
        "unicode": "1f44d-1f3fc"
    },
    ":thumbsup_tone3:": {
        "category": "people",
        "name": "thumbs up sign tone 3",
        "unicode": "1f44d-1f3fd"
    },
    ":thumbsup_tone4:": {
        "category": "people",
        "name": "thumbs up sign tone 4",
        "unicode": "1f44d-1f3fe"
    },
    ":thumbsup_tone5:": {
        "category": "people",
        "name": "thumbs up sign tone 5",
        "unicode": "1f44d-1f3ff"
    },
    ":thunder_cloud_rain:": {
        "category": "nature",
        "name": "thunder cloud and rain",
        "unicode": "26c8",
        "unicode_alt": "26c8-fe0f"
    },
    ":ticket:": {
        "category": "activity",
        "name": "ticket",
        "unicode": "1f3ab"
    },
    ":tickets:": {
        "category": "activity",
        "name": "admission tickets",
        "unicode": "1f39f",
        "unicode_alt": "1f39f-fe0f"
    },
    ":tiger2:": {
        "category": "nature",
        "name": "tiger",
        "unicode": "1f405"
    },
    ":tiger:": {
        "category": "nature",
        "name": "tiger face",
        "unicode": "1f42f"
    },
    ":timer:": {
        "category": "objects",
        "name": "timer clock",
        "unicode": "23f2",
        "unicode_alt": "23f2-fe0f"
    },
    ":tired_face:": {
        "category": "people",
        "name": "tired face",
        "unicode": "1f62b"
    },
    ":tm:": {
        "category": "symbols",
        "name": "trade mark sign",
        "unicode": "2122",
        "unicode_alt": "2122-fe0f"
    },
    ":toilet:": {
        "category": "objects",
        "name": "toilet",
        "unicode": "1f6bd"
    },
    ":tokyo_tower:": {
        "category": "travel",
        "name": "tokyo tower",
        "unicode": "1f5fc"
    },
    ":tomato:": {
        "category": "food",
        "name": "tomato",
        "unicode": "1f345"
    },
    ":tone1:": {
        "category": "modifier",
        "name": "emoji modifier Fitzpatrick type-1-2",
        "unicode": "1f3fb"
    },
    ":tone2:": {
        "category": "modifier",
        "name": "emoji modifier Fitzpatrick type-3",
        "unicode": "1f3fc"
    },
    ":tone3:": {
        "category": "modifier",
        "name": "emoji modifier Fitzpatrick type-4",
        "unicode": "1f3fd"
    },
    ":tone4:": {
        "category": "modifier",
        "name": "emoji modifier Fitzpatrick type-5",
        "unicode": "1f3fe"
    },
    ":tone5:": {
        "category": "modifier",
        "name": "emoji modifier Fitzpatrick type-6",
        "unicode": "1f3ff"
    },
    ":tongue:": {
        "category": "people",
        "name": "tongue",
        "unicode": "1f445"
    },
    ":tools:": {
        "category": "objects",
        "name": "hammer and wrench",
        "unicode": "1f6e0",
        "unicode_alt": "1f6e0-fe0f"
    },
    ":top:": {
        "category": "symbols",
        "name": "top with upwards arrow above",
        "unicode": "1f51d"
    },
    ":tophat:": {
        "category": "people",
        "name": "top hat",
        "unicode": "1f3a9"
    },
    ":track_next:": {
        "category": "symbols",
        "name": "black right-pointing double triangle with vertical bar",
        "unicode": "23ed",
        "unicode_alt": "23ed-fe0f"
    },
    ":track_previous:": {
        "category": "symbols",
        "name": "black left-pointing double triangle with vertical bar",
        "unicode": "23ee",
        "unicode_alt": "23ee-fe0f"
    },
    ":trackball:": {
        "category": "objects",
        "name": "trackball",
        "unicode": "1f5b2",
        "unicode_alt": "1f5b2-fe0f"
    },
    ":tractor:": {
        "category": "travel",
        "name": "tractor",
        "unicode": "1f69c"
    },
    ":traffic_light:": {
        "category": "travel",
        "name": "horizontal traffic light",
        "unicode": "1f6a5"
    },
    ":train2:": {
        "category": "travel",
        "name": "train",
        "unicode": "1f686"
    },
    ":train:": {
        "category": "travel",
        "name": "tram car",
        "unicode": "1f68b"
    },
    ":tram:": {
        "category": "travel",
        "name": "tram",
        "unicode": "1f68a"
    },
    ":triangular_flag_on_post:": {
        "category": "objects",
        "name": "triangular flag on post",
        "unicode": "1f6a9"
    },
    ":triangular_ruler:": {
        "category": "objects",
        "name": "triangular ruler",
        "unicode": "1f4d0"
    },
    ":trident:": {
        "category": "symbols",
        "name": "trident emblem",
        "unicode": "1f531"
    },
    ":triumph:": {
        "category": "people",
        "name": "face with look of triumph",
        "unicode": "1f624"
    },
    ":trolleybus:": {
        "category": "travel",
        "name": "trolleybus",
        "unicode": "1f68e"
    },
    ":trophy:": {
        "category": "activity",
        "name": "trophy",
        "unicode": "1f3c6"
    },
    ":tropical_drink:": {
        "category": "food",
        "name": "tropical drink",
        "unicode": "1f379"
    },
    ":tropical_fish:": {
        "category": "nature",
        "name": "tropical fish",
        "unicode": "1f420"
    },
    ":truck:": {
        "category": "travel",
        "name": "delivery truck",
        "unicode": "1f69a"
    },
    ":trumpet:": {
        "category": "activity",
        "name": "trumpet",
        "unicode": "1f3ba"
    },
    ":tulip:": {
        "category": "nature",
        "name": "tulip",
        "unicode": "1f337"
    },
    ":tumbler_glass:": {
        "category": "food",
        "name": "tumbler glass",
        "unicode": "1f943"
    },
    ":turkey:": {
        "category": "nature",
        "name": "turkey",
        "unicode": "1f983"
    },
    ":turtle:": {
        "category": "nature",
        "name": "turtle",
        "unicode": "1f422"
    },
    ":tv:": {
        "category": "objects",
        "name": "television",
        "unicode": "1f4fa"
    },
    ":twisted_rightwards_arrows:": {
        "category": "symbols",
        "name": "twisted rightwards arrows",
        "unicode": "1f500"
    },
    ":two:": {
        "category": "symbols",
        "name": "keycap digit two",
        "unicode": "0032-20e3",
        "unicode_alt": "0032-fe0f-20e3"
    },
    ":two_hearts:": {
        "category": "symbols",
        "name": "two hearts",
        "unicode": "1f495"
    },
    ":two_men_holding_hands:": {
        "category": "people",
        "name": "two men holding hands",
        "unicode": "1f46c"
    },
    ":two_women_holding_hands:": {
        "category": "people",
        "name": "two women holding hands",
        "unicode": "1f46d"
    },
    ":u5272:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-5272",
        "unicode": "1f239"
    },
    ":u5408:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-5408",
        "unicode": "1f234"
    },
    ":u55b6:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-55b6",
        "unicode": "1f23a"
    },
    ":u6307:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-6307",
        "unicode": "1f22f",
        "unicode_alt": "1f22f-fe0f"
    },
    ":u6708:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-6708",
        "unicode": "1f237",
        "unicode_alt": "1f237-fe0f"
    },
    ":u6709:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-6709",
        "unicode": "1f236"
    },
    ":u6e80:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-6e80",
        "unicode": "1f235"
    },
    ":u7121:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-7121",
        "unicode": "1f21a",
        "unicode_alt": "1f21a-fe0f"
    },
    ":u7533:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-7533",
        "unicode": "1f238"
    },
    ":u7981:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-7981",
        "unicode": "1f232"
    },
    ":u7a7a:": {
        "category": "symbols",
        "name": "squared cjk unified ideograph-7a7a",
        "unicode": "1f233"
    },
    ":umbrella2:": {
        "category": "nature",
        "name": "umbrella",
        "unicode": "2602",
        "unicode_alt": "2602-fe0f"
    },
    ":umbrella:": {
        "category": "nature",
        "name": "umbrella with rain drops",
        "unicode": "2614",
        "unicode_alt": "2614-fe0f"
    },
    ":unamused:": {
        "category": "people",
        "name": "unamused face",
        "unicode": "1f612"
    },
    ":underage:": {
        "category": "symbols",
        "name": "no one under eighteen symbol",
        "unicode": "1f51e"
    },
    ":unicorn:": {
        "category": "nature",
        "name": "unicorn face",
        "unicode": "1f984"
    },
    ":unlock:": {
        "category": "objects",
        "name": "open lock",
        "unicode": "1f513"
    },
    ":up:": {
        "category": "symbols",
        "name": "squared up with exclamation mark",
        "unicode": "1f199"
    },
    ":upside_down:": {
        "category": "people",
        "name": "upside-down face",
        "unicode": "1f643"
    },
    ":urn:": {
        "category": "objects",
        "name": "funeral urn",
        "unicode": "26b1",
        "unicode_alt": "26b1-fe0f"
    },
    ":v:": {
        "category": "people",
        "name": "victory hand",
        "unicode": "270c",
        "unicode_alt": "270c-fe0f"
    },
    ":v_tone1:": {
        "category": "people",
        "name": "victory hand tone 1",
        "unicode": "270c-1f3fb"
    },
    ":v_tone2:": {
        "category": "people",
        "name": "victory hand tone 2",
        "unicode": "270c-1f3fc"
    },
    ":v_tone3:": {
        "category": "people",
        "name": "victory hand tone 3",
        "unicode": "270c-1f3fd"
    },
    ":v_tone4:": {
        "category": "people",
        "name": "victory hand tone 4",
        "unicode": "270c-1f3fe"
    },
    ":v_tone5:": {
        "category": "people",
        "name": "victory hand tone 5",
        "unicode": "270c-1f3ff"
    },
    ":vertical_traffic_light:": {
        "category": "travel",
        "name": "vertical traffic light",
        "unicode": "1f6a6"
    },
    ":vhs:": {
        "category": "objects",
        "name": "videocassette",
        "unicode": "1f4fc"
    },
    ":vibration_mode:": {
        "category": "symbols",
        "name": "vibration mode",
        "unicode": "1f4f3"
    },
    ":video_camera:": {
        "category": "objects",
        "name": "video camera",
        "unicode": "1f4f9"
    },
    ":video_game:": {
        "category": "activity",
        "name": "video game",
        "unicode": "1f3ae"
    },
    ":violin:": {
        "category": "activity",
        "name": "violin",
        "unicode": "1f3bb"
    },
    ":virgo:": {
        "category": "symbols",
        "name": "virgo",
        "unicode": "264d",
        "unicode_alt": "264d-fe0f"
    },
    ":volcano:": {
        "category": "travel",
        "name": "volcano",
        "unicode": "1f30b"
    },
    ":volleyball:": {
        "category": "activity",
        "name": "volleyball",
        "unicode": "1f3d0"
    },
    ":vs:": {
        "category": "symbols",
        "name": "squared vs",
        "unicode": "1f19a"
    },
    ":vulcan:": {
        "category": "people",
        "name": "raised hand with part between middle and ring fingers",
        "unicode": "1f596"
    },
    ":vulcan_tone1:": {
        "category": "people",
        "name": "raised hand with part between middle and ring fingers tone 1",
        "unicode": "1f596-1f3fb"
    },
    ":vulcan_tone2:": {
        "category": "people",
        "name": "raised hand with part between middle and ring fingers tone 2",
        "unicode": "1f596-1f3fc"
    },
    ":vulcan_tone3:": {
        "category": "people",
        "name": "raised hand with part between middle and ring fingers tone 3",
        "unicode": "1f596-1f3fd"
    },
    ":vulcan_tone4:": {
        "category": "people",
        "name": "raised hand with part between middle and ring fingers tone 4",
        "unicode": "1f596-1f3fe"
    },
    ":vulcan_tone5:": {
        "category": "people",
        "name": "raised hand with part between middle and ring fingers tone 5",
        "unicode": "1f596-1f3ff"
    },
    ":walking:": {
        "category": "people",
        "name": "pedestrian",
        "unicode": "1f6b6"
    },
    ":walking_tone1:": {
        "category": "people",
        "name": "pedestrian tone 1",
        "unicode": "1f6b6-1f3fb"
    },
    ":walking_tone2:": {
        "category": "people",
        "name": "pedestrian tone 2",
        "unicode": "1f6b6-1f3fc"
    },
    ":walking_tone3:": {
        "category": "people",
        "name": "pedestrian tone 3",
        "unicode": "1f6b6-1f3fd"
    },
    ":walking_tone4:": {
        "category": "people",
        "name": "pedestrian tone 4",
        "unicode": "1f6b6-1f3fe"
    },
    ":walking_tone5:": {
        "category": "people",
        "name": "pedestrian tone 5",
        "unicode": "1f6b6-1f3ff"
    },
    ":waning_crescent_moon:": {
        "category": "nature",
        "name": "waning crescent moon symbol",
        "unicode": "1f318"
    },
    ":waning_gibbous_moon:": {
        "category": "nature",
        "name": "waning gibbous moon symbol",
        "unicode": "1f316"
    },
    ":warning:": {
        "category": "symbols",
        "name": "warning sign",
        "unicode": "26a0",
        "unicode_alt": "26a0-fe0f"
    },
    ":wastebasket:": {
        "category": "objects",
        "name": "wastebasket",
        "unicode": "1f5d1",
        "unicode_alt": "1f5d1-fe0f"
    },
    ":watch:": {
        "category": "objects",
        "name": "watch",
        "unicode": "231a",
        "unicode_alt": "231a-fe0f"
    },
    ":water_buffalo:": {
        "category": "nature",
        "name": "water buffalo",
        "unicode": "1f403"
    },
    ":water_polo:": {
        "category": "activity",
        "name": "water polo",
        "unicode": "1f93d"
    },
    ":water_polo_tone1:": {
        "category": "activity",
        "name": "water polo tone 1",
        "unicode": "1f93d-1f3fb"
    },
    ":water_polo_tone2:": {
        "category": "activity",
        "name": "water polo tone 2",
        "unicode": "1f93d-1f3fc"
    },
    ":water_polo_tone3:": {
        "category": "activity",
        "name": "water polo tone 3",
        "unicode": "1f93d-1f3fd"
    },
    ":water_polo_tone4:": {
        "category": "activity",
        "name": "water polo tone 4",
        "unicode": "1f93d-1f3fe"
    },
    ":water_polo_tone5:": {
        "category": "activity",
        "name": "water polo tone 5",
        "unicode": "1f93d-1f3ff"
    },
    ":watermelon:": {
        "category": "food",
        "name": "watermelon",
        "unicode": "1f349"
    },
    ":wave:": {
        "category": "people",
        "name": "waving hand sign",
        "unicode": "1f44b"
    },
    ":wave_tone1:": {
        "category": "people",
        "name": "waving hand sign tone 1",
        "unicode": "1f44b-1f3fb"
    },
    ":wave_tone2:": {
        "category": "people",
        "name": "waving hand sign tone 2",
        "unicode": "1f44b-1f3fc"
    },
    ":wave_tone3:": {
        "category": "people",
        "name": "waving hand sign tone 3",
        "unicode": "1f44b-1f3fd"
    },
    ":wave_tone4:": {
        "category": "people",
        "name": "waving hand sign tone 4",
        "unicode": "1f44b-1f3fe"
    },
    ":wave_tone5:": {
        "category": "people",
        "name": "waving hand sign tone 5",
        "unicode": "1f44b-1f3ff"
    },
    ":wavy_dash:": {
        "category": "symbols",
        "name": "wavy dash",
        "unicode": "3030",
        "unicode_alt": "3030-fe0f"
    },
    ":waxing_crescent_moon:": {
        "category": "nature",
        "name": "waxing crescent moon symbol",
        "unicode": "1f312"
    },
    ":waxing_gibbous_moon:": {
        "category": "nature",
        "name": "waxing gibbous moon symbol",
        "unicode": "1f314"
    },
    ":wc:": {
        "category": "symbols",
        "name": "water closet",
        "unicode": "1f6be"
    },
    ":weary:": {
        "category": "people",
        "name": "weary face",
        "unicode": "1f629"
    },
    ":wedding:": {
        "category": "travel",
        "name": "wedding",
        "unicode": "1f492"
    },
    ":whale2:": {
        "category": "nature",
        "name": "whale",
        "unicode": "1f40b"
    },
    ":whale:": {
        "category": "nature",
        "name": "spouting whale",
        "unicode": "1f433"
    },
    ":wheel_of_dharma:": {
        "category": "symbols",
        "name": "wheel of dharma",
        "unicode": "2638",
        "unicode_alt": "2638-fe0f"
    },
    ":wheelchair:": {
        "category": "symbols",
        "name": "wheelchair symbol",
        "unicode": "267f",
        "unicode_alt": "267f-fe0f"
    },
    ":white_check_mark:": {
        "category": "symbols",
        "name": "white heavy check mark",
        "unicode": "2705"
    },
    ":white_circle:": {
        "category": "symbols",
        "name": "white circle",
        "unicode": "26aa",
        "unicode_alt": "26aa-fe0f"
    },
    ":white_flower:": {
        "category": "symbols",
        "name": "white flower",
        "unicode": "1f4ae"
    },
    ":white_large_square:": {
        "category": "symbols",
        "name": "white large square",
        "unicode": "2b1c",
        "unicode_alt": "2b1c-fe0f"
    },
    ":white_medium_small_square:": {
        "category": "symbols",
        "name": "white medium small square",
        "unicode": "25fd",
        "unicode_alt": "25fd-fe0f"
    },
    ":white_medium_square:": {
        "category": "symbols",
        "name": "white medium square",
        "unicode": "25fb",
        "unicode_alt": "25fb-fe0f"
    },
    ":white_small_square:": {
        "category": "symbols",
        "name": "white small square",
        "unicode": "25ab",
        "unicode_alt": "25ab-fe0f"
    },
    ":white_square_button:": {
        "category": "symbols",
        "name": "white square button",
        "unicode": "1f533"
    },
    ":white_sun_cloud:": {
        "category": "nature",
        "name": "white sun behind cloud",
        "unicode": "1f325",
        "unicode_alt": "1f325-fe0f"
    },
    ":white_sun_rain_cloud:": {
        "category": "nature",
        "name": "white sun behind cloud with rain",
        "unicode": "1f326",
        "unicode_alt": "1f326-fe0f"
    },
    ":white_sun_small_cloud:": {
        "category": "nature",
        "name": "white sun with small cloud",
        "unicode": "1f324",
        "unicode_alt": "1f324-fe0f"
    },
    ":wilted_rose:": {
        "category": "nature",
        "name": "wilted flower",
        "unicode": "1f940"
    },
    ":wind_blowing_face:": {
        "category": "nature",
        "name": "wind blowing face",
        "unicode": "1f32c",
        "unicode_alt": "1f32c-fe0f"
    },
    ":wind_chime:": {
        "category": "objects",
        "name": "wind chime",
        "unicode": "1f390"
    },
    ":wine_glass:": {
        "category": "food",
        "name": "wine glass",
        "unicode": "1f377"
    },
    ":wink:": {
        "category": "people",
        "name": "winking face",
        "unicode": "1f609"
    },
    ":wolf:": {
        "category": "nature",
        "name": "wolf face",
        "unicode": "1f43a"
    },
    ":woman:": {
        "category": "people",
        "name": "woman",
        "unicode": "1f469"
    },
    ":woman_tone1:": {
        "category": "people",
        "name": "woman tone 1",
        "unicode": "1f469-1f3fb"
    },
    ":woman_tone2:": {
        "category": "people",
        "name": "woman tone 2",
        "unicode": "1f469-1f3fc"
    },
    ":woman_tone3:": {
        "category": "people",
        "name": "woman tone 3",
        "unicode": "1f469-1f3fd"
    },
    ":woman_tone4:": {
        "category": "people",
        "name": "woman tone 4",
        "unicode": "1f469-1f3fe"
    },
    ":woman_tone5:": {
        "category": "people",
        "name": "woman tone 5",
        "unicode": "1f469-1f3ff"
    },
    ":womans_clothes:": {
        "category": "people",
        "name": "womans clothes",
        "unicode": "1f45a"
    },
    ":womans_hat:": {
        "category": "people",
        "name": "womans hat",
        "unicode": "1f452"
    },
    ":womens:": {
        "category": "symbols",
        "name": "womens symbol",
        "unicode": "1f6ba"
    },
    ":worried:": {
        "category": "people",
        "name": "worried face",
        "unicode": "1f61f"
    },
    ":wrench:": {
        "category": "objects",
        "name": "wrench",
        "unicode": "1f527"
    },
    ":wrestlers:": {
        "category": "activity",
        "name": "wrestlers",
        "unicode": "1f93c"
    },
    ":wrestlers_tone1:": {
        "category": "activity",
        "name": "wrestlers tone 1",
        "unicode": "1f93c-1f3fb"
    },
    ":wrestlers_tone2:": {
        "category": "activity",
        "name": "wrestlers tone 2",
        "unicode": "1f93c-1f3fc"
    },
    ":wrestlers_tone3:": {
        "category": "activity",
        "name": "wrestlers tone 3",
        "unicode": "1f93c-1f3fd"
    },
    ":wrestlers_tone4:": {
        "category": "activity",
        "name": "wrestlers tone 4",
        "unicode": "1f93c-1f3fe"
    },
    ":wrestlers_tone5:": {
        "category": "activity",
        "name": "wrestlers tone 5",
        "unicode": "1f93c-1f3ff"
    },
    ":writing_hand:": {
        "category": "people",
        "name": "writing hand",
        "unicode": "270d",
        "unicode_alt": "270d-fe0f"
    },
    ":writing_hand_tone1:": {
        "category": "people",
        "name": "writing hand tone 1",
        "unicode": "270d-1f3fb"
    },
    ":writing_hand_tone2:": {
        "category": "people",
        "name": "writing hand tone 2",
        "unicode": "270d-1f3fc"
    },
    ":writing_hand_tone3:": {
        "category": "people",
        "name": "writing hand tone 3",
        "unicode": "270d-1f3fd"
    },
    ":writing_hand_tone4:": {
        "category": "people",
        "name": "writing hand tone 4",
        "unicode": "270d-1f3fe"
    },
    ":writing_hand_tone5:": {
        "category": "people",
        "name": "writing hand tone 5",
        "unicode": "270d-1f3ff"
    },
    ":x:": {
        "category": "symbols",
        "name": "cross mark",
        "unicode": "274c"
    },
    ":yellow_heart:": {
        "category": "symbols",
        "name": "yellow heart",
        "unicode": "1f49b"
    },
    ":yen:": {
        "category": "objects",
        "name": "banknote with yen sign",
        "unicode": "1f4b4"
    },
    ":yin_yang:": {
        "category": "symbols",
        "name": "yin yang",
        "unicode": "262f",
        "unicode_alt": "262f-fe0f"
    },
    ":yum:": {
        "category": "people",
        "name": "face savouring delicious food",
        "unicode": "1f60b"
    },
    ":zap:": {
        "category": "nature",
        "name": "high voltage sign",
        "unicode": "26a1",
        "unicode_alt": "26a1-fe0f"
    },
    ":zero:": {
        "category": "symbols",
        "name": "keycap digit zero",
        "unicode": "0030-20e3",
        "unicode_alt": "0030-fe0f-20e3"
    },
    ":zipper_mouth:": {
        "category": "people",
        "name": "zipper-mouth face",
        "unicode": "1f910"
    },
    ":zzz:": {
        "category": "people",
        "name": "sleeping symbol",
        "unicode": "1f4a4"
    }
}
aliases = {
    ":+1:": ":thumbsup:",
    ":+1_tone1:": ":thumbsup_tone1:",
    ":+1_tone2:": ":thumbsup_tone2:",
    ":+1_tone3:": ":thumbsup_tone3:",
    ":+1_tone4:": ":thumbsup_tone4:",
    ":+1_tone5:": ":thumbsup_tone5:",
    ":-1:": ":thumbsdown:",
    ":-1_tone1:": ":thumbsdown_tone1:",
    ":-1_tone2:": ":thumbsdown_tone2:",
    ":-1_tone3:": ":thumbsdown_tone3:",
    ":-1_tone4:": ":thumbsdown_tone4:",
    ":-1_tone5:": ":thumbsdown_tone5:",
    ":ac:": ":flag_ac:",
    ":ad:": ":flag_ad:",
    ":admission_tickets:": ":tickets:",
    ":ae:": ":flag_ae:",
    ":af:": ":flag_af:",
    ":ag:": ":flag_ag:",
    ":ai:": ":flag_ai:",
    ":al:": ":flag_al:",
    ":am:": ":flag_am:",
    ":ao:": ":flag_ao:",
    ":aq:": ":flag_aq:",
    ":ar:": ":flag_ar:",
    ":archery:": ":bow_and_arrow:",
    ":as:": ":flag_as:",
    ":at:": ":flag_at:",
    ":atom_symbol:": ":atom:",
    ":au:": ":flag_au:",
    ":aw:": ":flag_aw:",
    ":ax:": ":flag_ax:",
    ":az:": ":flag_az:",
    ":ba:": ":flag_ba:",
    ":back_of_hand:": ":raised_back_of_hand:",
    ":back_of_hand_tone1:": ":raised_back_of_hand_tone1:",
    ":back_of_hand_tone2:": ":raised_back_of_hand_tone2:",
    ":back_of_hand_tone3:": ":raised_back_of_hand_tone3:",
    ":back_of_hand_tone4:": ":raised_back_of_hand_tone4:",
    ":back_of_hand_tone5:": ":raised_back_of_hand_tone5:",
    ":baguette_bread:": ":french_bread:",
    ":ballot_box_with_ballot:": ":ballot_box:",
    ":bb:": ":flag_bb:",
    ":bd:": ":flag_bd:",
    ":be:": ":flag_be:",
    ":beach_with_umbrella:": ":beach:",
    ":bellhop_bell:": ":bellhop:",
    ":bf:": ":flag_bf:",
    ":bg:": ":flag_bg:",
    ":bh:": ":flag_bh:",
    ":bi:": ":flag_bi:",
    ":biohazard_sign:": ":biohazard:",
    ":bj:": ":flag_bj:",
    ":bl:": ":flag_bl:",
    ":bm:": ":flag_bm:",
    ":bn:": ":flag_bn:",
    ":bo:": ":flag_bo:",
    ":bottle_with_popping_cork:": ":champagne:",
    ":boxing_gloves:": ":boxing_glove:",
    ":bq:": ":flag_bq:",
    ":br:": ":flag_br:",
    ":bs:": ":flag_bs:",
    ":bt:": ":flag_bt:",
    ":building_construction:": ":construction_site:",
    ":bv:": ":flag_bv:",
    ":bw:": ":flag_bw:",
    ":by:": ":flag_by:",
    ":bz:": ":flag_bz:",
    ":ca:": ":flag_ca:",
    ":call_me_hand:": ":call_me:",
    ":call_me_hand_tone1:": ":call_me_tone1:",
    ":call_me_hand_tone2:": ":call_me_tone2:",
    ":call_me_hand_tone3:": ":call_me_tone3:",
    ":call_me_hand_tone4:": ":call_me_tone4:",
    ":call_me_hand_tone5:": ":call_me_tone5:",
    ":card_file_box:": ":card_box:",
    ":card_index_dividers:": ":dividers:",
    ":cc:": ":flag_cc:",
    ":cf:": ":flag_cf:",
    ":cg:": ":flag_cg:",
    ":ch:": ":flag_ch:",
    ":cheese_wedge:": ":cheese:",
    ":chile:": ":flag_cl:",
    ":ci:": ":flag_ci:",
    ":city_sunrise:": ":city_sunset:",
    ":ck:": ":flag_ck:",
    ":clinking_glass:": ":champagne_glass:",
    ":cloud_with_lightning:": ":cloud_lightning:",
    ":cloud_with_rain:": ":cloud_rain:",
    ":cloud_with_snow:": ":cloud_snow:",
    ":cloud_with_tornado:": ":cloud_tornado:",
    ":clown_face:": ":clown:",
    ":cm:": ":flag_cm:",
    ":cn:": ":flag_cn:",
    ":co:": ":flag_co:",
    ":congo:": ":flag_cd:",
    ":couch_and_lamp:": ":couch:",
    ":couple_with_heart_mm:": ":couple_mm:",
    ":couple_with_heart_ww:": ":couple_ww:",
    ":couplekiss_mm:": ":kiss_mm:",
    ":couplekiss_ww:": ":kiss_ww:",
    ":cp:": ":flag_cp:",
    ":cr:": ":flag_cr:",
    ":cricket_bat_ball:": ":cricket:",
    ":cu:": ":flag_cu:",
    ":cv:": ":flag_cv:",
    ":cw:": ":flag_cw:",
    ":cx:": ":flag_cx:",
    ":cy:": ":flag_cy:",
    ":cz:": ":flag_cz:",
    ":dagger_knife:": ":dagger:",
    ":de:": ":flag_de:",
    ":derelict_house_building:": ":house_abandoned:",
    ":desert_island:": ":island:",
    ":desktop_computer:": ":desktop:",
    ":dg:": ":flag_dg:",
    ":dj:": ":flag_dj:",
    ":dk:": ":flag_dk:",
    ":dm:": ":flag_dm:",
    ":do:": ":flag_do:",
    ":double_vertical_bar:": ":pause_button:",
    ":dove_of_peace:": ":dove:",
    ":drool:": ":drooling_face:",
    ":drum_with_drumsticks:": ":drum:",
    ":dz:": ":flag_dz:",
    ":ea:": ":flag_ea:",
    ":ec:": ":flag_ec:",
    ":ee:": ":flag_ee:",
    ":eg:": ":flag_eg:",
    ":eh:": ":flag_eh:",
    ":eject_symbol:": ":eject:",
    ":email:": ":e-mail:",
    ":er:": ":flag_er:",
    ":es:": ":flag_es:",
    ":et:": ":flag_et:",
    ":eu:": ":flag_eu:",
    ":expecting_woman:": ":pregnant_woman:",
    ":expecting_woman_tone1:": ":pregnant_woman_tone1:",
    ":expecting_woman_tone2:": ":pregnant_woman_tone2:",
    ":expecting_woman_tone3:": ":pregnant_woman_tone3:",
    ":expecting_woman_tone4:": ":pregnant_woman_tone4:",
    ":expecting_woman_tone5:": ":pregnant_woman_tone5:",
    ":face_with_cowboy_hat:": ":cowboy:",
    ":face_with_head_bandage:": ":head_bandage:",
    ":face_with_rolling_eyes:": ":rolling_eyes:",
    ":face_with_thermometer:": ":thermometer_face:",
    ":facepalm:": ":face_palm:",
    ":facepalm_tone1:": ":face_palm_tone1:",
    ":facepalm_tone2:": ":face_palm_tone2:",
    ":facepalm_tone3:": ":face_palm_tone3:",
    ":facepalm_tone4:": ":face_palm_tone4:",
    ":facepalm_tone5:": ":face_palm_tone5:",
    ":fencing:": ":fencer:",
    ":fi:": ":flag_fi:",
    ":film_projector:": ":projector:",
    ":first_place_medal:": ":first_place:",
    ":fj:": ":flag_fj:",
    ":fk:": ":flag_fk:",
    ":flame:": ":fire:",
    ":flan:": ":custard:",
    ":fm:": ":flag_fm:",
    ":fo:": ":flag_fo:",
    ":fork_and_knife_with_plate:": ":fork_knife_plate:",
    ":fox_face:": ":fox:",
    ":fr:": ":flag_fr:",
    ":frame_with_picture:": ":frame_photo:",
    ":funeral_urn:": ":urn:",
    ":ga:": ":flag_ga:",
    ":gay_pride_flag:": ":rainbow_flag:",
    ":gb:": ":flag_gb:",
    ":gd:": ":flag_gd:",
    ":ge:": ":flag_ge:",
    ":gf:": ":flag_gf:",
    ":gg:": ":flag_gg:",
    ":gh:": ":flag_gh:",
    ":gi:": ":flag_gi:",
    ":gl:": ":flag_gl:",
    ":glass_of_milk:": ":milk:",
    ":gm:": ":flag_gm:",
    ":gn:": ":flag_gn:",
    ":goal_net:": ":goal:",
    ":gp:": ":flag_gp:",
    ":gq:": ":flag_gq:",
    ":gr:": ":flag_gr:",
    ":grandma:": ":older_woman:",
    ":grandma_tone1:": ":older_woman_tone1:",
    ":grandma_tone2:": ":older_woman_tone2:",
    ":grandma_tone3:": ":older_woman_tone3:",
    ":grandma_tone4:": ":older_woman_tone4:",
    ":grandma_tone5:": ":older_woman_tone5:",
    ":green_salad:": ":salad:",
    ":gs:": ":flag_gs:",
    ":gt:": ":flag_gt:",
    ":gu:": ":flag_gu:",
    ":gw:": ":flag_gw:",
    ":gy:": ":flag_gy:",
    ":hammer_and_pick:": ":hammer_pick:",
    ":hammer_and_wrench:": ":tools:",
    ":hand_with_index_and_middle_finger_crossed:": ":fingers_crossed:",
    ":hand_with_index_and_middle_fingers_crossed_tone1:": ":fingers_crossed_tone1:",
    ":hand_with_index_and_middle_fingers_crossed_tone2:": ":fingers_crossed_tone2:",
    ":hand_with_index_and_middle_fingers_crossed_tone3:": ":fingers_crossed_tone3:",
    ":hand_with_index_and_middle_fingers_crossed_tone4:": ":fingers_crossed_tone4:",
    ":hand_with_index_and_middle_fingers_crossed_tone5:": ":fingers_crossed_tone5:",
    ":hankey:": ":poop:",
    ":heavy_heart_exclamation_mark_ornament:": ":heart_exclamation:",
    ":helmet_with_white_cross:": ":helmet_with_cross:",
    ":hk:": ":flag_hk:",
    ":hm:": ":flag_hm:",
    ":hn:": ":flag_hn:",
    ":hot_dog:": ":hotdog:",
    ":house_buildings:": ":homes:",
    ":hr:": ":flag_hr:",
    ":ht:": ":flag_ht:",
    ":hu:": ":flag_hu:",
    ":hugging_face:": ":hugging:",
    ":ic:": ":flag_ic:",
    ":ie:": ":flag_ie:",
    ":il:": ":flag_il:",
    ":im:": ":flag_im:",
    ":in:": ":flag_in:",
    ":indonesia:": ":flag_id:",
    ":io:": ":flag_io:",
    ":iq:": ":flag_iq:",
    ":ir:": ":flag_ir:",
    ":is:": ":flag_is:",
    ":it:": ":flag_it:",
    ":je:": ":flag_je:",
    ":jm:": ":flag_jm:",
    ":jo:": ":flag_jo:",
    ":jp:": ":flag_jp:",
    ":juggler:": ":juggling:",
    ":juggler_tone1:": ":juggling_tone1:",
    ":juggler_tone2:": ":juggling_tone2:",
    ":juggler_tone3:": ":juggling_tone3:",
    ":juggler_tone4:": ":juggling_tone4:",
    ":juggler_tone5:": ":juggling_tone5:",
    ":karate_uniform:": ":martial_arts_uniform:",
    ":kayak:": ":canoe:",
    ":ke:": ":flag_ke:",
    ":keycap_asterisk:": ":asterisk:",
    ":kg:": ":flag_kg:",
    ":kh:": ":flag_kh:",
    ":ki:": ":flag_ki:",
    ":kiwifruit:": ":kiwi:",
    ":km:": ":flag_km:",
    ":kn:": ":flag_kn:",
    ":kp:": ":flag_kp:",
    ":kr:": ":flag_kr:",
    ":kw:": ":flag_kw:",
    ":ky:": ":flag_ky:",
    ":kz:": ":flag_kz:",
    ":la:": ":flag_la:",
    ":latin_cross:": ":cross:",
    ":lb:": ":flag_lb:",
    ":lc:": ":flag_lc:",
    ":left_fist:": ":left_facing_fist:",
    ":left_fist_tone1:": ":left_facing_fist_tone1:",
    ":left_fist_tone2:": ":left_facing_fist_tone2:",
    ":left_fist_tone3:": ":left_facing_fist_tone3:",
    ":left_fist_tone4:": ":left_facing_fist_tone4:",
    ":left_fist_tone5:": ":left_facing_fist_tone5:",
    ":left_speech_bubble:": ":speech_left:",
    ":li:": ":flag_li:",
    ":liar:": ":lying_face:",
    ":linked_paperclips:": ":paperclips:",
    ":lion:": ":lion_face:",
    ":lk:": ":flag_lk:",
    ":lower_left_ballpoint_pen:": ":pen_ballpoint:",
    ":lower_left_crayon:": ":crayon:",
    ":lower_left_fountain_pen:": ":pen_fountain:",
    ":lower_left_paintbrush:": ":paintbrush:",
    ":lr:": ":flag_lr:",
    ":ls:": ":flag_ls:",
    ":lt:": ":flag_lt:",
    ":lu:": ":flag_lu:",
    ":lv:": ":flag_lv:",
    ":ly:": ":flag_ly:",
    ":ma:": ":flag_ma:",
    ":male_dancer:": ":man_dancing:",
    ":male_dancer_tone1:": ":man_dancing_tone1:",
    ":male_dancer_tone2:": ":man_dancing_tone2:",
    ":male_dancer_tone3:": ":man_dancing_tone3:",
    ":male_dancer_tone4:": ":man_dancing_tone4:",
    ":male_dancer_tone5:": ":man_dancing_tone5:",
    ":man_in_business_suit_levitating:": ":levitate:",
    ":mantlepiece_clock:": ":clock:",
    ":mc:": ":flag_mc:",
    ":md:": ":flag_md:",
    ":me:": ":flag_me:",
    ":mf:": ":flag_mf:",
    ":mg:": ":flag_mg:",
    ":mh:": ":flag_mh:",
    ":mk:": ":flag_mk:",
    ":ml:": ":flag_ml:",
    ":mm:": ":flag_mm:",
    ":mn:": ":flag_mn:",
    ":mo:": ":flag_mo:",
    ":money_mouth_face:": ":money_mouth:",
    ":mother_christmas:": ":mrs_claus:",
    ":mother_christmas_tone1:": ":mrs_claus_tone1:",
    ":mother_christmas_tone2:": ":mrs_claus_tone2:",
    ":mother_christmas_tone3:": ":mrs_claus_tone3:",
    ":mother_christmas_tone4:": ":mrs_claus_tone4:",
    ":mother_christmas_tone5:": ":mrs_claus_tone5:",
    ":motorbike:": ":motor_scooter:",
    ":mp:": ":flag_mp:",
    ":mq:": ":flag_mq:",
    ":mr:": ":flag_mr:",
    ":ms:": ":flag_ms:",
    ":mt:": ":flag_mt:",
    ":mu:": ":flag_mu:",
    ":mv:": ":flag_mv:",
    ":mw:": ":flag_mw:",
    ":mx:": ":flag_mx:",
    ":my:": ":flag_my:",
    ":mz:": ":flag_mz:",
    ":na:": ":flag_na:",
    ":national_park:": ":park:",
    ":nc:": ":flag_nc:",
    ":ne:": ":flag_ne:",
    ":nerd_face:": ":nerd:",
    ":next_track:": ":track_next:",
    ":nf:": ":flag_nf:",
    ":ni:": ":flag_ni:",
    ":nigeria:": ":flag_ng:",
    ":nl:": ":flag_nl:",
    ":no:": ":flag_no:",
    ":np:": ":flag_np:",
    ":nr:": ":flag_nr:",
    ":nu:": ":flag_nu:",
    ":nz:": ":flag_nz:",
    ":oil_drum:": ":oil:",
    ":old_key:": ":key2:",
    ":om:": ":flag_om:",
    ":pa:": ":flag_pa:",
    ":paella:": ":shallow_pan_of_food:",
    ":passenger_ship:": ":cruise_ship:",
    ":paw_prints:": ":feet:",
    ":pe:": ":flag_pe:",
    ":peace_symbol:": ":peace:",
    ":person_doing_cartwheel:": ":cartwheel:",
    ":person_doing_cartwheel_tone1:": ":cartwheel_tone1:",
    ":person_doing_cartwheel_tone2:": ":cartwheel_tone2:",
    ":person_doing_cartwheel_tone3:": ":cartwheel_tone3:",
    ":person_doing_cartwheel_tone4:": ":cartwheel_tone4:",
    ":person_doing_cartwheel_tone5:": ":cartwheel_tone5:",
    ":person_with_ball:": ":basketball_player:",
    ":person_with_ball_tone1:": ":basketball_player_tone1:",
    ":person_with_ball_tone2:": ":basketball_player_tone2:",
    ":person_with_ball_tone3:": ":basketball_player_tone3:",
    ":person_with_ball_tone4:": ":basketball_player_tone4:",
    ":person_with_ball_tone5:": ":basketball_player_tone5:",
    ":pf:": ":flag_pf:",
    ":pg:": ":flag_pg:",
    ":ph:": ":flag_ph:",
    ":pk:": ":flag_pk:",
    ":pl:": ":flag_pl:",
    ":pm:": ":flag_pm:",
    ":pn:": ":flag_pn:",
    ":poo:": ":poop:",
    ":pr:": ":flag_pr:",
    ":previous_track:": ":track_previous:",
    ":ps:": ":flag_ps:",
    ":pt:": ":flag_pt:",
    ":pudding:": ":custard:",
    ":pw:": ":flag_pw:",
    ":py:": ":flag_py:",
    ":qa:": ":flag_qa:",
    ":racing_car:": ":race_car:",
    ":racing_motorcycle:": ":motorcycle:",
    ":radioactive_sign:": ":radioactive:",
    ":railroad_track:": ":railway_track:",
    ":raised_hand_with_fingers_splayed:": ":hand_splayed:",
    ":raised_hand_with_fingers_splayed_tone1:": ":hand_splayed_tone1:",
    ":raised_hand_with_fingers_splayed_tone2:": ":hand_splayed_tone2:",
    ":raised_hand_with_fingers_splayed_tone3:": ":hand_splayed_tone3:",
    ":raised_hand_with_fingers_splayed_tone4:": ":hand_splayed_tone4:",
    ":raised_hand_with_fingers_splayed_tone5:": ":hand_splayed_tone5:",
    ":raised_hand_with_part_between_middle_and_ring_fingers:": ":vulcan:",
    ":raised_hand_with_part_between_middle_and_ring_fingers_tone1:": ":vulcan_tone1:",
    ":raised_hand_with_part_between_middle_and_ring_fingers_tone2:": ":vulcan_tone2:",
    ":raised_hand_with_part_between_middle_and_ring_fingers_tone3:": ":vulcan_tone3:",
    ":raised_hand_with_part_between_middle_and_ring_fingers_tone4:": ":vulcan_tone4:",
    ":raised_hand_with_part_between_middle_and_ring_fingers_tone5:": ":vulcan_tone5:",
    ":re:": ":flag_re:",
    ":reversed_hand_with_middle_finger_extended:": ":middle_finger:",
    ":reversed_hand_with_middle_finger_extended_tone1:": ":middle_finger_tone1:",
    ":reversed_hand_with_middle_finger_extended_tone2:": ":middle_finger_tone2:",
    ":reversed_hand_with_middle_finger_extended_tone3:": ":middle_finger_tone3:",
    ":reversed_hand_with_middle_finger_extended_tone4:": ":middle_finger_tone4:",
    ":reversed_hand_with_middle_finger_extended_tone5:": ":middle_finger_tone5:",
    ":rhinoceros:": ":rhino:",
    ":right_anger_bubble:": ":anger_right:",
    ":right_fist:": ":right_facing_fist:",
    ":right_fist_tone1:": ":right_facing_fist_tone1:",
    ":right_fist_tone2:": ":right_facing_fist_tone2:",
    ":right_fist_tone3:": ":right_facing_fist_tone3:",
    ":right_fist_tone4:": ":right_facing_fist_tone4:",
    ":right_fist_tone5:": ":right_facing_fist_tone5:",
    ":ro:": ":flag_ro:",
    ":robot_face:": ":robot:",
    ":rolled_up_newspaper:": ":newspaper2:",
    ":rolling_on_the_floor_laughing:": ":rofl:",
    ":rs:": ":flag_rs:",
    ":ru:": ":flag_ru:",
    ":rw:": ":flag_rw:",
    ":satisfied:": ":laughing:",
    ":saudi:": ":flag_sa:",
    ":saudiarabia:": ":flag_sa:",
    ":sb:": ":flag_sb:",
    ":sc:": ":flag_sc:",
    ":sd:": ":flag_sd:",
    ":se:": ":flag_se:",
    ":second_place_medal:": ":second_place:",
    ":sg:": ":flag_sg:",
    ":sh:": ":flag_sh:",
    ":shaking_hands:": ":handshake:",
    ":shaking_hands_tone1:": ":handshake_tone1:",
    ":shaking_hands_tone2:": ":handshake_tone2:",
    ":shaking_hands_tone3:": ":handshake_tone3:",
    ":shaking_hands_tone4:": ":handshake_tone4:",
    ":shaking_hands_tone5:": ":handshake_tone5:",
    ":shelled_peanut:": ":peanuts:",
    ":shit:": ":poop:",
    ":shopping_trolley:": ":shopping_cart:",
    ":si:": ":flag_si:",
    ":sick:": ":nauseated_face:",
    ":sign_of_the_horns:": ":metal:",
    ":sign_of_the_horns_tone1:": ":metal_tone1:",
    ":sign_of_the_horns_tone2:": ":metal_tone2:",
    ":sign_of_the_horns_tone3:": ":metal_tone3:",
    ":sign_of_the_horns_tone4:": ":metal_tone4:",
    ":sign_of_the_horns_tone5:": ":metal_tone5:",
    ":sj:": ":flag_sj:",
    ":sk:": ":flag_sk:",
    ":skeleton:": ":skull:",
    ":skull_and_crossbones:": ":skull_crossbones:",
    ":sl:": ":flag_sl:",
    ":sleuth_or_spy:": ":spy:",
    ":sleuth_or_spy_tone1:": ":spy_tone1:",
    ":sleuth_or_spy_tone2:": ":spy_tone2:",
    ":sleuth_or_spy_tone3:": ":spy_tone3:",
    ":sleuth_or_spy_tone4:": ":spy_tone4:",
    ":sleuth_or_spy_tone5:": ":spy_tone5:",
    ":slightly_frowning_face:": ":slight_frown:",
    ":slightly_smiling_face:": ":slight_smile:",
    ":sm:": ":flag_sm:",
    ":small_airplane:": ":airplane_small:",
    ":sn:": ":flag_sn:",
    ":sneeze:": ":sneezing_face:",
    ":snow_capped_mountain:": ":mountain_snow:",
    ":so:": ":flag_so:",
    ":speaking_head_in_silhouette:": ":speaking_head:",
    ":spiral_calendar_pad:": ":calendar_spiral:",
    ":spiral_note_pad:": ":notepad_spiral:",
    ":sports_medal:": ":medal:",
    ":sr:": ":flag_sr:",
    ":ss:": ":flag_ss:",
    ":st:": ":flag_st:",
    ":stop_sign:": ":octagonal_sign:",
    ":studio_microphone:": ":microphone2:",
    ":stuffed_pita:": ":stuffed_flatbread:",
    ":sv:": ":flag_sv:",
    ":sx:": ":flag_sx:",
    ":sy:": ":flag_sy:",
    ":sz:": ":flag_sz:",
    ":ta:": ":flag_ta:",
    ":table_tennis:": ":ping_pong:",
    ":tc:": ":flag_tc:",
    ":td:": ":flag_td:",
    ":tf:": ":flag_tf:",
    ":tg:": ":flag_tg:",
    ":th:": ":flag_th:",
    ":thinking_face:": ":thinking:",
    ":third_place_medal:": ":third_place:",
    ":three_button_mouse:": ":mouse_three_button:",
    ":thumbdown:": ":thumbsdown:",
    ":thumbdown_tone1:": ":thumbsdown_tone1:",
    ":thumbdown_tone2:": ":thumbsdown_tone2:",
    ":thumbdown_tone3:": ":thumbsdown_tone3:",
    ":thumbdown_tone4:": ":thumbsdown_tone4:",
    ":thumbdown_tone5:": ":thumbsdown_tone5:",
    ":thumbup:": ":thumbsup:",
    ":thumbup_tone1:": ":thumbsup_tone1:",
    ":thumbup_tone2:": ":thumbsup_tone2:",
    ":thumbup_tone3:": ":thumbsup_tone3:",
    ":thumbup_tone4:": ":thumbsup_tone4:",
    ":thumbup_tone5:": ":thumbsup_tone5:",
    ":thunder_cloud_and_rain:": ":thunder_cloud_rain:",
    ":timer_clock:": ":timer:",
    ":tj:": ":flag_tj:",
    ":tk:": ":flag_tk:",
    ":tl:": ":flag_tl:",
    ":tn:": ":flag_tn:",
    ":to:": ":flag_to:",
    ":tr:": ":flag_tr:",
    ":tt:": ":flag_tt:",
    ":turkmenistan:": ":flag_tm:",
    ":tuvalu:": ":flag_tv:",
    ":tuxedo_tone1:": ":man_in_tuxedo_tone1:",
    ":tuxedo_tone2:": ":man_in_tuxedo_tone2:",
    ":tuxedo_tone3:": ":man_in_tuxedo_tone3:",
    ":tuxedo_tone4:": ":man_in_tuxedo_tone4:",
    ":tuxedo_tone5:": ":man_in_tuxedo_tone5:",
    ":tw:": ":flag_tw:",
    ":tz:": ":flag_tz:",
    ":ua:": ":flag_ua:",
    ":ug:": ":flag_ug:",
    ":um:": ":flag_um:",
    ":umbrella_on_ground:": ":beach_umbrella:",
    ":unicorn_face:": ":unicorn:",
    ":upside_down_face:": ":upside_down:",
    ":us:": ":flag_us:",
    ":uy:": ":flag_uy:",
    ":uz:": ":flag_uz:",
    ":va:": ":flag_va:",
    ":vc:": ":flag_vc:",
    ":ve:": ":flag_ve:",
    ":vg:": ":flag_vg:",
    ":vi:": ":flag_vi:",
    ":vn:": ":flag_vn:",
    ":vu:": ":flag_vu:",
    ":waving_black_flag:": ":flag_black:",
    ":waving_white_flag:": ":flag_white:",
    ":weight_lifter:": ":lifter:",
    ":weight_lifter_tone1:": ":lifter_tone1:",
    ":weight_lifter_tone2:": ":lifter_tone2:",
    ":weight_lifter_tone3:": ":lifter_tone3:",
    ":weight_lifter_tone4:": ":lifter_tone4:",
    ":weight_lifter_tone5:": ":lifter_tone5:",
    ":wf:": ":flag_wf:",
    ":whisky:": ":tumbler_glass:",
    ":white_frowning_face:": ":frowning2:",
    ":white_sun_behind_cloud:": ":white_sun_cloud:",
    ":white_sun_behind_cloud_with_rain:": ":white_sun_rain_cloud:",
    ":white_sun_with_small_cloud:": ":white_sun_small_cloud:",
    ":wilted_flower:": ":wilted_rose:",
    ":world_map:": ":map:",
    ":worship_symbol:": ":place_of_worship:",
    ":wrestling:": ":wrestlers:",
    ":wrestling_tone1:": ":wrestlers_tone1:",
    ":wrestling_tone2:": ":wrestlers_tone2:",
    ":wrestling_tone3:": ":wrestlers_tone3:",
    ":wrestling_tone4:": ":wrestlers_tone4:",
    ":wrestling_tone5:": ":wrestlers_tone5:",
    ":ws:": ":flag_ws:",
    ":xk:": ":flag_xk:",
    ":ye:": ":flag_ye:",
    ":yt:": ":flag_yt:",
    ":za:": ":flag_za:",
    ":zipper_mouth_face:": ":zipper_mouth:",
    ":zm:": ":flag_zm:",
    ":zw:": ":flag_zw:"
}
