# HaoBBS Makefile
# 提供便捷的项目管理命令

.PHONY: help install start run dev prod clean build deploy test backup

# 默认目标
help:
	@echo "HaoBBS 项目管理命令"
	@echo ""
	@echo "可用命令:"
	@echo "  install    完整安装和启动（首次使用）"
	@echo "  start      默认启动"
	@echo "  run        快速启动"
	@echo "  dev        开发模式启动（CSS监听）"
	@echo "  prod       生产模式启动"
	@echo "  clean      清理并重新安装依赖"
	@echo "  build      构建CSS资源"
	@echo "  deploy     部署到远程服务器"
	@echo "  test       运行测试"
	@echo "  backup     备份数据库"
	@echo "  help       显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make install   # 首次安装"
	@echo "  make dev       # 开发模式"
	@echo "  make run       # 快速启动"

# 完整安装和启动
install:
	@chmod +x scripts/*.sh
	@./scripts/start.sh --clean

# 默认启动
start:
	@chmod +x scripts/*.sh
	@./scripts/start.sh

# 快速启动
run:
	@chmod +x scripts/*.sh
	@./scripts/run.sh

# 开发模式
dev:
	@chmod +x scripts/*.sh
	@./scripts/start.sh --dev

# 生产模式
prod:
	@chmod +x scripts/*.sh
	@./scripts/start.sh --prod

# 清理并重新安装
clean:
	@chmod +x scripts/*.sh
	@./scripts/start.sh --clean

# 构建CSS
build:
	@chmod +x scripts/*.sh
	@./scripts/build-css.sh prod

# 部署到远程服务器
deploy:
	@chmod +x scripts/*.sh
	@./scripts/deploy.sh

# 运行测试
test:
	@echo "运行测试..."
	@if [ -d "venv_fixed" ]; then \
		source venv_fixed/bin/activate && python -m pytest tests/ -v; \
	else \
		echo "虚拟环境不存在，请先运行 make install"; \
	fi

# 备份数据库
backup:
	@echo "备份数据库..."
	@if [ -d "venv_fixed" ]; then \
		source venv_fixed/bin/activate && python scripts/backup.py; \
	else \
		echo "虚拟环境不存在，请先运行 make install"; \
	fi

# 检查项目状态
status:
	@echo "项目状态检查:"
	@echo "  项目路径: $(PWD)"
	@if [ -d "venv_fixed" ]; then \
		echo "  虚拟环境: ✅ 已创建"; \
	else \
		echo "  虚拟环境: ❌ 未创建"; \
	fi
	@if [ -f "forum.db" ]; then \
		echo "  数据库: ✅ 已存在"; \
	else \
		echo "  数据库: ❌ 不存在"; \
	fi
	@if [ -f "static/css/output.css" ]; then \
		echo "  CSS文件: ✅ 已构建"; \
	else \
		echo "  CSS文件: ❌ 未构建"; \
	fi
	@if [ -d "node_modules" ]; then \
		echo "  Node依赖: ✅ 已安装"; \
	else \
		echo "  Node依赖: ❌ 未安装"; \
	fi

# 清理所有生成的文件
distclean:
	@echo "清理所有生成的文件..."
	@rm -rf venv_fixed/
	@rm -rf node_modules/
	@rm -f package-lock.json
	@rm -f static/css/output.css
	@echo "清理完成"

# 显示项目信息
info:
	@echo "HaoBBS 项目信息"
	@echo "================"
	@echo "项目名称: HaoBBS"
	@echo "版本: 1.0.0"
	@echo "技术栈: Flask + Tailwind CSS + SQLite"
	@echo "Python版本要求: >= 3.8"
	@echo "Node.js版本要求: >= 14"
	@echo "默认端口: 5002"
	@echo ""
	@echo "目录结构:"
	@echo "  app.py           - Flask主应用"
	@echo "  forum.db         - SQLite数据库"
	@echo "  static/          - 静态资源"
	@echo "  templates/       - HTML模板"
	@echo "  scripts/         - 部署脚本"
	@echo "  venv_fixed/      - Python虚拟环境"
