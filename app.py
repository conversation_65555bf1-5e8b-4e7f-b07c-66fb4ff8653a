from flask import Flask, jsonify, render_template, request, redirect, url_for, abort, session, flash, send_from_directory
import sqlite3
from datetime import datetime, timezone, timedelta
from functools import wraps
from werkzeug.security import generate_password_hash, check_password_hash
import os
import markdown
import bleach
from markdown.extensions import codehilite, fenced_code, tables, toc
from pymdownx import superfences

app = Flask(__name__)
app.secret_key = '44e9e4f0c2764bc6e4875847d0b3c9ca62740429'  # 用于会话管理
app.config['SESSION_COOKIE_NAME'] = f'session_{os.environ.get("PORT", "5002")}'  # 动态设置会话名称
app.permanent_session_lifetime = timedelta(days=7)  # 设置会话有效期为7天
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # 设置静态文件默认缓存时间为1年
DATABASE = 'forum.db'

# 自定义静态文件路由，添加缓存控制
@app.route('/static/images/<path:filename>')
def serve_image(filename):
    response = send_from_directory('static/images', filename)
    response.headers['Cache-Control'] = 'public, max-age=31536000'
    response.headers['Expires'] = (datetime.now() + timedelta(days=365)).strftime('%a, %d %b %Y %H:%M:%S GMT')
    return response

# 登录验证装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 数据库初始化函数
def init_db():
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    # 创建用户表
    c.execute('''CREATE TABLE IF NOT EXISTS users
                (id INTEGER PRIMARY KEY AUTOINCREMENT,
                 username TEXT UNIQUE NOT NULL,
                 password_hash TEXT NOT NULL,
                 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)''')
    
    # 创建帖子表
    c.execute('''CREATE TABLE IF NOT EXISTS posts
                (id INTEGER PRIMARY KEY AUTOINCREMENT,
                 title TEXT NOT NULL,
                 content TEXT NOT NULL,
                 category TEXT NOT NULL DEFAULT '默认分类',
                 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)''')
    
    # 创建回复表
    c.execute('''CREATE TABLE IF NOT EXISTS replies
                (id INTEGER PRIMARY KEY AUTOINCREMENT,
                 post_id INTEGER NOT NULL,
                 content TEXT NOT NULL,
                 created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                 FOREIGN KEY(post_id) REFERENCES posts(id))''')
    
    conn.commit()
    conn.close()

# 应用启动时自动初始化数据库
with app.app_context():
    init_db()

# 北京时间转换过滤器
@app.template_filter('beijing_time')
def beijing_time_filter(s):
    try:
        dt = datetime.strptime(s, "%Y-%m-%d %H:%M:%S")
    except ValueError:
        return s

    utc_time = dt.replace(tzinfo=timezone.utc)
    beijing_time = utc_time.astimezone(timezone(timedelta(hours=8)))
    return beijing_time.strftime("%Y-%m-%d %H:%M:%S")

# Markdown配置
MARKDOWN_EXTENSIONS = [
    'markdown.extensions.fenced_code',
    'markdown.extensions.codehilite',
    'markdown.extensions.tables',
    'markdown.extensions.toc',
    'markdown.extensions.nl2br',
    'pymdownx.superfences',
    'pymdownx.highlight',
    'pymdownx.inlinehilite',
    'pymdownx.magiclink',
    'pymdownx.betterem',
    'pymdownx.tilde',
    'pymdownx.emoji',
    'pymdownx.tasklist',
    'pymdownx.mark'
]

MARKDOWN_EXTENSION_CONFIGS = {
    'markdown.extensions.codehilite': {
        'css_class': 'highlight',
        'use_pygments': True
    },
    'pymdownx.superfences': {
        'custom_fences': [
            {
                'name': 'mermaid',
                'class': 'mermaid',
                'format': superfences.fence_code_format
            }
        ]
    },
    'pymdownx.highlight': {
        'anchor_linenums': True,
        'line_spans': '__span',
        'pygments_lang_class': True
    },
    'pymdownx.tasklist': {
        'custom_checkbox': True
    }
}

# 允许的HTML标签和属性（用于bleach清理）
ALLOWED_TAGS = [
    'p', 'br', 'strong', 'em', 'u', 's', 'del', 'ins', 'mark',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li', 'dl', 'dt', 'dd',
    'blockquote', 'pre', 'code', 'span', 'div',
    'table', 'thead', 'tbody', 'tr', 'th', 'td',
    'a',
    'hr'
]

ALLOWED_ATTRIBUTES = {
    'a': ['href', 'title'],
    'pre': ['class'],
    'code': ['class'],
    'span': ['class'],
    'div': ['class'],
    'table': ['class'],
    'th': ['align'],
    'td': ['align'],
    'blockquote': ['cite']
}

# 允许的协议（用于链接）
ALLOWED_PROTOCOLS = ['http', 'https', 'mailto']

def render_markdown(text):
    """
    将Markdown文本转换为安全的HTML
    """
    if not text:
        return ''

    # 使用markdown库转换
    md = markdown.Markdown(
        extensions=MARKDOWN_EXTENSIONS,
        extension_configs=MARKDOWN_EXTENSION_CONFIGS
    )
    html = md.convert(text)

    # 使用bleach清理HTML，防止XSS攻击
    clean_html = bleach.clean(
        html,
        tags=ALLOWED_TAGS,
        attributes=ALLOWED_ATTRIBUTES,
        protocols=ALLOWED_PROTOCOLS,
        strip=True
    )

    return clean_html

# Markdown模板过滤器
@app.template_filter('markdown')
def markdown_filter(text):
    """
    Jinja2模板过滤器，用于在模板中渲染Markdown
    """
    return render_markdown(text)

# 数据库连接方法
def get_db_connection():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    conn.execute("PRAGMA foreign_keys = ON")  # 启用外键约束
    return conn

# 路由部分
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        print(f"DEBUG: Login attempt - Username: {username}, Password: {password}")
        
        conn = get_db_connection()
        user = conn.execute('SELECT * FROM users WHERE username = ?', (username,)).fetchone()
        
        if user:
            print(f"DEBUG: User found - ID: {user['id']}, Username: {user['username']}")
            print(f"DEBUG: Stored password hash: {user['password_hash']}")
            password_check = check_password_hash(user['password_hash'], password)
            print(f"DEBUG: Password check result: {password_check}")
            
            if password_check:
                session.permanent = True  # 设置会话为永久性
                session['user_id'] = user['id']
                session['username'] = user['username']
                conn.close()
                print("DEBUG: Login successful, redirecting to index")
                return redirect(url_for('index'))
            else:
                print("DEBUG: Password check failed")
                flash('用户名或密码错误')
        else:
            print("DEBUG: User not found")
            flash('用户名或密码错误')
        conn.close()
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/')
@login_required
def index():
    category = request.args.get('category')
    
    conn = get_db_connection()
    
    # 获取所有存在的分类（按出现频率排序）
    categories = conn.execute('''
        SELECT category, COUNT(*) as count 
        FROM posts 
        GROUP BY category 
        ORDER BY count DESC
    ''').fetchall()
    
    # 构建查询
    query = 'SELECT * FROM posts'
    params = ()
    if category:
        query += ' WHERE category = ?'
        params = (category,)
    
    posts = conn.execute(query, params).fetchall()
    conn.close()
    
    return render_template('index.html', 
                         posts=posts,
                         categories=categories,
                         selected_category=category)

@app.route('/new_post')
@login_required
def new_post():
    return render_template('new_post.html')

@app.route('/post', methods=['POST'])
@login_required
def create_post():
    title = request.form['title']
    content = request.form['content']
    category = request.form.get('category', '默认分类')
    
    conn = get_db_connection()
    conn.execute('INSERT INTO posts (title, content, category) VALUES (?, ?, ?)',
                (title, content, category))
    conn.commit()
    conn.close()
    return redirect(url_for('index'))

from flask import request

@app.route('/post/<int:post_id>')
@login_required
def view_post(post_id):
    page = request.args.get('page', 1, type=int)
    sort = request.args.get('sort', 'desc')  # 默认倒序
    per_page = 10  # 每页显示10条回复
    
    if sort not in ['asc', 'desc']:
        sort = 'desc'

    conn = get_db_connection()
    post = conn.execute('SELECT * FROM posts WHERE id = ?', (post_id,)).fetchone()

    # 获取所有存在的分类（按出现频率排序）
    categories = conn.execute('''
        SELECT category, COUNT(*) as count 
        FROM posts 
        GROUP BY category 
        ORDER BY count DESC
    ''').fetchall()

    # 获取总回复数
    total_replies = conn.execute('SELECT COUNT(*) FROM replies WHERE post_id = ?', (post_id,)).fetchone()[0]

    # 计算总页数
    total_pages = (total_replies + per_page - 1) // per_page

    # 获取当前页的回复
    offset = (page - 1) * per_page
    order = 'ASC' if sort == 'asc' else 'DESC'
    replies = conn.execute(f'SELECT * FROM replies WHERE post_id = ? ORDER BY created_at {order} LIMIT ? OFFSET ?',
                          (post_id, per_page, offset)).fetchall()

    conn.close()

    return render_template('post.html',
                           post=post,
                           replies=replies,
                           page=page,
                           total_pages=total_pages,
                           total_replies=total_replies,
                           current_sort=sort,
                           categories=categories)  # 添加分类数据

@app.route('/reply/<int:post_id>', methods=['POST'])
@login_required
def create_reply(post_id):
    content = request.form['content']
    
    conn = get_db_connection()
    cursor = conn.execute('INSERT INTO replies (post_id, content) VALUES (?, ?)',
                         (post_id, content))
    conn.commit()
    reply_id = cursor.lastrowid
    conn.close()
    
    return jsonify(success=True, replyId=reply_id)

@app.route('/post/<int:post_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_post(post_id):
    conn = get_db_connection()
    post = conn.execute('SELECT * FROM posts WHERE id = ?', (post_id,)).fetchone()
    
    if not post:
        conn.close()
        abort(404)
    
    if request.method == 'POST':
        title = request.form['title']
        content = request.form['content']
        category = request.form.get('category', '默认分类')
        
        conn.execute('UPDATE posts SET title = ?, content = ?, category = ? WHERE id = ?',
                    (title, content, category, post_id))
        conn.commit()
        
        # 获取更新后的帖子数据
        updated_post = conn.execute('SELECT * FROM posts WHERE id = ?', (post_id,)).fetchone()
        
        # 转换时间为北京时间
        created_at = updated_post['created_at']
        try:
            dt = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
            utc_time = dt.replace(tzinfo=timezone.utc)
            beijing_time = utc_time.astimezone(timezone(timedelta(hours=8)))
            formatted_time = beijing_time.strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            formatted_time = created_at
        
        post_data = {
            'id': updated_post['id'],
            'title': updated_post['title'],
            'content': render_markdown(updated_post['content']),  # 返回渲染后的HTML
            'raw_content': updated_post['content'],  # 返回原始内容
            'category': updated_post['category'],
            'created_at': formatted_time
        }
        
        conn.close()
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify(post_data)
        else:
            return redirect(url_for('view_post', post_id=post_id))
    
    conn.close()
    return render_template('edit_post.html', post=post)

@app.route('/reply/<int:reply_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_reply(reply_id):
    conn = get_db_connection()
    reply = conn.execute('SELECT * FROM replies WHERE id = ?', (reply_id,)).fetchone()

    if not reply:
        conn.close()
        abort(404)

    if request.method == 'GET':
        # 返回原始内容用于编辑
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            conn.close()
            return jsonify({'content': reply['content']})

    if request.method == 'POST':
        content = request.form['content']

        conn.execute('UPDATE replies SET content = ? WHERE id = ?',
                    (content, reply_id))
        conn.commit()

        # 获取更新后的回复数据
        updated_reply = conn.execute('SELECT * FROM replies WHERE id = ?', (reply_id,)).fetchone()

        # 转换时间为北京时间
        created_at = updated_reply['created_at']
        try:
            dt = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
            utc_time = dt.replace(tzinfo=timezone.utc)
            beijing_time = utc_time.astimezone(timezone(timedelta(hours=8)))
            formatted_time = beijing_time.strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            formatted_time = created_at

        reply_data = {
            'id': updated_reply['id'],
            'content': render_markdown(updated_reply['content']),  # 返回渲染后的HTML
            'raw_content': updated_reply['content'],  # 返回原始内容
            'created_at': formatted_time,
            'post_id': updated_reply['post_id']
        }

        conn.close()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify(reply_data)
        else:
            return redirect(url_for('view_post', post_id=reply['post_id']))

    conn.close()
    return redirect(url_for('view_post', post_id=reply['post_id']))

@app.route('/reply/<int:reply_id>/delete', methods=['POST'])
@login_required
def delete_reply(reply_id):
    conn = get_db_connection()
    reply = conn.execute('SELECT * FROM replies WHERE id = ?', (reply_id,)).fetchone()
    
    if not reply:
        conn.close()
        abort(404)
    
    post_id = reply['post_id']
    conn.execute('DELETE FROM replies WHERE id = ?', (reply_id,))
    conn.commit()
    conn.close()
    return redirect(url_for('view_post', post_id=post_id))


@app.route('/post/<int:post_id>/delete', methods=['POST'])
@login_required
def delete_post(post_id):
    conn = get_db_connection()
    
    replies_count = conn.execute('SELECT COUNT(*) FROM replies WHERE post_id = ?', 
                               (post_id,)).fetchone()[0]
    
    if replies_count > 0:
        conn.close()
        return jsonify(success=False, message="请先删除所有回复后再删除帖子"), 400
    
    conn.execute('DELETE FROM posts WHERE id = ?', (post_id,))
    conn.commit()
    conn.close()
    return jsonify(success=True)


if __name__ == '__main__':
    app.run(debug=True, port=5002, host='0.0.0.0')
