"""English US keymap."""

keymap = {
    # Digits
    "0": "0",
    "1": "1",
    "2": "2",
    "3": "3",
    "4": "4",
    "5": "5",
    "6": "6",
    "7": "7",
    "8": "8",
    "9": "9",

    # Letters
    "a": "A",
    "b": "B",
    "c": "C",
    "d": "D",
    "e": "E",
    "f": "F",
    "g": "G",
    "h": "H",
    "i": "I",
    "j": "J",
    "k": "K",
    "l": "L",
    "m": "M",
    "n": "N",
    "o": "O",
    "p": "P",
    "q": "Q",
    "r": "R",
    "s": "S",
    "t": "T",
    "u": "U",
    "v": "V",
    "w": "W",
    "x": "X",
    "y": "Y",
    "z": "Z",

    # Space
    "space": "Space",

    # Punctuation
    "backslash": "\\",
    "bar": "|",
    "brace-left": "{",
    "brace-right": "}",
    "bracket-left": "[",
    "bracket-right": "]",
    "colon": ":",
    "comma": ",",
    "double-quote": "\"",
    "equal": "=",
    "exclam": "!",
    "grave": "`",
    "greater": ">",
    "less": "<",
    "minus": "-",
    "period": ".",
    "plus": "+",
    "question": "?",
    "semicolon": ";",
    "single-quote": "'",
    "slash": "/",
    "tilde": "~",
    "underscore": "_",

    # Navigation keys
    "arrow-up": "Up",
    "arrow-down": "Down",
    "arrow-left": "Left",
    "arrow-right": "Right",
    "page-up": "Page Up",
    "page-down": "Page Down",
    "home": "Home",
    "end": "End",


    # Edit keys
    "backspace": "Backspace",
    "delete": "Del",
    "insert": "Ins",
    "tab": "Tab",

    # Action keys
    "break": "Break",
    "caps-lock": "Caps Lock",
    "clear": "Clear",
    "eject": "Eject",
    "enter": "Enter",
    "escape": "Esc",
    "help": "Help",
    "print-screen": "Print Screen",
    "scroll-lock": "Scroll Lock",

    # Numeric keypad
    "num0": "Num 0",
    "num1": "Num 1",
    "num2": "Num 2",
    "num3": "Num 3",
    "num4": "Num 4",
    "num5": "Num 5",
    "num6": "Num 6",
    "num7": "Num 7",
    "num8": "Num 8",
    "num9": "Num 9",
    "num-asterisk": "Num *",
    "num-clear": "Num Clear",
    "num-delete": "Num Del",
    "num-equal": "Num =",
    "num-lock": "Num Lock",
    "num-minus": "Num -",
    "num-plus": "Num +",
    "num-separator": "Num .",
    "num-slash": "Num /",
    "num-enter": "Num Enter",

    # Modifier keys
    "alt": "Alt",
    "alt-graph": "AltGr",
    "command": "Cmd",
    "control": "Ctrl",
    "function": "Fn",
    "left-alt": "Left Alt",
    "left-command": "Left Command",
    "left-control": "Left Ctrl",
    "left-meta": "Left Meta",
    "left-option": "Left Option",
    "left-shift": "Left Shift",
    "left-super": "Left Super",
    "left-windows": "Left Win",
    "meta": "Meta",
    "option": "Option",
    "right-alt": "Right Alt",
    "right-command": "Right Command",
    "right-control": "Right Ctrl",
    "right-meta": "Right Meta",
    "right-option": "Right Option",
    "right-shift": "Right Shift",
    "right-super": "Right Super",
    "right-windows": "Right Win",
    "shift": "Shift",
    "super": "Super",
    "windows": "Win",

    # Function keys
    "f1": "F1",
    "f2": "F2",
    "f3": "F3",
    "f4": "F4",
    "f5": "F5",
    "f6": "F6",
    "f7": "F7",
    "f8": "F8",
    "f9": "F9",
    "f10": "F10",
    "f11": "F11",
    "f12": "F12",
    "f13": "F13",
    "f14": "F14",
    "f15": "F15",
    "f16": "F16",
    "f17": "F17",
    "f18": "F18",
    "f19": "F19",
    "f20": "F20",
    "f21": "F21",
    "f22": "F22",
    "f23": "F23",
    "f24": "F24",

    # Extra keys
    "backtab": "Back Tab",
    "browser-back": "Browser Back",
    "browser-favorites": "Browser Favorites",
    "browser-forward": "Browser Forward",
    "browser-home": "Browser Home",
    "browser-refresh": "Browser Refresh",
    "browser-search": "Browser Search",
    "browser-stop": "Browser Stop",
    "context-menu": "Menu",
    "copy": "Copy",
    "mail": "Mail",
    "media": "Media",
    "media-next-track": "Next Track",
    "media-pause": "Pause",
    "media-play": "Play",
    "media-play-pause": "Play/Pause",
    "media-prev-track": "Previous Track",
    "media-stop": "Stop",
    "print": "Print",
    "reset": "Reset",
    "select": "Select",
    "sleep": "Sleep",
    "volume-down": "Volume Down",
    "volume-mute": "Mute",
    "volume-up": "Volume Up",
    "zoom": "Zoom",
    "power": "Power",
    "fingerprint": "Fingerprint",

    # Mouse
    "left-button": "Left Button",
    "middle-button": "Middle Button",
    "right-button": "Right Button",
    "x-button1": "X Button 1",
    "x-button2": "X Button 2"
}

aliases = {
    "add": "num-plus",
    "altgr": "alt-graph",
    "apps": "context-menu",
    "back": "backspace",
    "bksp": "backspace",
    "bktab": "backtab",
    "cancel": "break",
    "capital": "caps-lock",
    "close-brace": "brace-right",
    "close-bracket": "bracket-right",
    "clr": "clear",
    "cmd": "command",
    "cplk": "caps-lock",
    "ctrl": "control",
    "dblquote": "double-quote",
    "decimal": "num-separator",
    "del": "delete",
    "divide": "num-slash",
    "down": "arrow-down",
    "esc": "escape",
    "return": "enter",
    "exclamation": "exclam",
    "favorites": "browser-favorites",
    "fn": "function",
    "forward": "browser-forward",
    "grave-accent": "grave",
    "greater-than": "greater",
    "gt": "greater",
    "hyphen": "minus",
    "ins": "insert",
    "lalt": "left-alt",
    "launch-mail": "mail",
    "launch-media": "media",
    "lbutton": "left-button",
    "lcmd": "left-command",
    "lcommand": "left-command",
    "lcontrol": "left-control",
    "lctrl": "left-control",
    "left": "arrow-left",
    "left-cmd": "left-command",
    "left-ctrl": "left-control",
    "lopt": "left-option",
    "loption": "left-option",
    "left-opt": "left-option",
    "left-win": "left-windows",
    "less-than": "less",
    "lmeta": "left-meta",
    "lshift": "left-shift",
    "lsuper": "left-super",
    "lt": "less",
    "lwin": "left-windows",
    "lwindows": "left-windows",
    "mbutton": "middle-button",
    "menu": "context-menu",
    "multiply": "num-asterisk",
    "mute": "volume-mute",
    "next": "page-down",
    "next-track": "media-next-track",
    "num-del": "num-delete",
    "numlk": "num-lock",
    "open-brace": "brace-left",
    "open-bracket": "bracket-left",
    "opt": "option",
    "page-dn": "page-down",
    "page-up": "page-up",
    "pause": "media-pause",
    "pg-dn": "page-down",
    "pg-up": "page-up",
    "pipe": "bar",
    "play": "media-play",
    "play-pause": "media-play-pause",
    "prev-track": "media-prev-track",
    "prior": "page-up",
    "prtsc": "print-screen",
    "question-mark": "question",
    "ralt": "right-alt",
    "rbutton": "right-button",
    "rcontrol": "right-control",
    "rcmd": "right-command",
    "rcommand": "right-command",
    "rctrl": "right-control",
    "refresh": "browser-refresh",
    "right": "arrow-right",
    "right-cmd": "right-command",
    "right-ctrl": "right-control",
    "right-meta": "right-meta",
    "right-opt": "right-option",
    "right-win": "right-windows",
    "rmeta": "right-meta",
    "ropt": "right-option",
    "roption": "right-option",
    "rshift": "right-shift",
    "rsuper": "right-super",
    "rwin": "right-windows",
    "rwindows": "right-windows",
    "scroll": "scroll-lock",
    "search": "browser-search",
    "separator": "num-separator",
    "spc": "space",
    "stop": "media-stop",
    "subtract": "num-minus",
    "tabulator": "tab",
    "up": "arrow-up",
    "vol-down": "volume-down",
    "vol-mute": "volume-mute",
    "vol-up": "volume-up",
    "win": "windows",
    "xbutton1": "x-button1",
    "xbutton2": "x-button2"
}
