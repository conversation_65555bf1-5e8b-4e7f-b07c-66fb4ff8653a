"""Gemoji autogen.

Generated from gemoji source. Do not edit by hand.

Copyright (c) 2019 GitHub, Inc.

Permission is hereby granted, free of charge, to any person
obtaining a copy of this software and associated documentation
files (the "Software"), to deal in the Software without
restriction, including without limitation the rights to use,
copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the
Software is furnished to do so, subject to the following
conditions:

The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
OTHER DEALINGS IN THE SOFTWARE.
"""
version = "v4.1.0"
name = "gemoji"
emoji = {
    ":+1:": {
        "category": "People & Body",
        "name": "thumbs up",
        "unicode": "1f44d"
    },
    ":-1:": {
        "category": "People & Body",
        "name": "thumbs down",
        "unicode": "1f44e"
    },
    ":100:": {
        "category": "Smileys & Emotion",
        "name": "hundred points",
        "unicode": "1f4af"
    },
    ":1234:": {
        "category": "Symbols",
        "name": "input numbers",
        "unicode": "1f522"
    },
    ":1st_place_medal:": {
        "category": "Activities",
        "name": "1st place medal",
        "unicode": "1f947"
    },
    ":2nd_place_medal:": {
        "category": "Activities",
        "name": "2nd place medal",
        "unicode": "1f948"
    },
    ":3rd_place_medal:": {
        "category": "Activities",
        "name": "3rd place medal",
        "unicode": "1f949"
    },
    ":8ball:": {
        "category": "Activities",
        "name": "pool 8 ball",
        "unicode": "1f3b1"
    },
    ":a:": {
        "category": "Symbols",
        "name": "A button (blood type)",
        "unicode": "1f170",
        "unicode_alt": "1f170-fe0f"
    },
    ":ab:": {
        "category": "Symbols",
        "name": "AB button (blood type)",
        "unicode": "1f18e"
    },
    ":abacus:": {
        "category": "Objects",
        "name": "abacus",
        "unicode": "1f9ee"
    },
    ":abc:": {
        "category": "Symbols",
        "name": "input latin letters",
        "unicode": "1f524"
    },
    ":abcd:": {
        "category": "Symbols",
        "name": "input latin lowercase",
        "unicode": "1f521"
    },
    ":accept:": {
        "category": "Symbols",
        "name": "Japanese \u201cacceptable\u201d button",
        "unicode": "1f251"
    },
    ":accordion:": {
        "category": "Objects",
        "name": "accordion",
        "unicode": "1fa97"
    },
    ":adhesive_bandage:": {
        "category": "Objects",
        "name": "adhesive bandage",
        "unicode": "1fa79"
    },
    ":adult:": {
        "category": "People & Body",
        "name": "person",
        "unicode": "1f9d1"
    },
    ":aerial_tramway:": {
        "category": "Travel & Places",
        "name": "aerial tramway",
        "unicode": "1f6a1"
    },
    ":afghanistan:": {
        "category": "Flags",
        "name": "flag: Afghanistan",
        "unicode": "1f1e6-1f1eb"
    },
    ":airplane:": {
        "category": "Travel & Places",
        "name": "airplane",
        "unicode": "2708",
        "unicode_alt": "2708-fe0f"
    },
    ":aland_islands:": {
        "category": "Flags",
        "name": "flag: \u00c5land Islands",
        "unicode": "1f1e6-1f1fd"
    },
    ":alarm_clock:": {
        "category": "Travel & Places",
        "name": "alarm clock",
        "unicode": "23f0"
    },
    ":albania:": {
        "category": "Flags",
        "name": "flag: Albania",
        "unicode": "1f1e6-1f1f1"
    },
    ":alembic:": {
        "category": "Objects",
        "name": "alembic",
        "unicode": "2697",
        "unicode_alt": "2697-fe0f"
    },
    ":algeria:": {
        "category": "Flags",
        "name": "flag: Algeria",
        "unicode": "1f1e9-1f1ff"
    },
    ":alien:": {
        "category": "Smileys & Emotion",
        "name": "alien",
        "unicode": "1f47d"
    },
    ":ambulance:": {
        "category": "Travel & Places",
        "name": "ambulance",
        "unicode": "1f691"
    },
    ":american_samoa:": {
        "category": "Flags",
        "name": "flag: American Samoa",
        "unicode": "1f1e6-1f1f8"
    },
    ":amphora:": {
        "category": "Food & Drink",
        "name": "amphora",
        "unicode": "1f3fa"
    },
    ":anatomical_heart:": {
        "category": "People & Body",
        "name": "anatomical heart",
        "unicode": "1fac0"
    },
    ":anchor:": {
        "category": "Travel & Places",
        "name": "anchor",
        "unicode": "2693"
    },
    ":andorra:": {
        "category": "Flags",
        "name": "flag: Andorra",
        "unicode": "1f1e6-1f1e9"
    },
    ":angel:": {
        "category": "People & Body",
        "name": "baby angel",
        "unicode": "1f47c"
    },
    ":anger:": {
        "category": "Smileys & Emotion",
        "name": "anger symbol",
        "unicode": "1f4a2"
    },
    ":angola:": {
        "category": "Flags",
        "name": "flag: Angola",
        "unicode": "1f1e6-1f1f4"
    },
    ":angry:": {
        "category": "Smileys & Emotion",
        "name": "angry face",
        "unicode": "1f620"
    },
    ":anguilla:": {
        "category": "Flags",
        "name": "flag: Anguilla",
        "unicode": "1f1e6-1f1ee"
    },
    ":anguished:": {
        "category": "Smileys & Emotion",
        "name": "anguished face",
        "unicode": "1f627"
    },
    ":ant:": {
        "category": "Animals & Nature",
        "name": "ant",
        "unicode": "1f41c"
    },
    ":antarctica:": {
        "category": "Flags",
        "name": "flag: Antarctica",
        "unicode": "1f1e6-1f1f6"
    },
    ":antigua_barbuda:": {
        "category": "Flags",
        "name": "flag: Antigua & Barbuda",
        "unicode": "1f1e6-1f1ec"
    },
    ":apple:": {
        "category": "Food & Drink",
        "name": "red apple",
        "unicode": "1f34e"
    },
    ":aquarius:": {
        "category": "Symbols",
        "name": "Aquarius",
        "unicode": "2652"
    },
    ":argentina:": {
        "category": "Flags",
        "name": "flag: Argentina",
        "unicode": "1f1e6-1f1f7"
    },
    ":aries:": {
        "category": "Symbols",
        "name": "Aries",
        "unicode": "2648"
    },
    ":armenia:": {
        "category": "Flags",
        "name": "flag: Armenia",
        "unicode": "1f1e6-1f1f2"
    },
    ":arrow_backward:": {
        "category": "Symbols",
        "name": "reverse button",
        "unicode": "25c0",
        "unicode_alt": "25c0-fe0f"
    },
    ":arrow_double_down:": {
        "category": "Symbols",
        "name": "fast down button",
        "unicode": "23ec"
    },
    ":arrow_double_up:": {
        "category": "Symbols",
        "name": "fast up button",
        "unicode": "23eb"
    },
    ":arrow_down:": {
        "category": "Symbols",
        "name": "down arrow",
        "unicode": "2b07",
        "unicode_alt": "2b07-fe0f"
    },
    ":arrow_down_small:": {
        "category": "Symbols",
        "name": "downwards button",
        "unicode": "1f53d"
    },
    ":arrow_forward:": {
        "category": "Symbols",
        "name": "play button",
        "unicode": "25b6",
        "unicode_alt": "25b6-fe0f"
    },
    ":arrow_heading_down:": {
        "category": "Symbols",
        "name": "right arrow curving down",
        "unicode": "2935",
        "unicode_alt": "2935-fe0f"
    },
    ":arrow_heading_up:": {
        "category": "Symbols",
        "name": "right arrow curving up",
        "unicode": "2934",
        "unicode_alt": "2934-fe0f"
    },
    ":arrow_left:": {
        "category": "Symbols",
        "name": "left arrow",
        "unicode": "2b05",
        "unicode_alt": "2b05-fe0f"
    },
    ":arrow_lower_left:": {
        "category": "Symbols",
        "name": "down-left arrow",
        "unicode": "2199",
        "unicode_alt": "2199-fe0f"
    },
    ":arrow_lower_right:": {
        "category": "Symbols",
        "name": "down-right arrow",
        "unicode": "2198",
        "unicode_alt": "2198-fe0f"
    },
    ":arrow_right:": {
        "category": "Symbols",
        "name": "right arrow",
        "unicode": "27a1",
        "unicode_alt": "27a1-fe0f"
    },
    ":arrow_right_hook:": {
        "category": "Symbols",
        "name": "left arrow curving right",
        "unicode": "21aa",
        "unicode_alt": "21aa-fe0f"
    },
    ":arrow_up:": {
        "category": "Symbols",
        "name": "up arrow",
        "unicode": "2b06",
        "unicode_alt": "2b06-fe0f"
    },
    ":arrow_up_down:": {
        "category": "Symbols",
        "name": "up-down arrow",
        "unicode": "2195",
        "unicode_alt": "2195-fe0f"
    },
    ":arrow_up_small:": {
        "category": "Symbols",
        "name": "upwards button",
        "unicode": "1f53c"
    },
    ":arrow_upper_left:": {
        "category": "Symbols",
        "name": "up-left arrow",
        "unicode": "2196",
        "unicode_alt": "2196-fe0f"
    },
    ":arrow_upper_right:": {
        "category": "Symbols",
        "name": "up-right arrow",
        "unicode": "2197",
        "unicode_alt": "2197-fe0f"
    },
    ":arrows_clockwise:": {
        "category": "Symbols",
        "name": "clockwise vertical arrows",
        "unicode": "1f503"
    },
    ":arrows_counterclockwise:": {
        "category": "Symbols",
        "name": "counterclockwise arrows button",
        "unicode": "1f504"
    },
    ":art:": {
        "category": "Activities",
        "name": "artist palette",
        "unicode": "1f3a8"
    },
    ":articulated_lorry:": {
        "category": "Travel & Places",
        "name": "articulated lorry",
        "unicode": "1f69b"
    },
    ":artificial_satellite:": {
        "category": "Travel & Places",
        "name": "satellite",
        "unicode": "1f6f0",
        "unicode_alt": "1f6f0-fe0f"
    },
    ":artist:": {
        "category": "People & Body",
        "name": "artist",
        "unicode": "1f9d1-1f3a8",
        "unicode_alt": "1f9d1-200d-1f3a8"
    },
    ":aruba:": {
        "category": "Flags",
        "name": "flag: Aruba",
        "unicode": "1f1e6-1f1fc"
    },
    ":ascension_island:": {
        "category": "Flags",
        "name": "flag: Ascension Island",
        "unicode": "1f1e6-1f1e8"
    },
    ":asterisk:": {
        "category": "Symbols",
        "name": "keycap: *",
        "unicode": "002a-20e3",
        "unicode_alt": "002a-fe0f-20e3"
    },
    ":astonished:": {
        "category": "Smileys & Emotion",
        "name": "astonished face",
        "unicode": "1f632"
    },
    ":astronaut:": {
        "category": "People & Body",
        "name": "astronaut",
        "unicode": "1f9d1-1f680",
        "unicode_alt": "1f9d1-200d-1f680"
    },
    ":athletic_shoe:": {
        "category": "Objects",
        "name": "running shoe",
        "unicode": "1f45f"
    },
    ":atm:": {
        "category": "Symbols",
        "name": "ATM sign",
        "unicode": "1f3e7"
    },
    ":atom_symbol:": {
        "category": "Symbols",
        "name": "atom symbol",
        "unicode": "269b",
        "unicode_alt": "269b-fe0f"
    },
    ":australia:": {
        "category": "Flags",
        "name": "flag: Australia",
        "unicode": "1f1e6-1f1fa"
    },
    ":austria:": {
        "category": "Flags",
        "name": "flag: Austria",
        "unicode": "1f1e6-1f1f9"
    },
    ":auto_rickshaw:": {
        "category": "Travel & Places",
        "name": "auto rickshaw",
        "unicode": "1f6fa"
    },
    ":avocado:": {
        "category": "Food & Drink",
        "name": "avocado",
        "unicode": "1f951"
    },
    ":axe:": {
        "category": "Objects",
        "name": "axe",
        "unicode": "1fa93"
    },
    ":azerbaijan:": {
        "category": "Flags",
        "name": "flag: Azerbaijan",
        "unicode": "1f1e6-1f1ff"
    },
    ":b:": {
        "category": "Symbols",
        "name": "B button (blood type)",
        "unicode": "1f171",
        "unicode_alt": "1f171-fe0f"
    },
    ":baby:": {
        "category": "People & Body",
        "name": "baby",
        "unicode": "1f476"
    },
    ":baby_bottle:": {
        "category": "Food & Drink",
        "name": "baby bottle",
        "unicode": "1f37c"
    },
    ":baby_chick:": {
        "category": "Animals & Nature",
        "name": "baby chick",
        "unicode": "1f424"
    },
    ":baby_symbol:": {
        "category": "Symbols",
        "name": "baby symbol",
        "unicode": "1f6bc"
    },
    ":back:": {
        "category": "Symbols",
        "name": "BACK arrow",
        "unicode": "1f519"
    },
    ":bacon:": {
        "category": "Food & Drink",
        "name": "bacon",
        "unicode": "1f953"
    },
    ":badger:": {
        "category": "Animals & Nature",
        "name": "badger",
        "unicode": "1f9a1"
    },
    ":badminton:": {
        "category": "Activities",
        "name": "badminton",
        "unicode": "1f3f8"
    },
    ":bagel:": {
        "category": "Food & Drink",
        "name": "bagel",
        "unicode": "1f96f"
    },
    ":baggage_claim:": {
        "category": "Symbols",
        "name": "baggage claim",
        "unicode": "1f6c4"
    },
    ":baguette_bread:": {
        "category": "Food & Drink",
        "name": "baguette bread",
        "unicode": "1f956"
    },
    ":bahamas:": {
        "category": "Flags",
        "name": "flag: Bahamas",
        "unicode": "1f1e7-1f1f8"
    },
    ":bahrain:": {
        "category": "Flags",
        "name": "flag: Bahrain",
        "unicode": "1f1e7-1f1ed"
    },
    ":balance_scale:": {
        "category": "Objects",
        "name": "balance scale",
        "unicode": "2696",
        "unicode_alt": "2696-fe0f"
    },
    ":bald_man:": {
        "category": "People & Body",
        "name": "man: bald",
        "unicode": "1f468-1f9b2",
        "unicode_alt": "1f468-200d-1f9b2"
    },
    ":bald_woman:": {
        "category": "People & Body",
        "name": "woman: bald",
        "unicode": "1f469-1f9b2",
        "unicode_alt": "1f469-200d-1f9b2"
    },
    ":ballet_shoes:": {
        "category": "Objects",
        "name": "ballet shoes",
        "unicode": "1fa70"
    },
    ":balloon:": {
        "category": "Activities",
        "name": "balloon",
        "unicode": "1f388"
    },
    ":ballot_box:": {
        "category": "Objects",
        "name": "ballot box with ballot",
        "unicode": "1f5f3",
        "unicode_alt": "1f5f3-fe0f"
    },
    ":ballot_box_with_check:": {
        "category": "Symbols",
        "name": "check box with check",
        "unicode": "2611",
        "unicode_alt": "2611-fe0f"
    },
    ":bamboo:": {
        "category": "Activities",
        "name": "pine decoration",
        "unicode": "1f38d"
    },
    ":banana:": {
        "category": "Food & Drink",
        "name": "banana",
        "unicode": "1f34c"
    },
    ":bangbang:": {
        "category": "Symbols",
        "name": "double exclamation mark",
        "unicode": "203c",
        "unicode_alt": "203c-fe0f"
    },
    ":bangladesh:": {
        "category": "Flags",
        "name": "flag: Bangladesh",
        "unicode": "1f1e7-1f1e9"
    },
    ":banjo:": {
        "category": "Objects",
        "name": "banjo",
        "unicode": "1fa95"
    },
    ":bank:": {
        "category": "Travel & Places",
        "name": "bank",
        "unicode": "1f3e6"
    },
    ":bar_chart:": {
        "category": "Objects",
        "name": "bar chart",
        "unicode": "1f4ca"
    },
    ":barbados:": {
        "category": "Flags",
        "name": "flag: Barbados",
        "unicode": "1f1e7-1f1e7"
    },
    ":barber:": {
        "category": "Travel & Places",
        "name": "barber pole",
        "unicode": "1f488"
    },
    ":baseball:": {
        "category": "Activities",
        "name": "baseball",
        "unicode": "26be"
    },
    ":basket:": {
        "category": "Objects",
        "name": "basket",
        "unicode": "1f9fa"
    },
    ":basketball:": {
        "category": "Activities",
        "name": "basketball",
        "unicode": "1f3c0"
    },
    ":bat:": {
        "category": "Animals & Nature",
        "name": "bat",
        "unicode": "1f987"
    },
    ":bath:": {
        "category": "People & Body",
        "name": "person taking bath",
        "unicode": "1f6c0"
    },
    ":bathtub:": {
        "category": "Objects",
        "name": "bathtub",
        "unicode": "1f6c1"
    },
    ":battery:": {
        "category": "Objects",
        "name": "battery",
        "unicode": "1f50b"
    },
    ":beach_umbrella:": {
        "category": "Travel & Places",
        "name": "beach with umbrella",
        "unicode": "1f3d6",
        "unicode_alt": "1f3d6-fe0f"
    },
    ":beans:": {
        "category": "Food & Drink",
        "name": "beans",
        "unicode": "1fad8"
    },
    ":bear:": {
        "category": "Animals & Nature",
        "name": "bear",
        "unicode": "1f43b"
    },
    ":bearded_person:": {
        "category": "People & Body",
        "name": "person: beard",
        "unicode": "1f9d4"
    },
    ":beaver:": {
        "category": "Animals & Nature",
        "name": "beaver",
        "unicode": "1f9ab"
    },
    ":bed:": {
        "category": "Objects",
        "name": "bed",
        "unicode": "1f6cf",
        "unicode_alt": "1f6cf-fe0f"
    },
    ":bee:": {
        "category": "Animals & Nature",
        "name": "honeybee",
        "unicode": "1f41d"
    },
    ":beer:": {
        "category": "Food & Drink",
        "name": "beer mug",
        "unicode": "1f37a"
    },
    ":beers:": {
        "category": "Food & Drink",
        "name": "clinking beer mugs",
        "unicode": "1f37b"
    },
    ":beetle:": {
        "category": "Animals & Nature",
        "name": "beetle",
        "unicode": "1fab2"
    },
    ":beginner:": {
        "category": "Symbols",
        "name": "Japanese symbol for beginner",
        "unicode": "1f530"
    },
    ":belarus:": {
        "category": "Flags",
        "name": "flag: Belarus",
        "unicode": "1f1e7-1f1fe"
    },
    ":belgium:": {
        "category": "Flags",
        "name": "flag: Belgium",
        "unicode": "1f1e7-1f1ea"
    },
    ":belize:": {
        "category": "Flags",
        "name": "flag: Belize",
        "unicode": "1f1e7-1f1ff"
    },
    ":bell:": {
        "category": "Objects",
        "name": "bell",
        "unicode": "1f514"
    },
    ":bell_pepper:": {
        "category": "Food & Drink",
        "name": "bell pepper",
        "unicode": "1fad1"
    },
    ":bellhop_bell:": {
        "category": "Travel & Places",
        "name": "bellhop bell",
        "unicode": "1f6ce",
        "unicode_alt": "1f6ce-fe0f"
    },
    ":benin:": {
        "category": "Flags",
        "name": "flag: Benin",
        "unicode": "1f1e7-1f1ef"
    },
    ":bento:": {
        "category": "Food & Drink",
        "name": "bento box",
        "unicode": "1f371"
    },
    ":bermuda:": {
        "category": "Flags",
        "name": "flag: Bermuda",
        "unicode": "1f1e7-1f1f2"
    },
    ":beverage_box:": {
        "category": "Food & Drink",
        "name": "beverage box",
        "unicode": "1f9c3"
    },
    ":bhutan:": {
        "category": "Flags",
        "name": "flag: Bhutan",
        "unicode": "1f1e7-1f1f9"
    },
    ":bicyclist:": {
        "category": "People & Body",
        "name": "person biking",
        "unicode": "1f6b4"
    },
    ":bike:": {
        "category": "Travel & Places",
        "name": "bicycle",
        "unicode": "1f6b2"
    },
    ":biking_man:": {
        "category": "People & Body",
        "name": "man biking",
        "unicode": "1f6b4-2642",
        "unicode_alt": "1f6b4-200d-2642-fe0f"
    },
    ":biking_woman:": {
        "category": "People & Body",
        "name": "woman biking",
        "unicode": "1f6b4-2640",
        "unicode_alt": "1f6b4-200d-2640-fe0f"
    },
    ":bikini:": {
        "category": "Objects",
        "name": "bikini",
        "unicode": "1f459"
    },
    ":billed_cap:": {
        "category": "Objects",
        "name": "billed cap",
        "unicode": "1f9e2"
    },
    ":biohazard:": {
        "category": "Symbols",
        "name": "biohazard",
        "unicode": "2623",
        "unicode_alt": "2623-fe0f"
    },
    ":bird:": {
        "category": "Animals & Nature",
        "name": "bird",
        "unicode": "1f426"
    },
    ":birthday:": {
        "category": "Food & Drink",
        "name": "birthday cake",
        "unicode": "1f382"
    },
    ":bison:": {
        "category": "Animals & Nature",
        "name": "bison",
        "unicode": "1f9ac"
    },
    ":biting_lip:": {
        "category": "People & Body",
        "name": "biting lip",
        "unicode": "1fae6"
    },
    ":black_bird:": {
        "category": "Animals & Nature",
        "name": "black bird",
        "unicode": "1f426-2b1b",
        "unicode_alt": "1f426-200d-2b1b"
    },
    ":black_cat:": {
        "category": "Animals & Nature",
        "name": "black cat",
        "unicode": "1f408-2b1b",
        "unicode_alt": "1f408-200d-2b1b"
    },
    ":black_circle:": {
        "category": "Symbols",
        "name": "black circle",
        "unicode": "26ab"
    },
    ":black_flag:": {
        "category": "Flags",
        "name": "black flag",
        "unicode": "1f3f4"
    },
    ":black_heart:": {
        "category": "Smileys & Emotion",
        "name": "black heart",
        "unicode": "1f5a4"
    },
    ":black_joker:": {
        "category": "Activities",
        "name": "joker",
        "unicode": "1f0cf"
    },
    ":black_large_square:": {
        "category": "Symbols",
        "name": "black large square",
        "unicode": "2b1b"
    },
    ":black_medium_small_square:": {
        "category": "Symbols",
        "name": "black medium-small square",
        "unicode": "25fe"
    },
    ":black_medium_square:": {
        "category": "Symbols",
        "name": "black medium square",
        "unicode": "25fc",
        "unicode_alt": "25fc-fe0f"
    },
    ":black_nib:": {
        "category": "Objects",
        "name": "black nib",
        "unicode": "2712",
        "unicode_alt": "2712-fe0f"
    },
    ":black_small_square:": {
        "category": "Symbols",
        "name": "black small square",
        "unicode": "25aa",
        "unicode_alt": "25aa-fe0f"
    },
    ":black_square_button:": {
        "category": "Symbols",
        "name": "black square button",
        "unicode": "1f532"
    },
    ":blond_haired_man:": {
        "category": "People & Body",
        "name": "man: blond hair",
        "unicode": "1f471-2642",
        "unicode_alt": "1f471-200d-2642-fe0f"
    },
    ":blond_haired_person:": {
        "category": "People & Body",
        "name": "person: blond hair",
        "unicode": "1f471"
    },
    ":blond_haired_woman:": {
        "category": "People & Body",
        "name": "woman: blond hair",
        "unicode": "1f471-2640",
        "unicode_alt": "1f471-200d-2640-fe0f"
    },
    ":blossom:": {
        "category": "Animals & Nature",
        "name": "blossom",
        "unicode": "1f33c"
    },
    ":blowfish:": {
        "category": "Animals & Nature",
        "name": "blowfish",
        "unicode": "1f421"
    },
    ":blue_book:": {
        "category": "Objects",
        "name": "blue book",
        "unicode": "1f4d8"
    },
    ":blue_car:": {
        "category": "Travel & Places",
        "name": "sport utility vehicle",
        "unicode": "1f699"
    },
    ":blue_heart:": {
        "category": "Smileys & Emotion",
        "name": "blue heart",
        "unicode": "1f499"
    },
    ":blue_square:": {
        "category": "Symbols",
        "name": "blue square",
        "unicode": "1f7e6"
    },
    ":blueberries:": {
        "category": "Food & Drink",
        "name": "blueberries",
        "unicode": "1fad0"
    },
    ":blush:": {
        "category": "Smileys & Emotion",
        "name": "smiling face with smiling eyes",
        "unicode": "1f60a"
    },
    ":boar:": {
        "category": "Animals & Nature",
        "name": "boar",
        "unicode": "1f417"
    },
    ":boat:": {
        "category": "Travel & Places",
        "name": "sailboat",
        "unicode": "26f5"
    },
    ":bolivia:": {
        "category": "Flags",
        "name": "flag: Bolivia",
        "unicode": "1f1e7-1f1f4"
    },
    ":bomb:": {
        "category": "Objects",
        "name": "bomb",
        "unicode": "1f4a3"
    },
    ":bone:": {
        "category": "People & Body",
        "name": "bone",
        "unicode": "1f9b4"
    },
    ":book:": {
        "category": "Objects",
        "name": "open book",
        "unicode": "1f4d6"
    },
    ":bookmark:": {
        "category": "Objects",
        "name": "bookmark",
        "unicode": "1f516"
    },
    ":bookmark_tabs:": {
        "category": "Objects",
        "name": "bookmark tabs",
        "unicode": "1f4d1"
    },
    ":books:": {
        "category": "Objects",
        "name": "books",
        "unicode": "1f4da"
    },
    ":boom:": {
        "category": "Smileys & Emotion",
        "name": "collision",
        "unicode": "1f4a5"
    },
    ":boomerang:": {
        "category": "Objects",
        "name": "boomerang",
        "unicode": "1fa83"
    },
    ":boot:": {
        "category": "Objects",
        "name": "woman\u2019s boot",
        "unicode": "1f462"
    },
    ":bosnia_herzegovina:": {
        "category": "Flags",
        "name": "flag: Bosnia & Herzegovina",
        "unicode": "1f1e7-1f1e6"
    },
    ":botswana:": {
        "category": "Flags",
        "name": "flag: Botswana",
        "unicode": "1f1e7-1f1fc"
    },
    ":bouncing_ball_man:": {
        "category": "People & Body",
        "name": "man bouncing ball",
        "unicode": "26f9-2642",
        "unicode_alt": "26f9-fe0f-200d-2642-fe0f"
    },
    ":bouncing_ball_person:": {
        "category": "People & Body",
        "name": "person bouncing ball",
        "unicode": "26f9",
        "unicode_alt": "26f9-fe0f"
    },
    ":bouncing_ball_woman:": {
        "category": "People & Body",
        "name": "woman bouncing ball",
        "unicode": "26f9-2640",
        "unicode_alt": "26f9-fe0f-200d-2640-fe0f"
    },
    ":bouquet:": {
        "category": "Animals & Nature",
        "name": "bouquet",
        "unicode": "1f490"
    },
    ":bouvet_island:": {
        "category": "Flags",
        "name": "flag: Bouvet Island",
        "unicode": "1f1e7-1f1fb"
    },
    ":bow:": {
        "category": "People & Body",
        "name": "person bowing",
        "unicode": "1f647"
    },
    ":bow_and_arrow:": {
        "category": "Objects",
        "name": "bow and arrow",
        "unicode": "1f3f9"
    },
    ":bowing_man:": {
        "category": "People & Body",
        "name": "man bowing",
        "unicode": "1f647-2642",
        "unicode_alt": "1f647-200d-2642-fe0f"
    },
    ":bowing_woman:": {
        "category": "People & Body",
        "name": "woman bowing",
        "unicode": "1f647-2640",
        "unicode_alt": "1f647-200d-2640-fe0f"
    },
    ":bowl_with_spoon:": {
        "category": "Food & Drink",
        "name": "bowl with spoon",
        "unicode": "1f963"
    },
    ":bowling:": {
        "category": "Activities",
        "name": "bowling",
        "unicode": "1f3b3"
    },
    ":boxing_glove:": {
        "category": "Activities",
        "name": "boxing glove",
        "unicode": "1f94a"
    },
    ":boy:": {
        "category": "People & Body",
        "name": "boy",
        "unicode": "1f466"
    },
    ":brain:": {
        "category": "People & Body",
        "name": "brain",
        "unicode": "1f9e0"
    },
    ":brazil:": {
        "category": "Flags",
        "name": "flag: Brazil",
        "unicode": "1f1e7-1f1f7"
    },
    ":bread:": {
        "category": "Food & Drink",
        "name": "bread",
        "unicode": "1f35e"
    },
    ":breast_feeding:": {
        "category": "People & Body",
        "name": "breast-feeding",
        "unicode": "1f931"
    },
    ":bricks:": {
        "category": "Travel & Places",
        "name": "brick",
        "unicode": "1f9f1"
    },
    ":bridge_at_night:": {
        "category": "Travel & Places",
        "name": "bridge at night",
        "unicode": "1f309"
    },
    ":briefcase:": {
        "category": "Objects",
        "name": "briefcase",
        "unicode": "1f4bc"
    },
    ":british_indian_ocean_territory:": {
        "category": "Flags",
        "name": "flag: British Indian Ocean Territory",
        "unicode": "1f1ee-1f1f4"
    },
    ":british_virgin_islands:": {
        "category": "Flags",
        "name": "flag: British Virgin Islands",
        "unicode": "1f1fb-1f1ec"
    },
    ":broccoli:": {
        "category": "Food & Drink",
        "name": "broccoli",
        "unicode": "1f966"
    },
    ":broken_heart:": {
        "category": "Smileys & Emotion",
        "name": "broken heart",
        "unicode": "1f494"
    },
    ":broom:": {
        "category": "Objects",
        "name": "broom",
        "unicode": "1f9f9"
    },
    ":brown_circle:": {
        "category": "Symbols",
        "name": "brown circle",
        "unicode": "1f7e4"
    },
    ":brown_heart:": {
        "category": "Smileys & Emotion",
        "name": "brown heart",
        "unicode": "1f90e"
    },
    ":brown_square:": {
        "category": "Symbols",
        "name": "brown square",
        "unicode": "1f7eb"
    },
    ":brunei:": {
        "category": "Flags",
        "name": "flag: Brunei",
        "unicode": "1f1e7-1f1f3"
    },
    ":bubble_tea:": {
        "category": "Food & Drink",
        "name": "bubble tea",
        "unicode": "1f9cb"
    },
    ":bubbles:": {
        "category": "Objects",
        "name": "bubbles",
        "unicode": "1fae7"
    },
    ":bucket:": {
        "category": "Objects",
        "name": "bucket",
        "unicode": "1faa3"
    },
    ":bug:": {
        "category": "Animals & Nature",
        "name": "bug",
        "unicode": "1f41b"
    },
    ":building_construction:": {
        "category": "Travel & Places",
        "name": "building construction",
        "unicode": "1f3d7",
        "unicode_alt": "1f3d7-fe0f"
    },
    ":bulb:": {
        "category": "Objects",
        "name": "light bulb",
        "unicode": "1f4a1"
    },
    ":bulgaria:": {
        "category": "Flags",
        "name": "flag: Bulgaria",
        "unicode": "1f1e7-1f1ec"
    },
    ":bullettrain_front:": {
        "category": "Travel & Places",
        "name": "bullet train",
        "unicode": "1f685"
    },
    ":bullettrain_side:": {
        "category": "Travel & Places",
        "name": "high-speed train",
        "unicode": "1f684"
    },
    ":burkina_faso:": {
        "category": "Flags",
        "name": "flag: Burkina Faso",
        "unicode": "1f1e7-1f1eb"
    },
    ":burrito:": {
        "category": "Food & Drink",
        "name": "burrito",
        "unicode": "1f32f"
    },
    ":burundi:": {
        "category": "Flags",
        "name": "flag: Burundi",
        "unicode": "1f1e7-1f1ee"
    },
    ":bus:": {
        "category": "Travel & Places",
        "name": "bus",
        "unicode": "1f68c"
    },
    ":business_suit_levitating:": {
        "category": "People & Body",
        "name": "person in suit levitating",
        "unicode": "1f574",
        "unicode_alt": "1f574-fe0f"
    },
    ":busstop:": {
        "category": "Travel & Places",
        "name": "bus stop",
        "unicode": "1f68f"
    },
    ":bust_in_silhouette:": {
        "category": "People & Body",
        "name": "bust in silhouette",
        "unicode": "1f464"
    },
    ":busts_in_silhouette:": {
        "category": "People & Body",
        "name": "busts in silhouette",
        "unicode": "1f465"
    },
    ":butter:": {
        "category": "Food & Drink",
        "name": "butter",
        "unicode": "1f9c8"
    },
    ":butterfly:": {
        "category": "Animals & Nature",
        "name": "butterfly",
        "unicode": "1f98b"
    },
    ":cactus:": {
        "category": "Animals & Nature",
        "name": "cactus",
        "unicode": "1f335"
    },
    ":cake:": {
        "category": "Food & Drink",
        "name": "shortcake",
        "unicode": "1f370"
    },
    ":calendar:": {
        "category": "Objects",
        "name": "tear-off calendar",
        "unicode": "1f4c6"
    },
    ":call_me_hand:": {
        "category": "People & Body",
        "name": "call me hand",
        "unicode": "1f919"
    },
    ":calling:": {
        "category": "Objects",
        "name": "mobile phone with arrow",
        "unicode": "1f4f2"
    },
    ":cambodia:": {
        "category": "Flags",
        "name": "flag: Cambodia",
        "unicode": "1f1f0-1f1ed"
    },
    ":camel:": {
        "category": "Animals & Nature",
        "name": "two-hump camel",
        "unicode": "1f42b"
    },
    ":camera:": {
        "category": "Objects",
        "name": "camera",
        "unicode": "1f4f7"
    },
    ":camera_flash:": {
        "category": "Objects",
        "name": "camera with flash",
        "unicode": "1f4f8"
    },
    ":cameroon:": {
        "category": "Flags",
        "name": "flag: Cameroon",
        "unicode": "1f1e8-1f1f2"
    },
    ":camping:": {
        "category": "Travel & Places",
        "name": "camping",
        "unicode": "1f3d5",
        "unicode_alt": "1f3d5-fe0f"
    },
    ":canada:": {
        "category": "Flags",
        "name": "flag: Canada",
        "unicode": "1f1e8-1f1e6"
    },
    ":canary_islands:": {
        "category": "Flags",
        "name": "flag: Canary Islands",
        "unicode": "1f1ee-1f1e8"
    },
    ":cancer:": {
        "category": "Symbols",
        "name": "Cancer",
        "unicode": "264b"
    },
    ":candle:": {
        "category": "Objects",
        "name": "candle",
        "unicode": "1f56f",
        "unicode_alt": "1f56f-fe0f"
    },
    ":candy:": {
        "category": "Food & Drink",
        "name": "candy",
        "unicode": "1f36c"
    },
    ":canned_food:": {
        "category": "Food & Drink",
        "name": "canned food",
        "unicode": "1f96b"
    },
    ":canoe:": {
        "category": "Travel & Places",
        "name": "canoe",
        "unicode": "1f6f6"
    },
    ":cape_verde:": {
        "category": "Flags",
        "name": "flag: Cape Verde",
        "unicode": "1f1e8-1f1fb"
    },
    ":capital_abcd:": {
        "category": "Symbols",
        "name": "input latin uppercase",
        "unicode": "1f520"
    },
    ":capricorn:": {
        "category": "Symbols",
        "name": "Capricorn",
        "unicode": "2651"
    },
    ":car:": {
        "category": "Travel & Places",
        "name": "automobile",
        "unicode": "1f697"
    },
    ":card_file_box:": {
        "category": "Objects",
        "name": "card file box",
        "unicode": "1f5c3",
        "unicode_alt": "1f5c3-fe0f"
    },
    ":card_index:": {
        "category": "Objects",
        "name": "card index",
        "unicode": "1f4c7"
    },
    ":card_index_dividers:": {
        "category": "Objects",
        "name": "card index dividers",
        "unicode": "1f5c2",
        "unicode_alt": "1f5c2-fe0f"
    },
    ":caribbean_netherlands:": {
        "category": "Flags",
        "name": "flag: Caribbean Netherlands",
        "unicode": "1f1e7-1f1f6"
    },
    ":carousel_horse:": {
        "category": "Travel & Places",
        "name": "carousel horse",
        "unicode": "1f3a0"
    },
    ":carpentry_saw:": {
        "category": "Objects",
        "name": "carpentry saw",
        "unicode": "1fa9a"
    },
    ":carrot:": {
        "category": "Food & Drink",
        "name": "carrot",
        "unicode": "1f955"
    },
    ":cartwheeling:": {
        "category": "People & Body",
        "name": "person cartwheeling",
        "unicode": "1f938"
    },
    ":cat2:": {
        "category": "Animals & Nature",
        "name": "cat",
        "unicode": "1f408"
    },
    ":cat:": {
        "category": "Animals & Nature",
        "name": "cat face",
        "unicode": "1f431"
    },
    ":cayman_islands:": {
        "category": "Flags",
        "name": "flag: Cayman Islands",
        "unicode": "1f1f0-1f1fe"
    },
    ":cd:": {
        "category": "Objects",
        "name": "optical disk",
        "unicode": "1f4bf"
    },
    ":central_african_republic:": {
        "category": "Flags",
        "name": "flag: Central African Republic",
        "unicode": "1f1e8-1f1eb"
    },
    ":ceuta_melilla:": {
        "category": "Flags",
        "name": "flag: Ceuta & Melilla",
        "unicode": "1f1ea-1f1e6"
    },
    ":chad:": {
        "category": "Flags",
        "name": "flag: Chad",
        "unicode": "1f1f9-1f1e9"
    },
    ":chains:": {
        "category": "Objects",
        "name": "chains",
        "unicode": "26d3",
        "unicode_alt": "26d3-fe0f"
    },
    ":chair:": {
        "category": "Objects",
        "name": "chair",
        "unicode": "1fa91"
    },
    ":champagne:": {
        "category": "Food & Drink",
        "name": "bottle with popping cork",
        "unicode": "1f37e"
    },
    ":chart:": {
        "category": "Objects",
        "name": "chart increasing with yen",
        "unicode": "1f4b9"
    },
    ":chart_with_downwards_trend:": {
        "category": "Objects",
        "name": "chart decreasing",
        "unicode": "1f4c9"
    },
    ":chart_with_upwards_trend:": {
        "category": "Objects",
        "name": "chart increasing",
        "unicode": "1f4c8"
    },
    ":checkered_flag:": {
        "category": "Flags",
        "name": "chequered flag",
        "unicode": "1f3c1"
    },
    ":cheese:": {
        "category": "Food & Drink",
        "name": "cheese wedge",
        "unicode": "1f9c0"
    },
    ":cherries:": {
        "category": "Food & Drink",
        "name": "cherries",
        "unicode": "1f352"
    },
    ":cherry_blossom:": {
        "category": "Animals & Nature",
        "name": "cherry blossom",
        "unicode": "1f338"
    },
    ":chess_pawn:": {
        "category": "Activities",
        "name": "chess pawn",
        "unicode": "265f",
        "unicode_alt": "265f-fe0f"
    },
    ":chestnut:": {
        "category": "Food & Drink",
        "name": "chestnut",
        "unicode": "1f330"
    },
    ":chicken:": {
        "category": "Animals & Nature",
        "name": "chicken",
        "unicode": "1f414"
    },
    ":child:": {
        "category": "People & Body",
        "name": "child",
        "unicode": "1f9d2"
    },
    ":children_crossing:": {
        "category": "Symbols",
        "name": "children crossing",
        "unicode": "1f6b8"
    },
    ":chile:": {
        "category": "Flags",
        "name": "flag: Chile",
        "unicode": "1f1e8-1f1f1"
    },
    ":chipmunk:": {
        "category": "Animals & Nature",
        "name": "chipmunk",
        "unicode": "1f43f",
        "unicode_alt": "1f43f-fe0f"
    },
    ":chocolate_bar:": {
        "category": "Food & Drink",
        "name": "chocolate bar",
        "unicode": "1f36b"
    },
    ":chopsticks:": {
        "category": "Food & Drink",
        "name": "chopsticks",
        "unicode": "1f962"
    },
    ":christmas_island:": {
        "category": "Flags",
        "name": "flag: Christmas Island",
        "unicode": "1f1e8-1f1fd"
    },
    ":christmas_tree:": {
        "category": "Activities",
        "name": "Christmas tree",
        "unicode": "1f384"
    },
    ":church:": {
        "category": "Travel & Places",
        "name": "church",
        "unicode": "26ea"
    },
    ":cinema:": {
        "category": "Symbols",
        "name": "cinema",
        "unicode": "1f3a6"
    },
    ":circus_tent:": {
        "category": "Travel & Places",
        "name": "circus tent",
        "unicode": "1f3aa"
    },
    ":city_sunrise:": {
        "category": "Travel & Places",
        "name": "sunset",
        "unicode": "1f307"
    },
    ":city_sunset:": {
        "category": "Travel & Places",
        "name": "cityscape at dusk",
        "unicode": "1f306"
    },
    ":cityscape:": {
        "category": "Travel & Places",
        "name": "cityscape",
        "unicode": "1f3d9",
        "unicode_alt": "1f3d9-fe0f"
    },
    ":cl:": {
        "category": "Symbols",
        "name": "CL button",
        "unicode": "1f191"
    },
    ":clamp:": {
        "category": "Objects",
        "name": "clamp",
        "unicode": "1f5dc",
        "unicode_alt": "1f5dc-fe0f"
    },
    ":clap:": {
        "category": "People & Body",
        "name": "clapping hands",
        "unicode": "1f44f"
    },
    ":clapper:": {
        "category": "Objects",
        "name": "clapper board",
        "unicode": "1f3ac"
    },
    ":classical_building:": {
        "category": "Travel & Places",
        "name": "classical building",
        "unicode": "1f3db",
        "unicode_alt": "1f3db-fe0f"
    },
    ":climbing:": {
        "category": "People & Body",
        "name": "person climbing",
        "unicode": "1f9d7"
    },
    ":climbing_man:": {
        "category": "People & Body",
        "name": "man climbing",
        "unicode": "1f9d7-2642",
        "unicode_alt": "1f9d7-200d-2642-fe0f"
    },
    ":climbing_woman:": {
        "category": "People & Body",
        "name": "woman climbing",
        "unicode": "1f9d7-2640",
        "unicode_alt": "1f9d7-200d-2640-fe0f"
    },
    ":clinking_glasses:": {
        "category": "Food & Drink",
        "name": "clinking glasses",
        "unicode": "1f942"
    },
    ":clipboard:": {
        "category": "Objects",
        "name": "clipboard",
        "unicode": "1f4cb"
    },
    ":clipperton_island:": {
        "category": "Flags",
        "name": "flag: Clipperton Island",
        "unicode": "1f1e8-1f1f5"
    },
    ":clock1030:": {
        "category": "Travel & Places",
        "name": "ten-thirty",
        "unicode": "1f565"
    },
    ":clock10:": {
        "category": "Travel & Places",
        "name": "ten o\u2019clock",
        "unicode": "1f559"
    },
    ":clock1130:": {
        "category": "Travel & Places",
        "name": "eleven-thirty",
        "unicode": "1f566"
    },
    ":clock11:": {
        "category": "Travel & Places",
        "name": "eleven o\u2019clock",
        "unicode": "1f55a"
    },
    ":clock1230:": {
        "category": "Travel & Places",
        "name": "twelve-thirty",
        "unicode": "1f567"
    },
    ":clock12:": {
        "category": "Travel & Places",
        "name": "twelve o\u2019clock",
        "unicode": "1f55b"
    },
    ":clock130:": {
        "category": "Travel & Places",
        "name": "one-thirty",
        "unicode": "1f55c"
    },
    ":clock1:": {
        "category": "Travel & Places",
        "name": "one o\u2019clock",
        "unicode": "1f550"
    },
    ":clock230:": {
        "category": "Travel & Places",
        "name": "two-thirty",
        "unicode": "1f55d"
    },
    ":clock2:": {
        "category": "Travel & Places",
        "name": "two o\u2019clock",
        "unicode": "1f551"
    },
    ":clock330:": {
        "category": "Travel & Places",
        "name": "three-thirty",
        "unicode": "1f55e"
    },
    ":clock3:": {
        "category": "Travel & Places",
        "name": "three o\u2019clock",
        "unicode": "1f552"
    },
    ":clock430:": {
        "category": "Travel & Places",
        "name": "four-thirty",
        "unicode": "1f55f"
    },
    ":clock4:": {
        "category": "Travel & Places",
        "name": "four o\u2019clock",
        "unicode": "1f553"
    },
    ":clock530:": {
        "category": "Travel & Places",
        "name": "five-thirty",
        "unicode": "1f560"
    },
    ":clock5:": {
        "category": "Travel & Places",
        "name": "five o\u2019clock",
        "unicode": "1f554"
    },
    ":clock630:": {
        "category": "Travel & Places",
        "name": "six-thirty",
        "unicode": "1f561"
    },
    ":clock6:": {
        "category": "Travel & Places",
        "name": "six o\u2019clock",
        "unicode": "1f555"
    },
    ":clock730:": {
        "category": "Travel & Places",
        "name": "seven-thirty",
        "unicode": "1f562"
    },
    ":clock7:": {
        "category": "Travel & Places",
        "name": "seven o\u2019clock",
        "unicode": "1f556"
    },
    ":clock830:": {
        "category": "Travel & Places",
        "name": "eight-thirty",
        "unicode": "1f563"
    },
    ":clock8:": {
        "category": "Travel & Places",
        "name": "eight o\u2019clock",
        "unicode": "1f557"
    },
    ":clock930:": {
        "category": "Travel & Places",
        "name": "nine-thirty",
        "unicode": "1f564"
    },
    ":clock9:": {
        "category": "Travel & Places",
        "name": "nine o\u2019clock",
        "unicode": "1f558"
    },
    ":closed_book:": {
        "category": "Objects",
        "name": "closed book",
        "unicode": "1f4d5"
    },
    ":closed_lock_with_key:": {
        "category": "Objects",
        "name": "locked with key",
        "unicode": "1f510"
    },
    ":closed_umbrella:": {
        "category": "Travel & Places",
        "name": "closed umbrella",
        "unicode": "1f302"
    },
    ":cloud:": {
        "category": "Travel & Places",
        "name": "cloud",
        "unicode": "2601",
        "unicode_alt": "2601-fe0f"
    },
    ":cloud_with_lightning:": {
        "category": "Travel & Places",
        "name": "cloud with lightning",
        "unicode": "1f329",
        "unicode_alt": "1f329-fe0f"
    },
    ":cloud_with_lightning_and_rain:": {
        "category": "Travel & Places",
        "name": "cloud with lightning and rain",
        "unicode": "26c8",
        "unicode_alt": "26c8-fe0f"
    },
    ":cloud_with_rain:": {
        "category": "Travel & Places",
        "name": "cloud with rain",
        "unicode": "1f327",
        "unicode_alt": "1f327-fe0f"
    },
    ":cloud_with_snow:": {
        "category": "Travel & Places",
        "name": "cloud with snow",
        "unicode": "1f328",
        "unicode_alt": "1f328-fe0f"
    },
    ":clown_face:": {
        "category": "Smileys & Emotion",
        "name": "clown face",
        "unicode": "1f921"
    },
    ":clubs:": {
        "category": "Activities",
        "name": "club suit",
        "unicode": "2663",
        "unicode_alt": "2663-fe0f"
    },
    ":cn:": {
        "category": "Flags",
        "name": "flag: China",
        "unicode": "1f1e8-1f1f3"
    },
    ":coat:": {
        "category": "Objects",
        "name": "coat",
        "unicode": "1f9e5"
    },
    ":cockroach:": {
        "category": "Animals & Nature",
        "name": "cockroach",
        "unicode": "1fab3"
    },
    ":cocktail:": {
        "category": "Food & Drink",
        "name": "cocktail glass",
        "unicode": "1f378"
    },
    ":coconut:": {
        "category": "Food & Drink",
        "name": "coconut",
        "unicode": "1f965"
    },
    ":cocos_islands:": {
        "category": "Flags",
        "name": "flag: Cocos (Keeling) Islands",
        "unicode": "1f1e8-1f1e8"
    },
    ":coffee:": {
        "category": "Food & Drink",
        "name": "hot beverage",
        "unicode": "2615"
    },
    ":coffin:": {
        "category": "Objects",
        "name": "coffin",
        "unicode": "26b0",
        "unicode_alt": "26b0-fe0f"
    },
    ":coin:": {
        "category": "Objects",
        "name": "coin",
        "unicode": "1fa99"
    },
    ":cold_face:": {
        "category": "Smileys & Emotion",
        "name": "cold face",
        "unicode": "1f976"
    },
    ":cold_sweat:": {
        "category": "Smileys & Emotion",
        "name": "anxious face with sweat",
        "unicode": "1f630"
    },
    ":colombia:": {
        "category": "Flags",
        "name": "flag: Colombia",
        "unicode": "1f1e8-1f1f4"
    },
    ":comet:": {
        "category": "Travel & Places",
        "name": "comet",
        "unicode": "2604",
        "unicode_alt": "2604-fe0f"
    },
    ":comoros:": {
        "category": "Flags",
        "name": "flag: Comoros",
        "unicode": "1f1f0-1f1f2"
    },
    ":compass:": {
        "category": "Travel & Places",
        "name": "compass",
        "unicode": "1f9ed"
    },
    ":computer:": {
        "category": "Objects",
        "name": "laptop",
        "unicode": "1f4bb"
    },
    ":computer_mouse:": {
        "category": "Objects",
        "name": "computer mouse",
        "unicode": "1f5b1",
        "unicode_alt": "1f5b1-fe0f"
    },
    ":confetti_ball:": {
        "category": "Activities",
        "name": "confetti ball",
        "unicode": "1f38a"
    },
    ":confounded:": {
        "category": "Smileys & Emotion",
        "name": "confounded face",
        "unicode": "1f616"
    },
    ":confused:": {
        "category": "Smileys & Emotion",
        "name": "confused face",
        "unicode": "1f615"
    },
    ":congo_brazzaville:": {
        "category": "Flags",
        "name": "flag: Congo - Brazzaville",
        "unicode": "1f1e8-1f1ec"
    },
    ":congo_kinshasa:": {
        "category": "Flags",
        "name": "flag: Congo - Kinshasa",
        "unicode": "1f1e8-1f1e9"
    },
    ":congratulations:": {
        "category": "Symbols",
        "name": "Japanese \u201ccongratulations\u201d button",
        "unicode": "3297",
        "unicode_alt": "3297-fe0f"
    },
    ":construction:": {
        "category": "Travel & Places",
        "name": "construction",
        "unicode": "1f6a7"
    },
    ":construction_worker:": {
        "category": "People & Body",
        "name": "construction worker",
        "unicode": "1f477"
    },
    ":construction_worker_man:": {
        "category": "People & Body",
        "name": "man construction worker",
        "unicode": "1f477-2642",
        "unicode_alt": "1f477-200d-2642-fe0f"
    },
    ":construction_worker_woman:": {
        "category": "People & Body",
        "name": "woman construction worker",
        "unicode": "1f477-2640",
        "unicode_alt": "1f477-200d-2640-fe0f"
    },
    ":control_knobs:": {
        "category": "Objects",
        "name": "control knobs",
        "unicode": "1f39b",
        "unicode_alt": "1f39b-fe0f"
    },
    ":convenience_store:": {
        "category": "Travel & Places",
        "name": "convenience store",
        "unicode": "1f3ea"
    },
    ":cook:": {
        "category": "People & Body",
        "name": "cook",
        "unicode": "1f9d1-1f373",
        "unicode_alt": "1f9d1-200d-1f373"
    },
    ":cook_islands:": {
        "category": "Flags",
        "name": "flag: Cook Islands",
        "unicode": "1f1e8-1f1f0"
    },
    ":cookie:": {
        "category": "Food & Drink",
        "name": "cookie",
        "unicode": "1f36a"
    },
    ":cool:": {
        "category": "Symbols",
        "name": "COOL button",
        "unicode": "1f192"
    },
    ":copyright:": {
        "category": "Symbols",
        "name": "copyright",
        "unicode": "00a9",
        "unicode_alt": "00a9-fe0f"
    },
    ":coral:": {
        "category": "Animals & Nature",
        "name": "coral",
        "unicode": "1fab8"
    },
    ":corn:": {
        "category": "Food & Drink",
        "name": "ear of corn",
        "unicode": "1f33d"
    },
    ":costa_rica:": {
        "category": "Flags",
        "name": "flag: Costa Rica",
        "unicode": "1f1e8-1f1f7"
    },
    ":cote_divoire:": {
        "category": "Flags",
        "name": "flag: C\u00f4te d\u2019Ivoire",
        "unicode": "1f1e8-1f1ee"
    },
    ":couch_and_lamp:": {
        "category": "Objects",
        "name": "couch and lamp",
        "unicode": "1f6cb",
        "unicode_alt": "1f6cb-fe0f"
    },
    ":couple:": {
        "category": "People & Body",
        "name": "woman and man holding hands",
        "unicode": "1f46b"
    },
    ":couple_with_heart:": {
        "category": "People & Body",
        "name": "couple with heart",
        "unicode": "1f491"
    },
    ":couple_with_heart_man_man:": {
        "category": "People & Body",
        "name": "couple with heart: man, man",
        "unicode": "1f468-2764-1f468",
        "unicode_alt": "1f468-200d-2764-fe0f-200d-1f468"
    },
    ":couple_with_heart_woman_man:": {
        "category": "People & Body",
        "name": "couple with heart: woman, man",
        "unicode": "1f469-2764-1f468",
        "unicode_alt": "1f469-200d-2764-fe0f-200d-1f468"
    },
    ":couple_with_heart_woman_woman:": {
        "category": "People & Body",
        "name": "couple with heart: woman, woman",
        "unicode": "1f469-2764-1f469",
        "unicode_alt": "1f469-200d-2764-fe0f-200d-1f469"
    },
    ":couplekiss:": {
        "category": "People & Body",
        "name": "kiss",
        "unicode": "1f48f"
    },
    ":couplekiss_man_man:": {
        "category": "People & Body",
        "name": "kiss: man, man",
        "unicode": "1f468-2764-1f48b-1f468",
        "unicode_alt": "1f468-200d-2764-fe0f-200d-1f48b-200d-1f468"
    },
    ":couplekiss_man_woman:": {
        "category": "People & Body",
        "name": "kiss: woman, man",
        "unicode": "1f469-2764-1f48b-1f468",
        "unicode_alt": "1f469-200d-2764-fe0f-200d-1f48b-200d-1f468"
    },
    ":couplekiss_woman_woman:": {
        "category": "People & Body",
        "name": "kiss: woman, woman",
        "unicode": "1f469-2764-1f48b-1f469",
        "unicode_alt": "1f469-200d-2764-fe0f-200d-1f48b-200d-1f469"
    },
    ":cow2:": {
        "category": "Animals & Nature",
        "name": "cow",
        "unicode": "1f404"
    },
    ":cow:": {
        "category": "Animals & Nature",
        "name": "cow face",
        "unicode": "1f42e"
    },
    ":cowboy_hat_face:": {
        "category": "Smileys & Emotion",
        "name": "cowboy hat face",
        "unicode": "1f920"
    },
    ":crab:": {
        "category": "Food & Drink",
        "name": "crab",
        "unicode": "1f980"
    },
    ":crayon:": {
        "category": "Objects",
        "name": "crayon",
        "unicode": "1f58d",
        "unicode_alt": "1f58d-fe0f"
    },
    ":credit_card:": {
        "category": "Objects",
        "name": "credit card",
        "unicode": "1f4b3"
    },
    ":crescent_moon:": {
        "category": "Travel & Places",
        "name": "crescent moon",
        "unicode": "1f319"
    },
    ":cricket:": {
        "category": "Animals & Nature",
        "name": "cricket",
        "unicode": "1f997"
    },
    ":cricket_game:": {
        "category": "Activities",
        "name": "cricket game",
        "unicode": "1f3cf"
    },
    ":croatia:": {
        "category": "Flags",
        "name": "flag: Croatia",
        "unicode": "1f1ed-1f1f7"
    },
    ":crocodile:": {
        "category": "Animals & Nature",
        "name": "crocodile",
        "unicode": "1f40a"
    },
    ":croissant:": {
        "category": "Food & Drink",
        "name": "croissant",
        "unicode": "1f950"
    },
    ":crossed_fingers:": {
        "category": "People & Body",
        "name": "crossed fingers",
        "unicode": "1f91e"
    },
    ":crossed_flags:": {
        "category": "Flags",
        "name": "crossed flags",
        "unicode": "1f38c"
    },
    ":crossed_swords:": {
        "category": "Objects",
        "name": "crossed swords",
        "unicode": "2694",
        "unicode_alt": "2694-fe0f"
    },
    ":crown:": {
        "category": "Objects",
        "name": "crown",
        "unicode": "1f451"
    },
    ":crutch:": {
        "category": "Objects",
        "name": "crutch",
        "unicode": "1fa7c"
    },
    ":cry:": {
        "category": "Smileys & Emotion",
        "name": "crying face",
        "unicode": "1f622"
    },
    ":crying_cat_face:": {
        "category": "Smileys & Emotion",
        "name": "crying cat",
        "unicode": "1f63f"
    },
    ":crystal_ball:": {
        "category": "Activities",
        "name": "crystal ball",
        "unicode": "1f52e"
    },
    ":cuba:": {
        "category": "Flags",
        "name": "flag: Cuba",
        "unicode": "1f1e8-1f1fa"
    },
    ":cucumber:": {
        "category": "Food & Drink",
        "name": "cucumber",
        "unicode": "1f952"
    },
    ":cup_with_straw:": {
        "category": "Food & Drink",
        "name": "cup with straw",
        "unicode": "1f964"
    },
    ":cupcake:": {
        "category": "Food & Drink",
        "name": "cupcake",
        "unicode": "1f9c1"
    },
    ":cupid:": {
        "category": "Smileys & Emotion",
        "name": "heart with arrow",
        "unicode": "1f498"
    },
    ":curacao:": {
        "category": "Flags",
        "name": "flag: Cura\u00e7ao",
        "unicode": "1f1e8-1f1fc"
    },
    ":curling_stone:": {
        "category": "Activities",
        "name": "curling stone",
        "unicode": "1f94c"
    },
    ":curly_haired_man:": {
        "category": "People & Body",
        "name": "man: curly hair",
        "unicode": "1f468-1f9b1",
        "unicode_alt": "1f468-200d-1f9b1"
    },
    ":curly_haired_woman:": {
        "category": "People & Body",
        "name": "woman: curly hair",
        "unicode": "1f469-1f9b1",
        "unicode_alt": "1f469-200d-1f9b1"
    },
    ":curly_loop:": {
        "category": "Symbols",
        "name": "curly loop",
        "unicode": "27b0"
    },
    ":currency_exchange:": {
        "category": "Symbols",
        "name": "currency exchange",
        "unicode": "1f4b1"
    },
    ":curry:": {
        "category": "Food & Drink",
        "name": "curry rice",
        "unicode": "1f35b"
    },
    ":cursing_face:": {
        "category": "Smileys & Emotion",
        "name": "face with symbols on mouth",
        "unicode": "1f92c"
    },
    ":custard:": {
        "category": "Food & Drink",
        "name": "custard",
        "unicode": "1f36e"
    },
    ":customs:": {
        "category": "Symbols",
        "name": "customs",
        "unicode": "1f6c3"
    },
    ":cut_of_meat:": {
        "category": "Food & Drink",
        "name": "cut of meat",
        "unicode": "1f969"
    },
    ":cyclone:": {
        "category": "Travel & Places",
        "name": "cyclone",
        "unicode": "1f300"
    },
    ":cyprus:": {
        "category": "Flags",
        "name": "flag: Cyprus",
        "unicode": "1f1e8-1f1fe"
    },
    ":czech_republic:": {
        "category": "Flags",
        "name": "flag: Czechia",
        "unicode": "1f1e8-1f1ff"
    },
    ":dagger:": {
        "category": "Objects",
        "name": "dagger",
        "unicode": "1f5e1",
        "unicode_alt": "1f5e1-fe0f"
    },
    ":dancers:": {
        "category": "People & Body",
        "name": "people with bunny ears",
        "unicode": "1f46f"
    },
    ":dancing_men:": {
        "category": "People & Body",
        "name": "men with bunny ears",
        "unicode": "1f46f-2642",
        "unicode_alt": "1f46f-200d-2642-fe0f"
    },
    ":dancing_women:": {
        "category": "People & Body",
        "name": "women with bunny ears",
        "unicode": "1f46f-2640",
        "unicode_alt": "1f46f-200d-2640-fe0f"
    },
    ":dango:": {
        "category": "Food & Drink",
        "name": "dango",
        "unicode": "1f361"
    },
    ":dark_sunglasses:": {
        "category": "Objects",
        "name": "sunglasses",
        "unicode": "1f576",
        "unicode_alt": "1f576-fe0f"
    },
    ":dart:": {
        "category": "Activities",
        "name": "bullseye",
        "unicode": "1f3af"
    },
    ":dash:": {
        "category": "Smileys & Emotion",
        "name": "dashing away",
        "unicode": "1f4a8"
    },
    ":date:": {
        "category": "Objects",
        "name": "calendar",
        "unicode": "1f4c5"
    },
    ":de:": {
        "category": "Flags",
        "name": "flag: Germany",
        "unicode": "1f1e9-1f1ea"
    },
    ":deaf_man:": {
        "category": "People & Body",
        "name": "deaf man",
        "unicode": "1f9cf-2642",
        "unicode_alt": "1f9cf-200d-2642-fe0f"
    },
    ":deaf_person:": {
        "category": "People & Body",
        "name": "deaf person",
        "unicode": "1f9cf"
    },
    ":deaf_woman:": {
        "category": "People & Body",
        "name": "deaf woman",
        "unicode": "1f9cf-2640",
        "unicode_alt": "1f9cf-200d-2640-fe0f"
    },
    ":deciduous_tree:": {
        "category": "Animals & Nature",
        "name": "deciduous tree",
        "unicode": "1f333"
    },
    ":deer:": {
        "category": "Animals & Nature",
        "name": "deer",
        "unicode": "1f98c"
    },
    ":denmark:": {
        "category": "Flags",
        "name": "flag: Denmark",
        "unicode": "1f1e9-1f1f0"
    },
    ":department_store:": {
        "category": "Travel & Places",
        "name": "department store",
        "unicode": "1f3ec"
    },
    ":derelict_house:": {
        "category": "Travel & Places",
        "name": "derelict house",
        "unicode": "1f3da",
        "unicode_alt": "1f3da-fe0f"
    },
    ":desert:": {
        "category": "Travel & Places",
        "name": "desert",
        "unicode": "1f3dc",
        "unicode_alt": "1f3dc-fe0f"
    },
    ":desert_island:": {
        "category": "Travel & Places",
        "name": "desert island",
        "unicode": "1f3dd",
        "unicode_alt": "1f3dd-fe0f"
    },
    ":desktop_computer:": {
        "category": "Objects",
        "name": "desktop computer",
        "unicode": "1f5a5",
        "unicode_alt": "1f5a5-fe0f"
    },
    ":detective:": {
        "category": "People & Body",
        "name": "detective",
        "unicode": "1f575",
        "unicode_alt": "1f575-fe0f"
    },
    ":diamond_shape_with_a_dot_inside:": {
        "category": "Symbols",
        "name": "diamond with a dot",
        "unicode": "1f4a0"
    },
    ":diamonds:": {
        "category": "Activities",
        "name": "diamond suit",
        "unicode": "2666",
        "unicode_alt": "2666-fe0f"
    },
    ":diego_garcia:": {
        "category": "Flags",
        "name": "flag: Diego Garcia",
        "unicode": "1f1e9-1f1ec"
    },
    ":disappointed:": {
        "category": "Smileys & Emotion",
        "name": "disappointed face",
        "unicode": "1f61e"
    },
    ":disappointed_relieved:": {
        "category": "Smileys & Emotion",
        "name": "sad but relieved face",
        "unicode": "1f625"
    },
    ":disguised_face:": {
        "category": "Smileys & Emotion",
        "name": "disguised face",
        "unicode": "1f978"
    },
    ":diving_mask:": {
        "category": "Activities",
        "name": "diving mask",
        "unicode": "1f93f"
    },
    ":diya_lamp:": {
        "category": "Objects",
        "name": "diya lamp",
        "unicode": "1fa94"
    },
    ":dizzy:": {
        "category": "Smileys & Emotion",
        "name": "dizzy",
        "unicode": "1f4ab"
    },
    ":dizzy_face:": {
        "category": "Smileys & Emotion",
        "name": "face with crossed-out eyes",
        "unicode": "1f635"
    },
    ":djibouti:": {
        "category": "Flags",
        "name": "flag: Djibouti",
        "unicode": "1f1e9-1f1ef"
    },
    ":dna:": {
        "category": "Objects",
        "name": "dna",
        "unicode": "1f9ec"
    },
    ":do_not_litter:": {
        "category": "Symbols",
        "name": "no littering",
        "unicode": "1f6af"
    },
    ":dodo:": {
        "category": "Animals & Nature",
        "name": "dodo",
        "unicode": "1f9a4"
    },
    ":dog2:": {
        "category": "Animals & Nature",
        "name": "dog",
        "unicode": "1f415"
    },
    ":dog:": {
        "category": "Animals & Nature",
        "name": "dog face",
        "unicode": "1f436"
    },
    ":dollar:": {
        "category": "Objects",
        "name": "dollar banknote",
        "unicode": "1f4b5"
    },
    ":dolls:": {
        "category": "Activities",
        "name": "Japanese dolls",
        "unicode": "1f38e"
    },
    ":dolphin:": {
        "category": "Animals & Nature",
        "name": "dolphin",
        "unicode": "1f42c"
    },
    ":dominica:": {
        "category": "Flags",
        "name": "flag: Dominica",
        "unicode": "1f1e9-1f1f2"
    },
    ":dominican_republic:": {
        "category": "Flags",
        "name": "flag: Dominican Republic",
        "unicode": "1f1e9-1f1f4"
    },
    ":donkey:": {
        "category": "Animals & Nature",
        "name": "donkey",
        "unicode": "1facf"
    },
    ":door:": {
        "category": "Objects",
        "name": "door",
        "unicode": "1f6aa"
    },
    ":dotted_line_face:": {
        "category": "Smileys & Emotion",
        "name": "dotted line face",
        "unicode": "1fae5"
    },
    ":doughnut:": {
        "category": "Food & Drink",
        "name": "doughnut",
        "unicode": "1f369"
    },
    ":dove:": {
        "category": "Animals & Nature",
        "name": "dove",
        "unicode": "1f54a",
        "unicode_alt": "1f54a-fe0f"
    },
    ":dragon:": {
        "category": "Animals & Nature",
        "name": "dragon",
        "unicode": "1f409"
    },
    ":dragon_face:": {
        "category": "Animals & Nature",
        "name": "dragon face",
        "unicode": "1f432"
    },
    ":dress:": {
        "category": "Objects",
        "name": "dress",
        "unicode": "1f457"
    },
    ":dromedary_camel:": {
        "category": "Animals & Nature",
        "name": "camel",
        "unicode": "1f42a"
    },
    ":drooling_face:": {
        "category": "Smileys & Emotion",
        "name": "drooling face",
        "unicode": "1f924"
    },
    ":drop_of_blood:": {
        "category": "Objects",
        "name": "drop of blood",
        "unicode": "1fa78"
    },
    ":droplet:": {
        "category": "Travel & Places",
        "name": "droplet",
        "unicode": "1f4a7"
    },
    ":drum:": {
        "category": "Objects",
        "name": "drum",
        "unicode": "1f941"
    },
    ":duck:": {
        "category": "Animals & Nature",
        "name": "duck",
        "unicode": "1f986"
    },
    ":dumpling:": {
        "category": "Food & Drink",
        "name": "dumpling",
        "unicode": "1f95f"
    },
    ":dvd:": {
        "category": "Objects",
        "name": "dvd",
        "unicode": "1f4c0"
    },
    ":eagle:": {
        "category": "Animals & Nature",
        "name": "eagle",
        "unicode": "1f985"
    },
    ":ear:": {
        "category": "People & Body",
        "name": "ear",
        "unicode": "1f442"
    },
    ":ear_of_rice:": {
        "category": "Animals & Nature",
        "name": "sheaf of rice",
        "unicode": "1f33e"
    },
    ":ear_with_hearing_aid:": {
        "category": "People & Body",
        "name": "ear with hearing aid",
        "unicode": "1f9bb"
    },
    ":earth_africa:": {
        "category": "Travel & Places",
        "name": "globe showing Europe-Africa",
        "unicode": "1f30d"
    },
    ":earth_americas:": {
        "category": "Travel & Places",
        "name": "globe showing Americas",
        "unicode": "1f30e"
    },
    ":earth_asia:": {
        "category": "Travel & Places",
        "name": "globe showing Asia-Australia",
        "unicode": "1f30f"
    },
    ":ecuador:": {
        "category": "Flags",
        "name": "flag: Ecuador",
        "unicode": "1f1ea-1f1e8"
    },
    ":egg:": {
        "category": "Food & Drink",
        "name": "egg",
        "unicode": "1f95a"
    },
    ":eggplant:": {
        "category": "Food & Drink",
        "name": "eggplant",
        "unicode": "1f346"
    },
    ":egypt:": {
        "category": "Flags",
        "name": "flag: Egypt",
        "unicode": "1f1ea-1f1ec"
    },
    ":eight:": {
        "category": "Symbols",
        "name": "keycap: 8",
        "unicode": "0038-20e3",
        "unicode_alt": "0038-fe0f-20e3"
    },
    ":eight_pointed_black_star:": {
        "category": "Symbols",
        "name": "eight-pointed star",
        "unicode": "2734",
        "unicode_alt": "2734-fe0f"
    },
    ":eight_spoked_asterisk:": {
        "category": "Symbols",
        "name": "eight-spoked asterisk",
        "unicode": "2733",
        "unicode_alt": "2733-fe0f"
    },
    ":eject_button:": {
        "category": "Symbols",
        "name": "eject button",
        "unicode": "23cf",
        "unicode_alt": "23cf-fe0f"
    },
    ":el_salvador:": {
        "category": "Flags",
        "name": "flag: El Salvador",
        "unicode": "1f1f8-1f1fb"
    },
    ":electric_plug:": {
        "category": "Objects",
        "name": "electric plug",
        "unicode": "1f50c"
    },
    ":elephant:": {
        "category": "Animals & Nature",
        "name": "elephant",
        "unicode": "1f418"
    },
    ":elevator:": {
        "category": "Objects",
        "name": "elevator",
        "unicode": "1f6d7"
    },
    ":elf:": {
        "category": "People & Body",
        "name": "elf",
        "unicode": "1f9dd"
    },
    ":elf_man:": {
        "category": "People & Body",
        "name": "man elf",
        "unicode": "1f9dd-2642",
        "unicode_alt": "1f9dd-200d-2642-fe0f"
    },
    ":elf_woman:": {
        "category": "People & Body",
        "name": "woman elf",
        "unicode": "1f9dd-2640",
        "unicode_alt": "1f9dd-200d-2640-fe0f"
    },
    ":email:": {
        "category": "Objects",
        "name": "e-mail",
        "unicode": "1f4e7"
    },
    ":empty_nest:": {
        "category": "Animals & Nature",
        "name": "empty nest",
        "unicode": "1fab9"
    },
    ":end:": {
        "category": "Symbols",
        "name": "END arrow",
        "unicode": "1f51a"
    },
    ":england:": {
        "category": "Flags",
        "name": "flag: England",
        "unicode": "1f3f4-e0067-e0062-e0065-e006e-e0067-e007f"
    },
    ":envelope:": {
        "category": "Objects",
        "name": "envelope",
        "unicode": "2709",
        "unicode_alt": "2709-fe0f"
    },
    ":envelope_with_arrow:": {
        "category": "Objects",
        "name": "envelope with arrow",
        "unicode": "1f4e9"
    },
    ":equatorial_guinea:": {
        "category": "Flags",
        "name": "flag: Equatorial Guinea",
        "unicode": "1f1ec-1f1f6"
    },
    ":eritrea:": {
        "category": "Flags",
        "name": "flag: Eritrea",
        "unicode": "1f1ea-1f1f7"
    },
    ":es:": {
        "category": "Flags",
        "name": "flag: Spain",
        "unicode": "1f1ea-1f1f8"
    },
    ":estonia:": {
        "category": "Flags",
        "name": "flag: Estonia",
        "unicode": "1f1ea-1f1ea"
    },
    ":ethiopia:": {
        "category": "Flags",
        "name": "flag: Ethiopia",
        "unicode": "1f1ea-1f1f9"
    },
    ":eu:": {
        "category": "Flags",
        "name": "flag: European Union",
        "unicode": "1f1ea-1f1fa"
    },
    ":euro:": {
        "category": "Objects",
        "name": "euro banknote",
        "unicode": "1f4b6"
    },
    ":european_castle:": {
        "category": "Travel & Places",
        "name": "castle",
        "unicode": "1f3f0"
    },
    ":european_post_office:": {
        "category": "Travel & Places",
        "name": "post office",
        "unicode": "1f3e4"
    },
    ":evergreen_tree:": {
        "category": "Animals & Nature",
        "name": "evergreen tree",
        "unicode": "1f332"
    },
    ":exclamation:": {
        "category": "Symbols",
        "name": "red exclamation mark",
        "unicode": "2757"
    },
    ":exploding_head:": {
        "category": "Smileys & Emotion",
        "name": "exploding head",
        "unicode": "1f92f"
    },
    ":expressionless:": {
        "category": "Smileys & Emotion",
        "name": "expressionless face",
        "unicode": "1f611"
    },
    ":eye:": {
        "category": "People & Body",
        "name": "eye",
        "unicode": "1f441",
        "unicode_alt": "1f441-fe0f"
    },
    ":eye_speech_bubble:": {
        "category": "Smileys & Emotion",
        "name": "eye in speech bubble",
        "unicode": "1f441-1f5e8",
        "unicode_alt": "1f441-fe0f-200d-1f5e8-fe0f"
    },
    ":eyeglasses:": {
        "category": "Objects",
        "name": "glasses",
        "unicode": "1f453"
    },
    ":eyes:": {
        "category": "People & Body",
        "name": "eyes",
        "unicode": "1f440"
    },
    ":face_exhaling:": {
        "category": "Smileys & Emotion",
        "name": "face exhaling",
        "unicode": "1f62e-1f4a8",
        "unicode_alt": "1f62e-200d-1f4a8"
    },
    ":face_holding_back_tears:": {
        "category": "Smileys & Emotion",
        "name": "face holding back tears",
        "unicode": "1f979"
    },
    ":face_in_clouds:": {
        "category": "Smileys & Emotion",
        "name": "face in clouds",
        "unicode": "1f636-1f32b",
        "unicode_alt": "1f636-200d-1f32b-fe0f"
    },
    ":face_with_diagonal_mouth:": {
        "category": "Smileys & Emotion",
        "name": "face with diagonal mouth",
        "unicode": "1fae4"
    },
    ":face_with_head_bandage:": {
        "category": "Smileys & Emotion",
        "name": "face with head-bandage",
        "unicode": "1f915"
    },
    ":face_with_open_eyes_and_hand_over_mouth:": {
        "category": "Smileys & Emotion",
        "name": "face with open eyes and hand over mouth",
        "unicode": "1fae2"
    },
    ":face_with_peeking_eye:": {
        "category": "Smileys & Emotion",
        "name": "face with peeking eye",
        "unicode": "1fae3"
    },
    ":face_with_spiral_eyes:": {
        "category": "Smileys & Emotion",
        "name": "face with spiral eyes",
        "unicode": "1f635-1f4ab",
        "unicode_alt": "1f635-200d-1f4ab"
    },
    ":face_with_thermometer:": {
        "category": "Smileys & Emotion",
        "name": "face with thermometer",
        "unicode": "1f912"
    },
    ":facepalm:": {
        "category": "People & Body",
        "name": "person facepalming",
        "unicode": "1f926"
    },
    ":factory:": {
        "category": "Travel & Places",
        "name": "factory",
        "unicode": "1f3ed"
    },
    ":factory_worker:": {
        "category": "People & Body",
        "name": "factory worker",
        "unicode": "1f9d1-1f3ed",
        "unicode_alt": "1f9d1-200d-1f3ed"
    },
    ":fairy:": {
        "category": "People & Body",
        "name": "fairy",
        "unicode": "1f9da"
    },
    ":fairy_man:": {
        "category": "People & Body",
        "name": "man fairy",
        "unicode": "1f9da-2642",
        "unicode_alt": "1f9da-200d-2642-fe0f"
    },
    ":fairy_woman:": {
        "category": "People & Body",
        "name": "woman fairy",
        "unicode": "1f9da-2640",
        "unicode_alt": "1f9da-200d-2640-fe0f"
    },
    ":falafel:": {
        "category": "Food & Drink",
        "name": "falafel",
        "unicode": "1f9c6"
    },
    ":falkland_islands:": {
        "category": "Flags",
        "name": "flag: Falkland Islands",
        "unicode": "1f1eb-1f1f0"
    },
    ":fallen_leaf:": {
        "category": "Animals & Nature",
        "name": "fallen leaf",
        "unicode": "1f342"
    },
    ":family:": {
        "category": "People & Body",
        "name": "family",
        "unicode": "1f46a"
    },
    ":family_man_boy:": {
        "category": "People & Body",
        "name": "family: man, boy",
        "unicode": "1f468-1f466",
        "unicode_alt": "1f468-200d-1f466"
    },
    ":family_man_boy_boy:": {
        "category": "People & Body",
        "name": "family: man, boy, boy",
        "unicode": "1f468-1f466-1f466",
        "unicode_alt": "1f468-200d-1f466-200d-1f466"
    },
    ":family_man_girl:": {
        "category": "People & Body",
        "name": "family: man, girl",
        "unicode": "1f468-1f467",
        "unicode_alt": "1f468-200d-1f467"
    },
    ":family_man_girl_boy:": {
        "category": "People & Body",
        "name": "family: man, girl, boy",
        "unicode": "1f468-1f467-1f466",
        "unicode_alt": "1f468-200d-1f467-200d-1f466"
    },
    ":family_man_girl_girl:": {
        "category": "People & Body",
        "name": "family: man, girl, girl",
        "unicode": "1f468-1f467-1f467",
        "unicode_alt": "1f468-200d-1f467-200d-1f467"
    },
    ":family_man_man_boy:": {
        "category": "People & Body",
        "name": "family: man, man, boy",
        "unicode": "1f468-1f468-1f466",
        "unicode_alt": "1f468-200d-1f468-200d-1f466"
    },
    ":family_man_man_boy_boy:": {
        "category": "People & Body",
        "name": "family: man, man, boy, boy",
        "unicode": "1f468-1f468-1f466-1f466",
        "unicode_alt": "1f468-200d-1f468-200d-1f466-200d-1f466"
    },
    ":family_man_man_girl:": {
        "category": "People & Body",
        "name": "family: man, man, girl",
        "unicode": "1f468-1f468-1f467",
        "unicode_alt": "1f468-200d-1f468-200d-1f467"
    },
    ":family_man_man_girl_boy:": {
        "category": "People & Body",
        "name": "family: man, man, girl, boy",
        "unicode": "1f468-1f468-1f467-1f466",
        "unicode_alt": "1f468-200d-1f468-200d-1f467-200d-1f466"
    },
    ":family_man_man_girl_girl:": {
        "category": "People & Body",
        "name": "family: man, man, girl, girl",
        "unicode": "1f468-1f468-1f467-1f467",
        "unicode_alt": "1f468-200d-1f468-200d-1f467-200d-1f467"
    },
    ":family_man_woman_boy:": {
        "category": "People & Body",
        "name": "family: man, woman, boy",
        "unicode": "1f468-1f469-1f466",
        "unicode_alt": "1f468-200d-1f469-200d-1f466"
    },
    ":family_man_woman_boy_boy:": {
        "category": "People & Body",
        "name": "family: man, woman, boy, boy",
        "unicode": "1f468-1f469-1f466-1f466",
        "unicode_alt": "1f468-200d-1f469-200d-1f466-200d-1f466"
    },
    ":family_man_woman_girl:": {
        "category": "People & Body",
        "name": "family: man, woman, girl",
        "unicode": "1f468-1f469-1f467",
        "unicode_alt": "1f468-200d-1f469-200d-1f467"
    },
    ":family_man_woman_girl_boy:": {
        "category": "People & Body",
        "name": "family: man, woman, girl, boy",
        "unicode": "1f468-1f469-1f467-1f466",
        "unicode_alt": "1f468-200d-1f469-200d-1f467-200d-1f466"
    },
    ":family_man_woman_girl_girl:": {
        "category": "People & Body",
        "name": "family: man, woman, girl, girl",
        "unicode": "1f468-1f469-1f467-1f467",
        "unicode_alt": "1f468-200d-1f469-200d-1f467-200d-1f467"
    },
    ":family_woman_boy:": {
        "category": "People & Body",
        "name": "family: woman, boy",
        "unicode": "1f469-1f466",
        "unicode_alt": "1f469-200d-1f466"
    },
    ":family_woman_boy_boy:": {
        "category": "People & Body",
        "name": "family: woman, boy, boy",
        "unicode": "1f469-1f466-1f466",
        "unicode_alt": "1f469-200d-1f466-200d-1f466"
    },
    ":family_woman_girl:": {
        "category": "People & Body",
        "name": "family: woman, girl",
        "unicode": "1f469-1f467",
        "unicode_alt": "1f469-200d-1f467"
    },
    ":family_woman_girl_boy:": {
        "category": "People & Body",
        "name": "family: woman, girl, boy",
        "unicode": "1f469-1f467-1f466",
        "unicode_alt": "1f469-200d-1f467-200d-1f466"
    },
    ":family_woman_girl_girl:": {
        "category": "People & Body",
        "name": "family: woman, girl, girl",
        "unicode": "1f469-1f467-1f467",
        "unicode_alt": "1f469-200d-1f467-200d-1f467"
    },
    ":family_woman_woman_boy:": {
        "category": "People & Body",
        "name": "family: woman, woman, boy",
        "unicode": "1f469-1f469-1f466",
        "unicode_alt": "1f469-200d-1f469-200d-1f466"
    },
    ":family_woman_woman_boy_boy:": {
        "category": "People & Body",
        "name": "family: woman, woman, boy, boy",
        "unicode": "1f469-1f469-1f466-1f466",
        "unicode_alt": "1f469-200d-1f469-200d-1f466-200d-1f466"
    },
    ":family_woman_woman_girl:": {
        "category": "People & Body",
        "name": "family: woman, woman, girl",
        "unicode": "1f469-1f469-1f467",
        "unicode_alt": "1f469-200d-1f469-200d-1f467"
    },
    ":family_woman_woman_girl_boy:": {
        "category": "People & Body",
        "name": "family: woman, woman, girl, boy",
        "unicode": "1f469-1f469-1f467-1f466",
        "unicode_alt": "1f469-200d-1f469-200d-1f467-200d-1f466"
    },
    ":family_woman_woman_girl_girl:": {
        "category": "People & Body",
        "name": "family: woman, woman, girl, girl",
        "unicode": "1f469-1f469-1f467-1f467",
        "unicode_alt": "1f469-200d-1f469-200d-1f467-200d-1f467"
    },
    ":farmer:": {
        "category": "People & Body",
        "name": "farmer",
        "unicode": "1f9d1-1f33e",
        "unicode_alt": "1f9d1-200d-1f33e"
    },
    ":faroe_islands:": {
        "category": "Flags",
        "name": "flag: Faroe Islands",
        "unicode": "1f1eb-1f1f4"
    },
    ":fast_forward:": {
        "category": "Symbols",
        "name": "fast-forward button",
        "unicode": "23e9"
    },
    ":fax:": {
        "category": "Objects",
        "name": "fax machine",
        "unicode": "1f4e0"
    },
    ":fearful:": {
        "category": "Smileys & Emotion",
        "name": "fearful face",
        "unicode": "1f628"
    },
    ":feather:": {
        "category": "Animals & Nature",
        "name": "feather",
        "unicode": "1fab6"
    },
    ":feet:": {
        "category": "Animals & Nature",
        "name": "paw prints",
        "unicode": "1f43e"
    },
    ":female_detective:": {
        "category": "People & Body",
        "name": "woman detective",
        "unicode": "1f575-2640",
        "unicode_alt": "1f575-fe0f-200d-2640-fe0f"
    },
    ":female_sign:": {
        "category": "Symbols",
        "name": "female sign",
        "unicode": "2640",
        "unicode_alt": "2640-fe0f"
    },
    ":ferris_wheel:": {
        "category": "Travel & Places",
        "name": "ferris wheel",
        "unicode": "1f3a1"
    },
    ":ferry:": {
        "category": "Travel & Places",
        "name": "ferry",
        "unicode": "26f4",
        "unicode_alt": "26f4-fe0f"
    },
    ":field_hockey:": {
        "category": "Activities",
        "name": "field hockey",
        "unicode": "1f3d1"
    },
    ":fiji:": {
        "category": "Flags",
        "name": "flag: Fiji",
        "unicode": "1f1eb-1f1ef"
    },
    ":file_cabinet:": {
        "category": "Objects",
        "name": "file cabinet",
        "unicode": "1f5c4",
        "unicode_alt": "1f5c4-fe0f"
    },
    ":file_folder:": {
        "category": "Objects",
        "name": "file folder",
        "unicode": "1f4c1"
    },
    ":film_projector:": {
        "category": "Objects",
        "name": "film projector",
        "unicode": "1f4fd",
        "unicode_alt": "1f4fd-fe0f"
    },
    ":film_strip:": {
        "category": "Objects",
        "name": "film frames",
        "unicode": "1f39e",
        "unicode_alt": "1f39e-fe0f"
    },
    ":finland:": {
        "category": "Flags",
        "name": "flag: Finland",
        "unicode": "1f1eb-1f1ee"
    },
    ":fire:": {
        "category": "Travel & Places",
        "name": "fire",
        "unicode": "1f525"
    },
    ":fire_engine:": {
        "category": "Travel & Places",
        "name": "fire engine",
        "unicode": "1f692"
    },
    ":fire_extinguisher:": {
        "category": "Objects",
        "name": "fire extinguisher",
        "unicode": "1f9ef"
    },
    ":firecracker:": {
        "category": "Activities",
        "name": "firecracker",
        "unicode": "1f9e8"
    },
    ":firefighter:": {
        "category": "People & Body",
        "name": "firefighter",
        "unicode": "1f9d1-1f692",
        "unicode_alt": "1f9d1-200d-1f692"
    },
    ":fireworks:": {
        "category": "Activities",
        "name": "fireworks",
        "unicode": "1f386"
    },
    ":first_quarter_moon:": {
        "category": "Travel & Places",
        "name": "first quarter moon",
        "unicode": "1f313"
    },
    ":first_quarter_moon_with_face:": {
        "category": "Travel & Places",
        "name": "first quarter moon face",
        "unicode": "1f31b"
    },
    ":fish:": {
        "category": "Animals & Nature",
        "name": "fish",
        "unicode": "1f41f"
    },
    ":fish_cake:": {
        "category": "Food & Drink",
        "name": "fish cake with swirl",
        "unicode": "1f365"
    },
    ":fishing_pole_and_fish:": {
        "category": "Activities",
        "name": "fishing pole",
        "unicode": "1f3a3"
    },
    ":fist_left:": {
        "category": "People & Body",
        "name": "left-facing fist",
        "unicode": "1f91b"
    },
    ":fist_oncoming:": {
        "category": "People & Body",
        "name": "oncoming fist",
        "unicode": "1f44a"
    },
    ":fist_raised:": {
        "category": "People & Body",
        "name": "raised fist",
        "unicode": "270a"
    },
    ":fist_right:": {
        "category": "People & Body",
        "name": "right-facing fist",
        "unicode": "1f91c"
    },
    ":five:": {
        "category": "Symbols",
        "name": "keycap: 5",
        "unicode": "0035-20e3",
        "unicode_alt": "0035-fe0f-20e3"
    },
    ":flags:": {
        "category": "Activities",
        "name": "carp streamer",
        "unicode": "1f38f"
    },
    ":flamingo:": {
        "category": "Animals & Nature",
        "name": "flamingo",
        "unicode": "1f9a9"
    },
    ":flashlight:": {
        "category": "Objects",
        "name": "flashlight",
        "unicode": "1f526"
    },
    ":flat_shoe:": {
        "category": "Objects",
        "name": "flat shoe",
        "unicode": "1f97f"
    },
    ":flatbread:": {
        "category": "Food & Drink",
        "name": "flatbread",
        "unicode": "1fad3"
    },
    ":fleur_de_lis:": {
        "category": "Symbols",
        "name": "fleur-de-lis",
        "unicode": "269c",
        "unicode_alt": "269c-fe0f"
    },
    ":flight_arrival:": {
        "category": "Travel & Places",
        "name": "airplane arrival",
        "unicode": "1f6ec"
    },
    ":flight_departure:": {
        "category": "Travel & Places",
        "name": "airplane departure",
        "unicode": "1f6eb"
    },
    ":floppy_disk:": {
        "category": "Objects",
        "name": "floppy disk",
        "unicode": "1f4be"
    },
    ":flower_playing_cards:": {
        "category": "Activities",
        "name": "flower playing cards",
        "unicode": "1f3b4"
    },
    ":flushed:": {
        "category": "Smileys & Emotion",
        "name": "flushed face",
        "unicode": "1f633"
    },
    ":flute:": {
        "category": "Objects",
        "name": "flute",
        "unicode": "1fa88"
    },
    ":fly:": {
        "category": "Animals & Nature",
        "name": "fly",
        "unicode": "1fab0"
    },
    ":flying_disc:": {
        "category": "Activities",
        "name": "flying disc",
        "unicode": "1f94f"
    },
    ":flying_saucer:": {
        "category": "Travel & Places",
        "name": "flying saucer",
        "unicode": "1f6f8"
    },
    ":fog:": {
        "category": "Travel & Places",
        "name": "fog",
        "unicode": "1f32b",
        "unicode_alt": "1f32b-fe0f"
    },
    ":foggy:": {
        "category": "Travel & Places",
        "name": "foggy",
        "unicode": "1f301"
    },
    ":folding_hand_fan:": {
        "category": "Objects",
        "name": "folding hand fan",
        "unicode": "1faad"
    },
    ":fondue:": {
        "category": "Food & Drink",
        "name": "fondue",
        "unicode": "1fad5"
    },
    ":foot:": {
        "category": "People & Body",
        "name": "foot",
        "unicode": "1f9b6"
    },
    ":football:": {
        "category": "Activities",
        "name": "american football",
        "unicode": "1f3c8"
    },
    ":footprints:": {
        "category": "People & Body",
        "name": "footprints",
        "unicode": "1f463"
    },
    ":fork_and_knife:": {
        "category": "Food & Drink",
        "name": "fork and knife",
        "unicode": "1f374"
    },
    ":fortune_cookie:": {
        "category": "Food & Drink",
        "name": "fortune cookie",
        "unicode": "1f960"
    },
    ":fountain:": {
        "category": "Travel & Places",
        "name": "fountain",
        "unicode": "26f2"
    },
    ":fountain_pen:": {
        "category": "Objects",
        "name": "fountain pen",
        "unicode": "1f58b",
        "unicode_alt": "1f58b-fe0f"
    },
    ":four:": {
        "category": "Symbols",
        "name": "keycap: 4",
        "unicode": "0034-20e3",
        "unicode_alt": "0034-fe0f-20e3"
    },
    ":four_leaf_clover:": {
        "category": "Animals & Nature",
        "name": "four leaf clover",
        "unicode": "1f340"
    },
    ":fox_face:": {
        "category": "Animals & Nature",
        "name": "fox",
        "unicode": "1f98a"
    },
    ":fr:": {
        "category": "Flags",
        "name": "flag: France",
        "unicode": "1f1eb-1f1f7"
    },
    ":framed_picture:": {
        "category": "Activities",
        "name": "framed picture",
        "unicode": "1f5bc",
        "unicode_alt": "1f5bc-fe0f"
    },
    ":free:": {
        "category": "Symbols",
        "name": "FREE button",
        "unicode": "1f193"
    },
    ":french_guiana:": {
        "category": "Flags",
        "name": "flag: French Guiana",
        "unicode": "1f1ec-1f1eb"
    },
    ":french_polynesia:": {
        "category": "Flags",
        "name": "flag: French Polynesia",
        "unicode": "1f1f5-1f1eb"
    },
    ":french_southern_territories:": {
        "category": "Flags",
        "name": "flag: French Southern Territories",
        "unicode": "1f1f9-1f1eb"
    },
    ":fried_egg:": {
        "category": "Food & Drink",
        "name": "cooking",
        "unicode": "1f373"
    },
    ":fried_shrimp:": {
        "category": "Food & Drink",
        "name": "fried shrimp",
        "unicode": "1f364"
    },
    ":fries:": {
        "category": "Food & Drink",
        "name": "french fries",
        "unicode": "1f35f"
    },
    ":frog:": {
        "category": "Animals & Nature",
        "name": "frog",
        "unicode": "1f438"
    },
    ":frowning:": {
        "category": "Smileys & Emotion",
        "name": "frowning face with open mouth",
        "unicode": "1f626"
    },
    ":frowning_face:": {
        "category": "Smileys & Emotion",
        "name": "frowning face",
        "unicode": "2639",
        "unicode_alt": "2639-fe0f"
    },
    ":frowning_man:": {
        "category": "People & Body",
        "name": "man frowning",
        "unicode": "1f64d-2642",
        "unicode_alt": "1f64d-200d-2642-fe0f"
    },
    ":frowning_person:": {
        "category": "People & Body",
        "name": "person frowning",
        "unicode": "1f64d"
    },
    ":frowning_woman:": {
        "category": "People & Body",
        "name": "woman frowning",
        "unicode": "1f64d-2640",
        "unicode_alt": "1f64d-200d-2640-fe0f"
    },
    ":fuelpump:": {
        "category": "Travel & Places",
        "name": "fuel pump",
        "unicode": "26fd"
    },
    ":full_moon:": {
        "category": "Travel & Places",
        "name": "full moon",
        "unicode": "1f315"
    },
    ":full_moon_with_face:": {
        "category": "Travel & Places",
        "name": "full moon face",
        "unicode": "1f31d"
    },
    ":funeral_urn:": {
        "category": "Objects",
        "name": "funeral urn",
        "unicode": "26b1",
        "unicode_alt": "26b1-fe0f"
    },
    ":gabon:": {
        "category": "Flags",
        "name": "flag: Gabon",
        "unicode": "1f1ec-1f1e6"
    },
    ":gambia:": {
        "category": "Flags",
        "name": "flag: Gambia",
        "unicode": "1f1ec-1f1f2"
    },
    ":game_die:": {
        "category": "Activities",
        "name": "game die",
        "unicode": "1f3b2"
    },
    ":garlic:": {
        "category": "Food & Drink",
        "name": "garlic",
        "unicode": "1f9c4"
    },
    ":gb:": {
        "category": "Flags",
        "name": "flag: United Kingdom",
        "unicode": "1f1ec-1f1e7"
    },
    ":gear:": {
        "category": "Objects",
        "name": "gear",
        "unicode": "2699",
        "unicode_alt": "2699-fe0f"
    },
    ":gem:": {
        "category": "Objects",
        "name": "gem stone",
        "unicode": "1f48e"
    },
    ":gemini:": {
        "category": "Symbols",
        "name": "Gemini",
        "unicode": "264a"
    },
    ":genie:": {
        "category": "People & Body",
        "name": "genie",
        "unicode": "1f9de"
    },
    ":genie_man:": {
        "category": "People & Body",
        "name": "man genie",
        "unicode": "1f9de-2642",
        "unicode_alt": "1f9de-200d-2642-fe0f"
    },
    ":genie_woman:": {
        "category": "People & Body",
        "name": "woman genie",
        "unicode": "1f9de-2640",
        "unicode_alt": "1f9de-200d-2640-fe0f"
    },
    ":georgia:": {
        "category": "Flags",
        "name": "flag: Georgia",
        "unicode": "1f1ec-1f1ea"
    },
    ":ghana:": {
        "category": "Flags",
        "name": "flag: Ghana",
        "unicode": "1f1ec-1f1ed"
    },
    ":ghost:": {
        "category": "Smileys & Emotion",
        "name": "ghost",
        "unicode": "1f47b"
    },
    ":gibraltar:": {
        "category": "Flags",
        "name": "flag: Gibraltar",
        "unicode": "1f1ec-1f1ee"
    },
    ":gift:": {
        "category": "Activities",
        "name": "wrapped gift",
        "unicode": "1f381"
    },
    ":gift_heart:": {
        "category": "Smileys & Emotion",
        "name": "heart with ribbon",
        "unicode": "1f49d"
    },
    ":ginger_root:": {
        "category": "Food & Drink",
        "name": "ginger root",
        "unicode": "1fada"
    },
    ":giraffe:": {
        "category": "Animals & Nature",
        "name": "giraffe",
        "unicode": "1f992"
    },
    ":girl:": {
        "category": "People & Body",
        "name": "girl",
        "unicode": "1f467"
    },
    ":globe_with_meridians:": {
        "category": "Travel & Places",
        "name": "globe with meridians",
        "unicode": "1f310"
    },
    ":gloves:": {
        "category": "Objects",
        "name": "gloves",
        "unicode": "1f9e4"
    },
    ":goal_net:": {
        "category": "Activities",
        "name": "goal net",
        "unicode": "1f945"
    },
    ":goat:": {
        "category": "Animals & Nature",
        "name": "goat",
        "unicode": "1f410"
    },
    ":goggles:": {
        "category": "Objects",
        "name": "goggles",
        "unicode": "1f97d"
    },
    ":golf:": {
        "category": "Activities",
        "name": "flag in hole",
        "unicode": "26f3"
    },
    ":golfing:": {
        "category": "People & Body",
        "name": "person golfing",
        "unicode": "1f3cc",
        "unicode_alt": "1f3cc-fe0f"
    },
    ":golfing_man:": {
        "category": "People & Body",
        "name": "man golfing",
        "unicode": "1f3cc-2642",
        "unicode_alt": "1f3cc-fe0f-200d-2642-fe0f"
    },
    ":golfing_woman:": {
        "category": "People & Body",
        "name": "woman golfing",
        "unicode": "1f3cc-2640",
        "unicode_alt": "1f3cc-fe0f-200d-2640-fe0f"
    },
    ":goose:": {
        "category": "Animals & Nature",
        "name": "goose",
        "unicode": "1fabf"
    },
    ":gorilla:": {
        "category": "Animals & Nature",
        "name": "gorilla",
        "unicode": "1f98d"
    },
    ":grapes:": {
        "category": "Food & Drink",
        "name": "grapes",
        "unicode": "1f347"
    },
    ":greece:": {
        "category": "Flags",
        "name": "flag: Greece",
        "unicode": "1f1ec-1f1f7"
    },
    ":green_apple:": {
        "category": "Food & Drink",
        "name": "green apple",
        "unicode": "1f34f"
    },
    ":green_book:": {
        "category": "Objects",
        "name": "green book",
        "unicode": "1f4d7"
    },
    ":green_circle:": {
        "category": "Symbols",
        "name": "green circle",
        "unicode": "1f7e2"
    },
    ":green_heart:": {
        "category": "Smileys & Emotion",
        "name": "green heart",
        "unicode": "1f49a"
    },
    ":green_salad:": {
        "category": "Food & Drink",
        "name": "green salad",
        "unicode": "1f957"
    },
    ":green_square:": {
        "category": "Symbols",
        "name": "green square",
        "unicode": "1f7e9"
    },
    ":greenland:": {
        "category": "Flags",
        "name": "flag: Greenland",
        "unicode": "1f1ec-1f1f1"
    },
    ":grenada:": {
        "category": "Flags",
        "name": "flag: Grenada",
        "unicode": "1f1ec-1f1e9"
    },
    ":grey_exclamation:": {
        "category": "Symbols",
        "name": "white exclamation mark",
        "unicode": "2755"
    },
    ":grey_heart:": {
        "category": "Smileys & Emotion",
        "name": "grey heart",
        "unicode": "1fa76"
    },
    ":grey_question:": {
        "category": "Symbols",
        "name": "white question mark",
        "unicode": "2754"
    },
    ":grimacing:": {
        "category": "Smileys & Emotion",
        "name": "grimacing face",
        "unicode": "1f62c"
    },
    ":grin:": {
        "category": "Smileys & Emotion",
        "name": "beaming face with smiling eyes",
        "unicode": "1f601"
    },
    ":grinning:": {
        "category": "Smileys & Emotion",
        "name": "grinning face",
        "unicode": "1f600"
    },
    ":guadeloupe:": {
        "category": "Flags",
        "name": "flag: Guadeloupe",
        "unicode": "1f1ec-1f1f5"
    },
    ":guam:": {
        "category": "Flags",
        "name": "flag: Guam",
        "unicode": "1f1ec-1f1fa"
    },
    ":guard:": {
        "category": "People & Body",
        "name": "guard",
        "unicode": "1f482"
    },
    ":guardsman:": {
        "category": "People & Body",
        "name": "man guard",
        "unicode": "1f482-2642",
        "unicode_alt": "1f482-200d-2642-fe0f"
    },
    ":guardswoman:": {
        "category": "People & Body",
        "name": "woman guard",
        "unicode": "1f482-2640",
        "unicode_alt": "1f482-200d-2640-fe0f"
    },
    ":guatemala:": {
        "category": "Flags",
        "name": "flag: Guatemala",
        "unicode": "1f1ec-1f1f9"
    },
    ":guernsey:": {
        "category": "Flags",
        "name": "flag: Guernsey",
        "unicode": "1f1ec-1f1ec"
    },
    ":guide_dog:": {
        "category": "Animals & Nature",
        "name": "guide dog",
        "unicode": "1f9ae"
    },
    ":guinea:": {
        "category": "Flags",
        "name": "flag: Guinea",
        "unicode": "1f1ec-1f1f3"
    },
    ":guinea_bissau:": {
        "category": "Flags",
        "name": "flag: Guinea-Bissau",
        "unicode": "1f1ec-1f1fc"
    },
    ":guitar:": {
        "category": "Objects",
        "name": "guitar",
        "unicode": "1f3b8"
    },
    ":gun:": {
        "category": "Activities",
        "name": "water pistol",
        "unicode": "1f52b"
    },
    ":guyana:": {
        "category": "Flags",
        "name": "flag: Guyana",
        "unicode": "1f1ec-1f1fe"
    },
    ":hair_pick:": {
        "category": "Objects",
        "name": "hair pick",
        "unicode": "1faae"
    },
    ":haircut:": {
        "category": "People & Body",
        "name": "person getting haircut",
        "unicode": "1f487"
    },
    ":haircut_man:": {
        "category": "People & Body",
        "name": "man getting haircut",
        "unicode": "1f487-2642",
        "unicode_alt": "1f487-200d-2642-fe0f"
    },
    ":haircut_woman:": {
        "category": "People & Body",
        "name": "woman getting haircut",
        "unicode": "1f487-2640",
        "unicode_alt": "1f487-200d-2640-fe0f"
    },
    ":haiti:": {
        "category": "Flags",
        "name": "flag: Haiti",
        "unicode": "1f1ed-1f1f9"
    },
    ":hamburger:": {
        "category": "Food & Drink",
        "name": "hamburger",
        "unicode": "1f354"
    },
    ":hammer:": {
        "category": "Objects",
        "name": "hammer",
        "unicode": "1f528"
    },
    ":hammer_and_pick:": {
        "category": "Objects",
        "name": "hammer and pick",
        "unicode": "2692",
        "unicode_alt": "2692-fe0f"
    },
    ":hammer_and_wrench:": {
        "category": "Objects",
        "name": "hammer and wrench",
        "unicode": "1f6e0",
        "unicode_alt": "1f6e0-fe0f"
    },
    ":hamsa:": {
        "category": "Objects",
        "name": "hamsa",
        "unicode": "1faac"
    },
    ":hamster:": {
        "category": "Animals & Nature",
        "name": "hamster",
        "unicode": "1f439"
    },
    ":hand:": {
        "category": "People & Body",
        "name": "raised hand",
        "unicode": "270b"
    },
    ":hand_over_mouth:": {
        "category": "Smileys & Emotion",
        "name": "face with hand over mouth",
        "unicode": "1f92d"
    },
    ":hand_with_index_finger_and_thumb_crossed:": {
        "category": "People & Body",
        "name": "hand with index finger and thumb crossed",
        "unicode": "1faf0"
    },
    ":handbag:": {
        "category": "Objects",
        "name": "handbag",
        "unicode": "1f45c"
    },
    ":handball_person:": {
        "category": "People & Body",
        "name": "person playing handball",
        "unicode": "1f93e"
    },
    ":handshake:": {
        "category": "People & Body",
        "name": "handshake",
        "unicode": "1f91d"
    },
    ":hankey:": {
        "category": "Smileys & Emotion",
        "name": "pile of poo",
        "unicode": "1f4a9"
    },
    ":hash:": {
        "category": "Symbols",
        "name": "keycap: #",
        "unicode": "0023-20e3",
        "unicode_alt": "0023-fe0f-20e3"
    },
    ":hatched_chick:": {
        "category": "Animals & Nature",
        "name": "front-facing baby chick",
        "unicode": "1f425"
    },
    ":hatching_chick:": {
        "category": "Animals & Nature",
        "name": "hatching chick",
        "unicode": "1f423"
    },
    ":headphones:": {
        "category": "Objects",
        "name": "headphone",
        "unicode": "1f3a7"
    },
    ":headstone:": {
        "category": "Objects",
        "name": "headstone",
        "unicode": "1faa6"
    },
    ":health_worker:": {
        "category": "People & Body",
        "name": "health worker",
        "unicode": "1f9d1-2695",
        "unicode_alt": "1f9d1-200d-2695-fe0f"
    },
    ":hear_no_evil:": {
        "category": "Smileys & Emotion",
        "name": "hear-no-evil monkey",
        "unicode": "1f649"
    },
    ":heard_mcdonald_islands:": {
        "category": "Flags",
        "name": "flag: Heard & McDonald Islands",
        "unicode": "1f1ed-1f1f2"
    },
    ":heart:": {
        "category": "Smileys & Emotion",
        "name": "red heart",
        "unicode": "2764",
        "unicode_alt": "2764-fe0f"
    },
    ":heart_decoration:": {
        "category": "Smileys & Emotion",
        "name": "heart decoration",
        "unicode": "1f49f"
    },
    ":heart_eyes:": {
        "category": "Smileys & Emotion",
        "name": "smiling face with heart-eyes",
        "unicode": "1f60d"
    },
    ":heart_eyes_cat:": {
        "category": "Smileys & Emotion",
        "name": "smiling cat with heart-eyes",
        "unicode": "1f63b"
    },
    ":heart_hands:": {
        "category": "People & Body",
        "name": "heart hands",
        "unicode": "1faf6"
    },
    ":heart_on_fire:": {
        "category": "Smileys & Emotion",
        "name": "heart on fire",
        "unicode": "2764-1f525",
        "unicode_alt": "2764-fe0f-200d-1f525"
    },
    ":heartbeat:": {
        "category": "Smileys & Emotion",
        "name": "beating heart",
        "unicode": "1f493"
    },
    ":heartpulse:": {
        "category": "Smileys & Emotion",
        "name": "growing heart",
        "unicode": "1f497"
    },
    ":hearts:": {
        "category": "Activities",
        "name": "heart suit",
        "unicode": "2665",
        "unicode_alt": "2665-fe0f"
    },
    ":heavy_check_mark:": {
        "category": "Symbols",
        "name": "check mark",
        "unicode": "2714",
        "unicode_alt": "2714-fe0f"
    },
    ":heavy_division_sign:": {
        "category": "Symbols",
        "name": "divide",
        "unicode": "2797"
    },
    ":heavy_dollar_sign:": {
        "category": "Symbols",
        "name": "heavy dollar sign",
        "unicode": "1f4b2"
    },
    ":heavy_equals_sign:": {
        "category": "Symbols",
        "name": "heavy equals sign",
        "unicode": "1f7f0"
    },
    ":heavy_heart_exclamation:": {
        "category": "Smileys & Emotion",
        "name": "heart exclamation",
        "unicode": "2763",
        "unicode_alt": "2763-fe0f"
    },
    ":heavy_minus_sign:": {
        "category": "Symbols",
        "name": "minus",
        "unicode": "2796"
    },
    ":heavy_multiplication_x:": {
        "category": "Symbols",
        "name": "multiply",
        "unicode": "2716",
        "unicode_alt": "2716-fe0f"
    },
    ":heavy_plus_sign:": {
        "category": "Symbols",
        "name": "plus",
        "unicode": "2795"
    },
    ":hedgehog:": {
        "category": "Animals & Nature",
        "name": "hedgehog",
        "unicode": "1f994"
    },
    ":helicopter:": {
        "category": "Travel & Places",
        "name": "helicopter",
        "unicode": "1f681"
    },
    ":herb:": {
        "category": "Animals & Nature",
        "name": "herb",
        "unicode": "1f33f"
    },
    ":hibiscus:": {
        "category": "Animals & Nature",
        "name": "hibiscus",
        "unicode": "1f33a"
    },
    ":high_brightness:": {
        "category": "Symbols",
        "name": "bright button",
        "unicode": "1f506"
    },
    ":high_heel:": {
        "category": "Objects",
        "name": "high-heeled shoe",
        "unicode": "1f460"
    },
    ":hiking_boot:": {
        "category": "Objects",
        "name": "hiking boot",
        "unicode": "1f97e"
    },
    ":hindu_temple:": {
        "category": "Travel & Places",
        "name": "hindu temple",
        "unicode": "1f6d5"
    },
    ":hippopotamus:": {
        "category": "Animals & Nature",
        "name": "hippopotamus",
        "unicode": "1f99b"
    },
    ":hocho:": {
        "category": "Food & Drink",
        "name": "kitchen knife",
        "unicode": "1f52a"
    },
    ":hole:": {
        "category": "Smileys & Emotion",
        "name": "hole",
        "unicode": "1f573",
        "unicode_alt": "1f573-fe0f"
    },
    ":honduras:": {
        "category": "Flags",
        "name": "flag: Honduras",
        "unicode": "1f1ed-1f1f3"
    },
    ":honey_pot:": {
        "category": "Food & Drink",
        "name": "honey pot",
        "unicode": "1f36f"
    },
    ":hong_kong:": {
        "category": "Flags",
        "name": "flag: Hong Kong SAR China",
        "unicode": "1f1ed-1f1f0"
    },
    ":hook:": {
        "category": "Objects",
        "name": "hook",
        "unicode": "1fa9d"
    },
    ":horse:": {
        "category": "Animals & Nature",
        "name": "horse face",
        "unicode": "1f434"
    },
    ":horse_racing:": {
        "category": "People & Body",
        "name": "horse racing",
        "unicode": "1f3c7"
    },
    ":hospital:": {
        "category": "Travel & Places",
        "name": "hospital",
        "unicode": "1f3e5"
    },
    ":hot_face:": {
        "category": "Smileys & Emotion",
        "name": "hot face",
        "unicode": "1f975"
    },
    ":hot_pepper:": {
        "category": "Food & Drink",
        "name": "hot pepper",
        "unicode": "1f336",
        "unicode_alt": "1f336-fe0f"
    },
    ":hotdog:": {
        "category": "Food & Drink",
        "name": "hot dog",
        "unicode": "1f32d"
    },
    ":hotel:": {
        "category": "Travel & Places",
        "name": "hotel",
        "unicode": "1f3e8"
    },
    ":hotsprings:": {
        "category": "Travel & Places",
        "name": "hot springs",
        "unicode": "2668",
        "unicode_alt": "2668-fe0f"
    },
    ":hourglass:": {
        "category": "Travel & Places",
        "name": "hourglass done",
        "unicode": "231b"
    },
    ":hourglass_flowing_sand:": {
        "category": "Travel & Places",
        "name": "hourglass not done",
        "unicode": "23f3"
    },
    ":house:": {
        "category": "Travel & Places",
        "name": "house",
        "unicode": "1f3e0"
    },
    ":house_with_garden:": {
        "category": "Travel & Places",
        "name": "house with garden",
        "unicode": "1f3e1"
    },
    ":houses:": {
        "category": "Travel & Places",
        "name": "houses",
        "unicode": "1f3d8",
        "unicode_alt": "1f3d8-fe0f"
    },
    ":hugs:": {
        "category": "Smileys & Emotion",
        "name": "smiling face with open hands",
        "unicode": "1f917"
    },
    ":hungary:": {
        "category": "Flags",
        "name": "flag: Hungary",
        "unicode": "1f1ed-1f1fa"
    },
    ":hushed:": {
        "category": "Smileys & Emotion",
        "name": "hushed face",
        "unicode": "1f62f"
    },
    ":hut:": {
        "category": "Travel & Places",
        "name": "hut",
        "unicode": "1f6d6"
    },
    ":hyacinth:": {
        "category": "Animals & Nature",
        "name": "hyacinth",
        "unicode": "1fabb"
    },
    ":ice_cream:": {
        "category": "Food & Drink",
        "name": "ice cream",
        "unicode": "1f368"
    },
    ":ice_cube:": {
        "category": "Food & Drink",
        "name": "ice",
        "unicode": "1f9ca"
    },
    ":ice_hockey:": {
        "category": "Activities",
        "name": "ice hockey",
        "unicode": "1f3d2"
    },
    ":ice_skate:": {
        "category": "Activities",
        "name": "ice skate",
        "unicode": "26f8",
        "unicode_alt": "26f8-fe0f"
    },
    ":icecream:": {
        "category": "Food & Drink",
        "name": "soft ice cream",
        "unicode": "1f366"
    },
    ":iceland:": {
        "category": "Flags",
        "name": "flag: Iceland",
        "unicode": "1f1ee-1f1f8"
    },
    ":id:": {
        "category": "Symbols",
        "name": "ID button",
        "unicode": "1f194"
    },
    ":identification_card:": {
        "category": "Objects",
        "name": "identification card",
        "unicode": "1faaa"
    },
    ":ideograph_advantage:": {
        "category": "Symbols",
        "name": "Japanese \u201cbargain\u201d button",
        "unicode": "1f250"
    },
    ":imp:": {
        "category": "Smileys & Emotion",
        "name": "angry face with horns",
        "unicode": "1f47f"
    },
    ":inbox_tray:": {
        "category": "Objects",
        "name": "inbox tray",
        "unicode": "1f4e5"
    },
    ":incoming_envelope:": {
        "category": "Objects",
        "name": "incoming envelope",
        "unicode": "1f4e8"
    },
    ":index_pointing_at_the_viewer:": {
        "category": "People & Body",
        "name": "index pointing at the viewer",
        "unicode": "1faf5"
    },
    ":india:": {
        "category": "Flags",
        "name": "flag: India",
        "unicode": "1f1ee-1f1f3"
    },
    ":indonesia:": {
        "category": "Flags",
        "name": "flag: Indonesia",
        "unicode": "1f1ee-1f1e9"
    },
    ":infinity:": {
        "category": "Symbols",
        "name": "infinity",
        "unicode": "267e",
        "unicode_alt": "267e-fe0f"
    },
    ":information_source:": {
        "category": "Symbols",
        "name": "information",
        "unicode": "2139",
        "unicode_alt": "2139-fe0f"
    },
    ":innocent:": {
        "category": "Smileys & Emotion",
        "name": "smiling face with halo",
        "unicode": "1f607"
    },
    ":interrobang:": {
        "category": "Symbols",
        "name": "exclamation question mark",
        "unicode": "2049",
        "unicode_alt": "2049-fe0f"
    },
    ":iphone:": {
        "category": "Objects",
        "name": "mobile phone",
        "unicode": "1f4f1"
    },
    ":iran:": {
        "category": "Flags",
        "name": "flag: Iran",
        "unicode": "1f1ee-1f1f7"
    },
    ":iraq:": {
        "category": "Flags",
        "name": "flag: Iraq",
        "unicode": "1f1ee-1f1f6"
    },
    ":ireland:": {
        "category": "Flags",
        "name": "flag: Ireland",
        "unicode": "1f1ee-1f1ea"
    },
    ":isle_of_man:": {
        "category": "Flags",
        "name": "flag: Isle of Man",
        "unicode": "1f1ee-1f1f2"
    },
    ":israel:": {
        "category": "Flags",
        "name": "flag: Israel",
        "unicode": "1f1ee-1f1f1"
    },
    ":it:": {
        "category": "Flags",
        "name": "flag: Italy",
        "unicode": "1f1ee-1f1f9"
    },
    ":izakaya_lantern:": {
        "category": "Objects",
        "name": "red paper lantern",
        "unicode": "1f3ee"
    },
    ":jack_o_lantern:": {
        "category": "Activities",
        "name": "jack-o-lantern",
        "unicode": "1f383"
    },
    ":jamaica:": {
        "category": "Flags",
        "name": "flag: Jamaica",
        "unicode": "1f1ef-1f1f2"
    },
    ":japan:": {
        "category": "Travel & Places",
        "name": "map of Japan",
        "unicode": "1f5fe"
    },
    ":japanese_castle:": {
        "category": "Travel & Places",
        "name": "Japanese castle",
        "unicode": "1f3ef"
    },
    ":japanese_goblin:": {
        "category": "Smileys & Emotion",
        "name": "goblin",
        "unicode": "1f47a"
    },
    ":japanese_ogre:": {
        "category": "Smileys & Emotion",
        "name": "ogre",
        "unicode": "1f479"
    },
    ":jar:": {
        "category": "Food & Drink",
        "name": "jar",
        "unicode": "1fad9"
    },
    ":jeans:": {
        "category": "Objects",
        "name": "jeans",
        "unicode": "1f456"
    },
    ":jellyfish:": {
        "category": "Animals & Nature",
        "name": "jellyfish",
        "unicode": "1fabc"
    },
    ":jersey:": {
        "category": "Flags",
        "name": "flag: Jersey",
        "unicode": "1f1ef-1f1ea"
    },
    ":jigsaw:": {
        "category": "Activities",
        "name": "puzzle piece",
        "unicode": "1f9e9"
    },
    ":jordan:": {
        "category": "Flags",
        "name": "flag: Jordan",
        "unicode": "1f1ef-1f1f4"
    },
    ":joy:": {
        "category": "Smileys & Emotion",
        "name": "face with tears of joy",
        "unicode": "1f602"
    },
    ":joy_cat:": {
        "category": "Smileys & Emotion",
        "name": "cat with tears of joy",
        "unicode": "1f639"
    },
    ":joystick:": {
        "category": "Activities",
        "name": "joystick",
        "unicode": "1f579",
        "unicode_alt": "1f579-fe0f"
    },
    ":jp:": {
        "category": "Flags",
        "name": "flag: Japan",
        "unicode": "1f1ef-1f1f5"
    },
    ":judge:": {
        "category": "People & Body",
        "name": "judge",
        "unicode": "1f9d1-2696",
        "unicode_alt": "1f9d1-200d-2696-fe0f"
    },
    ":juggling_person:": {
        "category": "People & Body",
        "name": "person juggling",
        "unicode": "1f939"
    },
    ":kaaba:": {
        "category": "Travel & Places",
        "name": "kaaba",
        "unicode": "1f54b"
    },
    ":kangaroo:": {
        "category": "Animals & Nature",
        "name": "kangaroo",
        "unicode": "1f998"
    },
    ":kazakhstan:": {
        "category": "Flags",
        "name": "flag: Kazakhstan",
        "unicode": "1f1f0-1f1ff"
    },
    ":kenya:": {
        "category": "Flags",
        "name": "flag: Kenya",
        "unicode": "1f1f0-1f1ea"
    },
    ":key:": {
        "category": "Objects",
        "name": "key",
        "unicode": "1f511"
    },
    ":keyboard:": {
        "category": "Objects",
        "name": "keyboard",
        "unicode": "2328",
        "unicode_alt": "2328-fe0f"
    },
    ":keycap_ten:": {
        "category": "Symbols",
        "name": "keycap: 10",
        "unicode": "1f51f"
    },
    ":khanda:": {
        "category": "Symbols",
        "name": "khanda",
        "unicode": "1faaf"
    },
    ":kick_scooter:": {
        "category": "Travel & Places",
        "name": "kick scooter",
        "unicode": "1f6f4"
    },
    ":kimono:": {
        "category": "Objects",
        "name": "kimono",
        "unicode": "1f458"
    },
    ":kiribati:": {
        "category": "Flags",
        "name": "flag: Kiribati",
        "unicode": "1f1f0-1f1ee"
    },
    ":kiss:": {
        "category": "Smileys & Emotion",
        "name": "kiss mark",
        "unicode": "1f48b"
    },
    ":kissing:": {
        "category": "Smileys & Emotion",
        "name": "kissing face",
        "unicode": "1f617"
    },
    ":kissing_cat:": {
        "category": "Smileys & Emotion",
        "name": "kissing cat",
        "unicode": "1f63d"
    },
    ":kissing_closed_eyes:": {
        "category": "Smileys & Emotion",
        "name": "kissing face with closed eyes",
        "unicode": "1f61a"
    },
    ":kissing_heart:": {
        "category": "Smileys & Emotion",
        "name": "face blowing a kiss",
        "unicode": "1f618"
    },
    ":kissing_smiling_eyes:": {
        "category": "Smileys & Emotion",
        "name": "kissing face with smiling eyes",
        "unicode": "1f619"
    },
    ":kite:": {
        "category": "Activities",
        "name": "kite",
        "unicode": "1fa81"
    },
    ":kiwi_fruit:": {
        "category": "Food & Drink",
        "name": "kiwi fruit",
        "unicode": "1f95d"
    },
    ":kneeling_man:": {
        "category": "People & Body",
        "name": "man kneeling",
        "unicode": "1f9ce-2642",
        "unicode_alt": "1f9ce-200d-2642-fe0f"
    },
    ":kneeling_person:": {
        "category": "People & Body",
        "name": "person kneeling",
        "unicode": "1f9ce"
    },
    ":kneeling_woman:": {
        "category": "People & Body",
        "name": "woman kneeling",
        "unicode": "1f9ce-2640",
        "unicode_alt": "1f9ce-200d-2640-fe0f"
    },
    ":knot:": {
        "category": "Activities",
        "name": "knot",
        "unicode": "1faa2"
    },
    ":koala:": {
        "category": "Animals & Nature",
        "name": "koala",
        "unicode": "1f428"
    },
    ":koko:": {
        "category": "Symbols",
        "name": "Japanese \u201chere\u201d button",
        "unicode": "1f201"
    },
    ":kosovo:": {
        "category": "Flags",
        "name": "flag: Kosovo",
        "unicode": "1f1fd-1f1f0"
    },
    ":kr:": {
        "category": "Flags",
        "name": "flag: South Korea",
        "unicode": "1f1f0-1f1f7"
    },
    ":kuwait:": {
        "category": "Flags",
        "name": "flag: Kuwait",
        "unicode": "1f1f0-1f1fc"
    },
    ":kyrgyzstan:": {
        "category": "Flags",
        "name": "flag: Kyrgyzstan",
        "unicode": "1f1f0-1f1ec"
    },
    ":lab_coat:": {
        "category": "Objects",
        "name": "lab coat",
        "unicode": "1f97c"
    },
    ":label:": {
        "category": "Objects",
        "name": "label",
        "unicode": "1f3f7",
        "unicode_alt": "1f3f7-fe0f"
    },
    ":lacrosse:": {
        "category": "Activities",
        "name": "lacrosse",
        "unicode": "1f94d"
    },
    ":ladder:": {
        "category": "Objects",
        "name": "ladder",
        "unicode": "1fa9c"
    },
    ":lady_beetle:": {
        "category": "Animals & Nature",
        "name": "lady beetle",
        "unicode": "1f41e"
    },
    ":laos:": {
        "category": "Flags",
        "name": "flag: Laos",
        "unicode": "1f1f1-1f1e6"
    },
    ":large_blue_circle:": {
        "category": "Symbols",
        "name": "blue circle",
        "unicode": "1f535"
    },
    ":large_blue_diamond:": {
        "category": "Symbols",
        "name": "large blue diamond",
        "unicode": "1f537"
    },
    ":large_orange_diamond:": {
        "category": "Symbols",
        "name": "large orange diamond",
        "unicode": "1f536"
    },
    ":last_quarter_moon:": {
        "category": "Travel & Places",
        "name": "last quarter moon",
        "unicode": "1f317"
    },
    ":last_quarter_moon_with_face:": {
        "category": "Travel & Places",
        "name": "last quarter moon face",
        "unicode": "1f31c"
    },
    ":latin_cross:": {
        "category": "Symbols",
        "name": "latin cross",
        "unicode": "271d",
        "unicode_alt": "271d-fe0f"
    },
    ":latvia:": {
        "category": "Flags",
        "name": "flag: Latvia",
        "unicode": "1f1f1-1f1fb"
    },
    ":laughing:": {
        "category": "Smileys & Emotion",
        "name": "grinning squinting face",
        "unicode": "1f606"
    },
    ":leafy_green:": {
        "category": "Food & Drink",
        "name": "leafy green",
        "unicode": "1f96c"
    },
    ":leaves:": {
        "category": "Animals & Nature",
        "name": "leaf fluttering in wind",
        "unicode": "1f343"
    },
    ":lebanon:": {
        "category": "Flags",
        "name": "flag: Lebanon",
        "unicode": "1f1f1-1f1e7"
    },
    ":ledger:": {
        "category": "Objects",
        "name": "ledger",
        "unicode": "1f4d2"
    },
    ":left_luggage:": {
        "category": "Symbols",
        "name": "left luggage",
        "unicode": "1f6c5"
    },
    ":left_right_arrow:": {
        "category": "Symbols",
        "name": "left-right arrow",
        "unicode": "2194",
        "unicode_alt": "2194-fe0f"
    },
    ":left_speech_bubble:": {
        "category": "Smileys & Emotion",
        "name": "left speech bubble",
        "unicode": "1f5e8",
        "unicode_alt": "1f5e8-fe0f"
    },
    ":leftwards_arrow_with_hook:": {
        "category": "Symbols",
        "name": "right arrow curving left",
        "unicode": "21a9",
        "unicode_alt": "21a9-fe0f"
    },
    ":leftwards_hand:": {
        "category": "People & Body",
        "name": "leftwards hand",
        "unicode": "1faf2"
    },
    ":leftwards_pushing_hand:": {
        "category": "People & Body",
        "name": "leftwards pushing hand",
        "unicode": "1faf7"
    },
    ":leg:": {
        "category": "People & Body",
        "name": "leg",
        "unicode": "1f9b5"
    },
    ":lemon:": {
        "category": "Food & Drink",
        "name": "lemon",
        "unicode": "1f34b"
    },
    ":leo:": {
        "category": "Symbols",
        "name": "Leo",
        "unicode": "264c"
    },
    ":leopard:": {
        "category": "Animals & Nature",
        "name": "leopard",
        "unicode": "1f406"
    },
    ":lesotho:": {
        "category": "Flags",
        "name": "flag: Lesotho",
        "unicode": "1f1f1-1f1f8"
    },
    ":level_slider:": {
        "category": "Objects",
        "name": "level slider",
        "unicode": "1f39a",
        "unicode_alt": "1f39a-fe0f"
    },
    ":liberia:": {
        "category": "Flags",
        "name": "flag: Liberia",
        "unicode": "1f1f1-1f1f7"
    },
    ":libra:": {
        "category": "Symbols",
        "name": "Libra",
        "unicode": "264e"
    },
    ":libya:": {
        "category": "Flags",
        "name": "flag: Libya",
        "unicode": "1f1f1-1f1fe"
    },
    ":liechtenstein:": {
        "category": "Flags",
        "name": "flag: Liechtenstein",
        "unicode": "1f1f1-1f1ee"
    },
    ":light_blue_heart:": {
        "category": "Smileys & Emotion",
        "name": "light blue heart",
        "unicode": "1fa75"
    },
    ":light_rail:": {
        "category": "Travel & Places",
        "name": "light rail",
        "unicode": "1f688"
    },
    ":link:": {
        "category": "Objects",
        "name": "link",
        "unicode": "1f517"
    },
    ":lion:": {
        "category": "Animals & Nature",
        "name": "lion",
        "unicode": "1f981"
    },
    ":lips:": {
        "category": "People & Body",
        "name": "mouth",
        "unicode": "1f444"
    },
    ":lipstick:": {
        "category": "Objects",
        "name": "lipstick",
        "unicode": "1f484"
    },
    ":lithuania:": {
        "category": "Flags",
        "name": "flag: Lithuania",
        "unicode": "1f1f1-1f1f9"
    },
    ":lizard:": {
        "category": "Animals & Nature",
        "name": "lizard",
        "unicode": "1f98e"
    },
    ":llama:": {
        "category": "Animals & Nature",
        "name": "llama",
        "unicode": "1f999"
    },
    ":lobster:": {
        "category": "Food & Drink",
        "name": "lobster",
        "unicode": "1f99e"
    },
    ":lock:": {
        "category": "Objects",
        "name": "locked",
        "unicode": "1f512"
    },
    ":lock_with_ink_pen:": {
        "category": "Objects",
        "name": "locked with pen",
        "unicode": "1f50f"
    },
    ":lollipop:": {
        "category": "Food & Drink",
        "name": "lollipop",
        "unicode": "1f36d"
    },
    ":long_drum:": {
        "category": "Objects",
        "name": "long drum",
        "unicode": "1fa98"
    },
    ":loop:": {
        "category": "Symbols",
        "name": "double curly loop",
        "unicode": "27bf"
    },
    ":lotion_bottle:": {
        "category": "Objects",
        "name": "lotion bottle",
        "unicode": "1f9f4"
    },
    ":lotus:": {
        "category": "Animals & Nature",
        "name": "lotus",
        "unicode": "1fab7"
    },
    ":lotus_position:": {
        "category": "People & Body",
        "name": "person in lotus position",
        "unicode": "1f9d8"
    },
    ":lotus_position_man:": {
        "category": "People & Body",
        "name": "man in lotus position",
        "unicode": "1f9d8-2642",
        "unicode_alt": "1f9d8-200d-2642-fe0f"
    },
    ":lotus_position_woman:": {
        "category": "People & Body",
        "name": "woman in lotus position",
        "unicode": "1f9d8-2640",
        "unicode_alt": "1f9d8-200d-2640-fe0f"
    },
    ":loud_sound:": {
        "category": "Objects",
        "name": "speaker high volume",
        "unicode": "1f50a"
    },
    ":loudspeaker:": {
        "category": "Objects",
        "name": "loudspeaker",
        "unicode": "1f4e2"
    },
    ":love_hotel:": {
        "category": "Travel & Places",
        "name": "love hotel",
        "unicode": "1f3e9"
    },
    ":love_letter:": {
        "category": "Smileys & Emotion",
        "name": "love letter",
        "unicode": "1f48c"
    },
    ":love_you_gesture:": {
        "category": "People & Body",
        "name": "love-you gesture",
        "unicode": "1f91f"
    },
    ":low_battery:": {
        "category": "Objects",
        "name": "low battery",
        "unicode": "1faab"
    },
    ":low_brightness:": {
        "category": "Symbols",
        "name": "dim button",
        "unicode": "1f505"
    },
    ":luggage:": {
        "category": "Travel & Places",
        "name": "luggage",
        "unicode": "1f9f3"
    },
    ":lungs:": {
        "category": "People & Body",
        "name": "lungs",
        "unicode": "1fac1"
    },
    ":luxembourg:": {
        "category": "Flags",
        "name": "flag: Luxembourg",
        "unicode": "1f1f1-1f1fa"
    },
    ":lying_face:": {
        "category": "Smileys & Emotion",
        "name": "lying face",
        "unicode": "1f925"
    },
    ":m:": {
        "category": "Symbols",
        "name": "circled M",
        "unicode": "24c2",
        "unicode_alt": "24c2-fe0f"
    },
    ":macau:": {
        "category": "Flags",
        "name": "flag: Macao SAR China",
        "unicode": "1f1f2-1f1f4"
    },
    ":macedonia:": {
        "category": "Flags",
        "name": "flag: North Macedonia",
        "unicode": "1f1f2-1f1f0"
    },
    ":madagascar:": {
        "category": "Flags",
        "name": "flag: Madagascar",
        "unicode": "1f1f2-1f1ec"
    },
    ":mag:": {
        "category": "Objects",
        "name": "magnifying glass tilted left",
        "unicode": "1f50d"
    },
    ":mag_right:": {
        "category": "Objects",
        "name": "magnifying glass tilted right",
        "unicode": "1f50e"
    },
    ":mage:": {
        "category": "People & Body",
        "name": "mage",
        "unicode": "1f9d9"
    },
    ":mage_man:": {
        "category": "People & Body",
        "name": "man mage",
        "unicode": "1f9d9-2642",
        "unicode_alt": "1f9d9-200d-2642-fe0f"
    },
    ":mage_woman:": {
        "category": "People & Body",
        "name": "woman mage",
        "unicode": "1f9d9-2640",
        "unicode_alt": "1f9d9-200d-2640-fe0f"
    },
    ":magic_wand:": {
        "category": "Activities",
        "name": "magic wand",
        "unicode": "1fa84"
    },
    ":magnet:": {
        "category": "Objects",
        "name": "magnet",
        "unicode": "1f9f2"
    },
    ":mahjong:": {
        "category": "Activities",
        "name": "mahjong red dragon",
        "unicode": "1f004"
    },
    ":mailbox:": {
        "category": "Objects",
        "name": "closed mailbox with raised flag",
        "unicode": "1f4eb"
    },
    ":mailbox_closed:": {
        "category": "Objects",
        "name": "closed mailbox with lowered flag",
        "unicode": "1f4ea"
    },
    ":mailbox_with_mail:": {
        "category": "Objects",
        "name": "open mailbox with raised flag",
        "unicode": "1f4ec"
    },
    ":mailbox_with_no_mail:": {
        "category": "Objects",
        "name": "open mailbox with lowered flag",
        "unicode": "1f4ed"
    },
    ":malawi:": {
        "category": "Flags",
        "name": "flag: Malawi",
        "unicode": "1f1f2-1f1fc"
    },
    ":malaysia:": {
        "category": "Flags",
        "name": "flag: Malaysia",
        "unicode": "1f1f2-1f1fe"
    },
    ":maldives:": {
        "category": "Flags",
        "name": "flag: Maldives",
        "unicode": "1f1f2-1f1fb"
    },
    ":male_detective:": {
        "category": "People & Body",
        "name": "man detective",
        "unicode": "1f575-2642",
        "unicode_alt": "1f575-fe0f-200d-2642-fe0f"
    },
    ":male_sign:": {
        "category": "Symbols",
        "name": "male sign",
        "unicode": "2642",
        "unicode_alt": "2642-fe0f"
    },
    ":mali:": {
        "category": "Flags",
        "name": "flag: Mali",
        "unicode": "1f1f2-1f1f1"
    },
    ":malta:": {
        "category": "Flags",
        "name": "flag: Malta",
        "unicode": "1f1f2-1f1f9"
    },
    ":mammoth:": {
        "category": "Animals & Nature",
        "name": "mammoth",
        "unicode": "1f9a3"
    },
    ":man:": {
        "category": "People & Body",
        "name": "man",
        "unicode": "1f468"
    },
    ":man_artist:": {
        "category": "People & Body",
        "name": "man artist",
        "unicode": "1f468-1f3a8",
        "unicode_alt": "1f468-200d-1f3a8"
    },
    ":man_astronaut:": {
        "category": "People & Body",
        "name": "man astronaut",
        "unicode": "1f468-1f680",
        "unicode_alt": "1f468-200d-1f680"
    },
    ":man_beard:": {
        "category": "People & Body",
        "name": "man: beard",
        "unicode": "1f9d4-2642",
        "unicode_alt": "1f9d4-200d-2642-fe0f"
    },
    ":man_cartwheeling:": {
        "category": "People & Body",
        "name": "man cartwheeling",
        "unicode": "1f938-2642",
        "unicode_alt": "1f938-200d-2642-fe0f"
    },
    ":man_cook:": {
        "category": "People & Body",
        "name": "man cook",
        "unicode": "1f468-1f373",
        "unicode_alt": "1f468-200d-1f373"
    },
    ":man_dancing:": {
        "category": "People & Body",
        "name": "man dancing",
        "unicode": "1f57a"
    },
    ":man_facepalming:": {
        "category": "People & Body",
        "name": "man facepalming",
        "unicode": "1f926-2642",
        "unicode_alt": "1f926-200d-2642-fe0f"
    },
    ":man_factory_worker:": {
        "category": "People & Body",
        "name": "man factory worker",
        "unicode": "1f468-1f3ed",
        "unicode_alt": "1f468-200d-1f3ed"
    },
    ":man_farmer:": {
        "category": "People & Body",
        "name": "man farmer",
        "unicode": "1f468-1f33e",
        "unicode_alt": "1f468-200d-1f33e"
    },
    ":man_feeding_baby:": {
        "category": "People & Body",
        "name": "man feeding baby",
        "unicode": "1f468-1f37c",
        "unicode_alt": "1f468-200d-1f37c"
    },
    ":man_firefighter:": {
        "category": "People & Body",
        "name": "man firefighter",
        "unicode": "1f468-1f692",
        "unicode_alt": "1f468-200d-1f692"
    },
    ":man_health_worker:": {
        "category": "People & Body",
        "name": "man health worker",
        "unicode": "1f468-2695",
        "unicode_alt": "1f468-200d-2695-fe0f"
    },
    ":man_in_manual_wheelchair:": {
        "category": "People & Body",
        "name": "man in manual wheelchair",
        "unicode": "1f468-1f9bd",
        "unicode_alt": "1f468-200d-1f9bd"
    },
    ":man_in_motorized_wheelchair:": {
        "category": "People & Body",
        "name": "man in motorized wheelchair",
        "unicode": "1f468-1f9bc",
        "unicode_alt": "1f468-200d-1f9bc"
    },
    ":man_in_tuxedo:": {
        "category": "People & Body",
        "name": "man in tuxedo",
        "unicode": "1f935-2642",
        "unicode_alt": "1f935-200d-2642-fe0f"
    },
    ":man_judge:": {
        "category": "People & Body",
        "name": "man judge",
        "unicode": "1f468-2696",
        "unicode_alt": "1f468-200d-2696-fe0f"
    },
    ":man_juggling:": {
        "category": "People & Body",
        "name": "man juggling",
        "unicode": "1f939-2642",
        "unicode_alt": "1f939-200d-2642-fe0f"
    },
    ":man_mechanic:": {
        "category": "People & Body",
        "name": "man mechanic",
        "unicode": "1f468-1f527",
        "unicode_alt": "1f468-200d-1f527"
    },
    ":man_office_worker:": {
        "category": "People & Body",
        "name": "man office worker",
        "unicode": "1f468-1f4bc",
        "unicode_alt": "1f468-200d-1f4bc"
    },
    ":man_pilot:": {
        "category": "People & Body",
        "name": "man pilot",
        "unicode": "1f468-2708",
        "unicode_alt": "1f468-200d-2708-fe0f"
    },
    ":man_playing_handball:": {
        "category": "People & Body",
        "name": "man playing handball",
        "unicode": "1f93e-2642",
        "unicode_alt": "1f93e-200d-2642-fe0f"
    },
    ":man_playing_water_polo:": {
        "category": "People & Body",
        "name": "man playing water polo",
        "unicode": "1f93d-2642",
        "unicode_alt": "1f93d-200d-2642-fe0f"
    },
    ":man_scientist:": {
        "category": "People & Body",
        "name": "man scientist",
        "unicode": "1f468-1f52c",
        "unicode_alt": "1f468-200d-1f52c"
    },
    ":man_shrugging:": {
        "category": "People & Body",
        "name": "man shrugging",
        "unicode": "1f937-2642",
        "unicode_alt": "1f937-200d-2642-fe0f"
    },
    ":man_singer:": {
        "category": "People & Body",
        "name": "man singer",
        "unicode": "1f468-1f3a4",
        "unicode_alt": "1f468-200d-1f3a4"
    },
    ":man_student:": {
        "category": "People & Body",
        "name": "man student",
        "unicode": "1f468-1f393",
        "unicode_alt": "1f468-200d-1f393"
    },
    ":man_teacher:": {
        "category": "People & Body",
        "name": "man teacher",
        "unicode": "1f468-1f3eb",
        "unicode_alt": "1f468-200d-1f3eb"
    },
    ":man_technologist:": {
        "category": "People & Body",
        "name": "man technologist",
        "unicode": "1f468-1f4bb",
        "unicode_alt": "1f468-200d-1f4bb"
    },
    ":man_with_gua_pi_mao:": {
        "category": "People & Body",
        "name": "person with skullcap",
        "unicode": "1f472"
    },
    ":man_with_probing_cane:": {
        "category": "People & Body",
        "name": "man with white cane",
        "unicode": "1f468-1f9af",
        "unicode_alt": "1f468-200d-1f9af"
    },
    ":man_with_turban:": {
        "category": "People & Body",
        "name": "man wearing turban",
        "unicode": "1f473-2642",
        "unicode_alt": "1f473-200d-2642-fe0f"
    },
    ":man_with_veil:": {
        "category": "People & Body",
        "name": "man with veil",
        "unicode": "1f470-2642",
        "unicode_alt": "1f470-200d-2642-fe0f"
    },
    ":mango:": {
        "category": "Food & Drink",
        "name": "mango",
        "unicode": "1f96d"
    },
    ":mans_shoe:": {
        "category": "Objects",
        "name": "man\u2019s shoe",
        "unicode": "1f45e"
    },
    ":mantelpiece_clock:": {
        "category": "Travel & Places",
        "name": "mantelpiece clock",
        "unicode": "1f570",
        "unicode_alt": "1f570-fe0f"
    },
    ":manual_wheelchair:": {
        "category": "Travel & Places",
        "name": "manual wheelchair",
        "unicode": "1f9bd"
    },
    ":maple_leaf:": {
        "category": "Animals & Nature",
        "name": "maple leaf",
        "unicode": "1f341"
    },
    ":maracas:": {
        "category": "Objects",
        "name": "maracas",
        "unicode": "1fa87"
    },
    ":marshall_islands:": {
        "category": "Flags",
        "name": "flag: Marshall Islands",
        "unicode": "1f1f2-1f1ed"
    },
    ":martial_arts_uniform:": {
        "category": "Activities",
        "name": "martial arts uniform",
        "unicode": "1f94b"
    },
    ":martinique:": {
        "category": "Flags",
        "name": "flag: Martinique",
        "unicode": "1f1f2-1f1f6"
    },
    ":mask:": {
        "category": "Smileys & Emotion",
        "name": "face with medical mask",
        "unicode": "1f637"
    },
    ":massage:": {
        "category": "People & Body",
        "name": "person getting massage",
        "unicode": "1f486"
    },
    ":massage_man:": {
        "category": "People & Body",
        "name": "man getting massage",
        "unicode": "1f486-2642",
        "unicode_alt": "1f486-200d-2642-fe0f"
    },
    ":massage_woman:": {
        "category": "People & Body",
        "name": "woman getting massage",
        "unicode": "1f486-2640",
        "unicode_alt": "1f486-200d-2640-fe0f"
    },
    ":mate:": {
        "category": "Food & Drink",
        "name": "mate",
        "unicode": "1f9c9"
    },
    ":mauritania:": {
        "category": "Flags",
        "name": "flag: Mauritania",
        "unicode": "1f1f2-1f1f7"
    },
    ":mauritius:": {
        "category": "Flags",
        "name": "flag: Mauritius",
        "unicode": "1f1f2-1f1fa"
    },
    ":mayotte:": {
        "category": "Flags",
        "name": "flag: Mayotte",
        "unicode": "1f1fe-1f1f9"
    },
    ":meat_on_bone:": {
        "category": "Food & Drink",
        "name": "meat on bone",
        "unicode": "1f356"
    },
    ":mechanic:": {
        "category": "People & Body",
        "name": "mechanic",
        "unicode": "1f9d1-1f527",
        "unicode_alt": "1f9d1-200d-1f527"
    },
    ":mechanical_arm:": {
        "category": "People & Body",
        "name": "mechanical arm",
        "unicode": "1f9be"
    },
    ":mechanical_leg:": {
        "category": "People & Body",
        "name": "mechanical leg",
        "unicode": "1f9bf"
    },
    ":medal_military:": {
        "category": "Activities",
        "name": "military medal",
        "unicode": "1f396",
        "unicode_alt": "1f396-fe0f"
    },
    ":medal_sports:": {
        "category": "Activities",
        "name": "sports medal",
        "unicode": "1f3c5"
    },
    ":medical_symbol:": {
        "category": "Symbols",
        "name": "medical symbol",
        "unicode": "2695",
        "unicode_alt": "2695-fe0f"
    },
    ":mega:": {
        "category": "Objects",
        "name": "megaphone",
        "unicode": "1f4e3"
    },
    ":melon:": {
        "category": "Food & Drink",
        "name": "melon",
        "unicode": "1f348"
    },
    ":melting_face:": {
        "category": "Smileys & Emotion",
        "name": "melting face",
        "unicode": "1fae0"
    },
    ":memo:": {
        "category": "Objects",
        "name": "memo",
        "unicode": "1f4dd"
    },
    ":men_wrestling:": {
        "category": "People & Body",
        "name": "men wrestling",
        "unicode": "1f93c-2642",
        "unicode_alt": "1f93c-200d-2642-fe0f"
    },
    ":mending_heart:": {
        "category": "Smileys & Emotion",
        "name": "mending heart",
        "unicode": "2764-1fa79",
        "unicode_alt": "2764-fe0f-200d-1fa79"
    },
    ":menorah:": {
        "category": "Symbols",
        "name": "menorah",
        "unicode": "1f54e"
    },
    ":mens:": {
        "category": "Symbols",
        "name": "men\u2019s room",
        "unicode": "1f6b9"
    },
    ":mermaid:": {
        "category": "People & Body",
        "name": "mermaid",
        "unicode": "1f9dc-2640",
        "unicode_alt": "1f9dc-200d-2640-fe0f"
    },
    ":merman:": {
        "category": "People & Body",
        "name": "merman",
        "unicode": "1f9dc-2642",
        "unicode_alt": "1f9dc-200d-2642-fe0f"
    },
    ":merperson:": {
        "category": "People & Body",
        "name": "merperson",
        "unicode": "1f9dc"
    },
    ":metal:": {
        "category": "People & Body",
        "name": "sign of the horns",
        "unicode": "1f918"
    },
    ":metro:": {
        "category": "Travel & Places",
        "name": "metro",
        "unicode": "1f687"
    },
    ":mexico:": {
        "category": "Flags",
        "name": "flag: Mexico",
        "unicode": "1f1f2-1f1fd"
    },
    ":microbe:": {
        "category": "Animals & Nature",
        "name": "microbe",
        "unicode": "1f9a0"
    },
    ":micronesia:": {
        "category": "Flags",
        "name": "flag: Micronesia",
        "unicode": "1f1eb-1f1f2"
    },
    ":microphone:": {
        "category": "Objects",
        "name": "microphone",
        "unicode": "1f3a4"
    },
    ":microscope:": {
        "category": "Objects",
        "name": "microscope",
        "unicode": "1f52c"
    },
    ":middle_finger:": {
        "category": "People & Body",
        "name": "middle finger",
        "unicode": "1f595"
    },
    ":military_helmet:": {
        "category": "Objects",
        "name": "military helmet",
        "unicode": "1fa96"
    },
    ":milk_glass:": {
        "category": "Food & Drink",
        "name": "glass of milk",
        "unicode": "1f95b"
    },
    ":milky_way:": {
        "category": "Travel & Places",
        "name": "milky way",
        "unicode": "1f30c"
    },
    ":minibus:": {
        "category": "Travel & Places",
        "name": "minibus",
        "unicode": "1f690"
    },
    ":minidisc:": {
        "category": "Objects",
        "name": "computer disk",
        "unicode": "1f4bd"
    },
    ":mirror:": {
        "category": "Objects",
        "name": "mirror",
        "unicode": "1fa9e"
    },
    ":mirror_ball:": {
        "category": "Activities",
        "name": "mirror ball",
        "unicode": "1faa9"
    },
    ":mobile_phone_off:": {
        "category": "Symbols",
        "name": "mobile phone off",
        "unicode": "1f4f4"
    },
    ":moldova:": {
        "category": "Flags",
        "name": "flag: Moldova",
        "unicode": "1f1f2-1f1e9"
    },
    ":monaco:": {
        "category": "Flags",
        "name": "flag: Monaco",
        "unicode": "1f1f2-1f1e8"
    },
    ":money_mouth_face:": {
        "category": "Smileys & Emotion",
        "name": "money-mouth face",
        "unicode": "1f911"
    },
    ":money_with_wings:": {
        "category": "Objects",
        "name": "money with wings",
        "unicode": "1f4b8"
    },
    ":moneybag:": {
        "category": "Objects",
        "name": "money bag",
        "unicode": "1f4b0"
    },
    ":mongolia:": {
        "category": "Flags",
        "name": "flag: Mongolia",
        "unicode": "1f1f2-1f1f3"
    },
    ":monkey:": {
        "category": "Animals & Nature",
        "name": "monkey",
        "unicode": "1f412"
    },
    ":monkey_face:": {
        "category": "Animals & Nature",
        "name": "monkey face",
        "unicode": "1f435"
    },
    ":monocle_face:": {
        "category": "Smileys & Emotion",
        "name": "face with monocle",
        "unicode": "1f9d0"
    },
    ":monorail:": {
        "category": "Travel & Places",
        "name": "monorail",
        "unicode": "1f69d"
    },
    ":montenegro:": {
        "category": "Flags",
        "name": "flag: Montenegro",
        "unicode": "1f1f2-1f1ea"
    },
    ":montserrat:": {
        "category": "Flags",
        "name": "flag: Montserrat",
        "unicode": "1f1f2-1f1f8"
    },
    ":moon:": {
        "category": "Travel & Places",
        "name": "waxing gibbous moon",
        "unicode": "1f314"
    },
    ":moon_cake:": {
        "category": "Food & Drink",
        "name": "moon cake",
        "unicode": "1f96e"
    },
    ":moose:": {
        "category": "Animals & Nature",
        "name": "moose",
        "unicode": "1face"
    },
    ":morocco:": {
        "category": "Flags",
        "name": "flag: Morocco",
        "unicode": "1f1f2-1f1e6"
    },
    ":mortar_board:": {
        "category": "Objects",
        "name": "graduation cap",
        "unicode": "1f393"
    },
    ":mosque:": {
        "category": "Travel & Places",
        "name": "mosque",
        "unicode": "1f54c"
    },
    ":mosquito:": {
        "category": "Animals & Nature",
        "name": "mosquito",
        "unicode": "1f99f"
    },
    ":motor_boat:": {
        "category": "Travel & Places",
        "name": "motor boat",
        "unicode": "1f6e5",
        "unicode_alt": "1f6e5-fe0f"
    },
    ":motor_scooter:": {
        "category": "Travel & Places",
        "name": "motor scooter",
        "unicode": "1f6f5"
    },
    ":motorcycle:": {
        "category": "Travel & Places",
        "name": "motorcycle",
        "unicode": "1f3cd",
        "unicode_alt": "1f3cd-fe0f"
    },
    ":motorized_wheelchair:": {
        "category": "Travel & Places",
        "name": "motorized wheelchair",
        "unicode": "1f9bc"
    },
    ":motorway:": {
        "category": "Travel & Places",
        "name": "motorway",
        "unicode": "1f6e3",
        "unicode_alt": "1f6e3-fe0f"
    },
    ":mount_fuji:": {
        "category": "Travel & Places",
        "name": "mount fuji",
        "unicode": "1f5fb"
    },
    ":mountain:": {
        "category": "Travel & Places",
        "name": "mountain",
        "unicode": "26f0",
        "unicode_alt": "26f0-fe0f"
    },
    ":mountain_bicyclist:": {
        "category": "People & Body",
        "name": "person mountain biking",
        "unicode": "1f6b5"
    },
    ":mountain_biking_man:": {
        "category": "People & Body",
        "name": "man mountain biking",
        "unicode": "1f6b5-2642",
        "unicode_alt": "1f6b5-200d-2642-fe0f"
    },
    ":mountain_biking_woman:": {
        "category": "People & Body",
        "name": "woman mountain biking",
        "unicode": "1f6b5-2640",
        "unicode_alt": "1f6b5-200d-2640-fe0f"
    },
    ":mountain_cableway:": {
        "category": "Travel & Places",
        "name": "mountain cableway",
        "unicode": "1f6a0"
    },
    ":mountain_railway:": {
        "category": "Travel & Places",
        "name": "mountain railway",
        "unicode": "1f69e"
    },
    ":mountain_snow:": {
        "category": "Travel & Places",
        "name": "snow-capped mountain",
        "unicode": "1f3d4",
        "unicode_alt": "1f3d4-fe0f"
    },
    ":mouse2:": {
        "category": "Animals & Nature",
        "name": "mouse",
        "unicode": "1f401"
    },
    ":mouse:": {
        "category": "Animals & Nature",
        "name": "mouse face",
        "unicode": "1f42d"
    },
    ":mouse_trap:": {
        "category": "Objects",
        "name": "mouse trap",
        "unicode": "1faa4"
    },
    ":movie_camera:": {
        "category": "Objects",
        "name": "movie camera",
        "unicode": "1f3a5"
    },
    ":moyai:": {
        "category": "Objects",
        "name": "moai",
        "unicode": "1f5ff"
    },
    ":mozambique:": {
        "category": "Flags",
        "name": "flag: Mozambique",
        "unicode": "1f1f2-1f1ff"
    },
    ":mrs_claus:": {
        "category": "People & Body",
        "name": "Mrs. Claus",
        "unicode": "1f936"
    },
    ":muscle:": {
        "category": "People & Body",
        "name": "flexed biceps",
        "unicode": "1f4aa"
    },
    ":mushroom:": {
        "category": "Animals & Nature",
        "name": "mushroom",
        "unicode": "1f344"
    },
    ":musical_keyboard:": {
        "category": "Objects",
        "name": "musical keyboard",
        "unicode": "1f3b9"
    },
    ":musical_note:": {
        "category": "Objects",
        "name": "musical note",
        "unicode": "1f3b5"
    },
    ":musical_score:": {
        "category": "Objects",
        "name": "musical score",
        "unicode": "1f3bc"
    },
    ":mute:": {
        "category": "Objects",
        "name": "muted speaker",
        "unicode": "1f507"
    },
    ":mx_claus:": {
        "category": "People & Body",
        "name": "mx claus",
        "unicode": "1f9d1-1f384",
        "unicode_alt": "1f9d1-200d-1f384"
    },
    ":myanmar:": {
        "category": "Flags",
        "name": "flag: Myanmar (Burma)",
        "unicode": "1f1f2-1f1f2"
    },
    ":nail_care:": {
        "category": "People & Body",
        "name": "nail polish",
        "unicode": "1f485"
    },
    ":name_badge:": {
        "category": "Symbols",
        "name": "name badge",
        "unicode": "1f4db"
    },
    ":namibia:": {
        "category": "Flags",
        "name": "flag: Namibia",
        "unicode": "1f1f3-1f1e6"
    },
    ":national_park:": {
        "category": "Travel & Places",
        "name": "national park",
        "unicode": "1f3de",
        "unicode_alt": "1f3de-fe0f"
    },
    ":nauru:": {
        "category": "Flags",
        "name": "flag: Nauru",
        "unicode": "1f1f3-1f1f7"
    },
    ":nauseated_face:": {
        "category": "Smileys & Emotion",
        "name": "nauseated face",
        "unicode": "1f922"
    },
    ":nazar_amulet:": {
        "category": "Objects",
        "name": "nazar amulet",
        "unicode": "1f9ff"
    },
    ":necktie:": {
        "category": "Objects",
        "name": "necktie",
        "unicode": "1f454"
    },
    ":negative_squared_cross_mark:": {
        "category": "Symbols",
        "name": "cross mark button",
        "unicode": "274e"
    },
    ":nepal:": {
        "category": "Flags",
        "name": "flag: Nepal",
        "unicode": "1f1f3-1f1f5"
    },
    ":nerd_face:": {
        "category": "Smileys & Emotion",
        "name": "nerd face",
        "unicode": "1f913"
    },
    ":nest_with_eggs:": {
        "category": "Animals & Nature",
        "name": "nest with eggs",
        "unicode": "1faba"
    },
    ":nesting_dolls:": {
        "category": "Activities",
        "name": "nesting dolls",
        "unicode": "1fa86"
    },
    ":netherlands:": {
        "category": "Flags",
        "name": "flag: Netherlands",
        "unicode": "1f1f3-1f1f1"
    },
    ":neutral_face:": {
        "category": "Smileys & Emotion",
        "name": "neutral face",
        "unicode": "1f610"
    },
    ":new:": {
        "category": "Symbols",
        "name": "NEW button",
        "unicode": "1f195"
    },
    ":new_caledonia:": {
        "category": "Flags",
        "name": "flag: New Caledonia",
        "unicode": "1f1f3-1f1e8"
    },
    ":new_moon:": {
        "category": "Travel & Places",
        "name": "new moon",
        "unicode": "1f311"
    },
    ":new_moon_with_face:": {
        "category": "Travel & Places",
        "name": "new moon face",
        "unicode": "1f31a"
    },
    ":new_zealand:": {
        "category": "Flags",
        "name": "flag: New Zealand",
        "unicode": "1f1f3-1f1ff"
    },
    ":newspaper:": {
        "category": "Objects",
        "name": "newspaper",
        "unicode": "1f4f0"
    },
    ":newspaper_roll:": {
        "category": "Objects",
        "name": "rolled-up newspaper",
        "unicode": "1f5de",
        "unicode_alt": "1f5de-fe0f"
    },
    ":next_track_button:": {
        "category": "Symbols",
        "name": "next track button",
        "unicode": "23ed",
        "unicode_alt": "23ed-fe0f"
    },
    ":ng:": {
        "category": "Symbols",
        "name": "NG button",
        "unicode": "1f196"
    },
    ":nicaragua:": {
        "category": "Flags",
        "name": "flag: Nicaragua",
        "unicode": "1f1f3-1f1ee"
    },
    ":niger:": {
        "category": "Flags",
        "name": "flag: Niger",
        "unicode": "1f1f3-1f1ea"
    },
    ":nigeria:": {
        "category": "Flags",
        "name": "flag: Nigeria",
        "unicode": "1f1f3-1f1ec"
    },
    ":night_with_stars:": {
        "category": "Travel & Places",
        "name": "night with stars",
        "unicode": "1f303"
    },
    ":nine:": {
        "category": "Symbols",
        "name": "keycap: 9",
        "unicode": "0039-20e3",
        "unicode_alt": "0039-fe0f-20e3"
    },
    ":ninja:": {
        "category": "People & Body",
        "name": "ninja",
        "unicode": "1f977"
    },
    ":niue:": {
        "category": "Flags",
        "name": "flag: Niue",
        "unicode": "1f1f3-1f1fa"
    },
    ":no_bell:": {
        "category": "Objects",
        "name": "bell with slash",
        "unicode": "1f515"
    },
    ":no_bicycles:": {
        "category": "Symbols",
        "name": "no bicycles",
        "unicode": "1f6b3"
    },
    ":no_entry:": {
        "category": "Symbols",
        "name": "no entry",
        "unicode": "26d4"
    },
    ":no_entry_sign:": {
        "category": "Symbols",
        "name": "prohibited",
        "unicode": "1f6ab"
    },
    ":no_good:": {
        "category": "People & Body",
        "name": "person gesturing NO",
        "unicode": "1f645"
    },
    ":no_good_man:": {
        "category": "People & Body",
        "name": "man gesturing NO",
        "unicode": "1f645-2642",
        "unicode_alt": "1f645-200d-2642-fe0f"
    },
    ":no_good_woman:": {
        "category": "People & Body",
        "name": "woman gesturing NO",
        "unicode": "1f645-2640",
        "unicode_alt": "1f645-200d-2640-fe0f"
    },
    ":no_mobile_phones:": {
        "category": "Symbols",
        "name": "no mobile phones",
        "unicode": "1f4f5"
    },
    ":no_mouth:": {
        "category": "Smileys & Emotion",
        "name": "face without mouth",
        "unicode": "1f636"
    },
    ":no_pedestrians:": {
        "category": "Symbols",
        "name": "no pedestrians",
        "unicode": "1f6b7"
    },
    ":no_smoking:": {
        "category": "Symbols",
        "name": "no smoking",
        "unicode": "1f6ad"
    },
    ":non-potable_water:": {
        "category": "Symbols",
        "name": "non-potable water",
        "unicode": "1f6b1"
    },
    ":norfolk_island:": {
        "category": "Flags",
        "name": "flag: Norfolk Island",
        "unicode": "1f1f3-1f1eb"
    },
    ":north_korea:": {
        "category": "Flags",
        "name": "flag: North Korea",
        "unicode": "1f1f0-1f1f5"
    },
    ":northern_mariana_islands:": {
        "category": "Flags",
        "name": "flag: Northern Mariana Islands",
        "unicode": "1f1f2-1f1f5"
    },
    ":norway:": {
        "category": "Flags",
        "name": "flag: Norway",
        "unicode": "1f1f3-1f1f4"
    },
    ":nose:": {
        "category": "People & Body",
        "name": "nose",
        "unicode": "1f443"
    },
    ":notebook:": {
        "category": "Objects",
        "name": "notebook",
        "unicode": "1f4d3"
    },
    ":notebook_with_decorative_cover:": {
        "category": "Objects",
        "name": "notebook with decorative cover",
        "unicode": "1f4d4"
    },
    ":notes:": {
        "category": "Objects",
        "name": "musical notes",
        "unicode": "1f3b6"
    },
    ":nut_and_bolt:": {
        "category": "Objects",
        "name": "nut and bolt",
        "unicode": "1f529"
    },
    ":o2:": {
        "category": "Symbols",
        "name": "O button (blood type)",
        "unicode": "1f17e",
        "unicode_alt": "1f17e-fe0f"
    },
    ":o:": {
        "category": "Symbols",
        "name": "hollow red circle",
        "unicode": "2b55"
    },
    ":ocean:": {
        "category": "Travel & Places",
        "name": "water wave",
        "unicode": "1f30a"
    },
    ":octopus:": {
        "category": "Animals & Nature",
        "name": "octopus",
        "unicode": "1f419"
    },
    ":oden:": {
        "category": "Food & Drink",
        "name": "oden",
        "unicode": "1f362"
    },
    ":office:": {
        "category": "Travel & Places",
        "name": "office building",
        "unicode": "1f3e2"
    },
    ":office_worker:": {
        "category": "People & Body",
        "name": "office worker",
        "unicode": "1f9d1-1f4bc",
        "unicode_alt": "1f9d1-200d-1f4bc"
    },
    ":oil_drum:": {
        "category": "Travel & Places",
        "name": "oil drum",
        "unicode": "1f6e2",
        "unicode_alt": "1f6e2-fe0f"
    },
    ":ok:": {
        "category": "Symbols",
        "name": "OK button",
        "unicode": "1f197"
    },
    ":ok_hand:": {
        "category": "People & Body",
        "name": "OK hand",
        "unicode": "1f44c"
    },
    ":ok_man:": {
        "category": "People & Body",
        "name": "man gesturing OK",
        "unicode": "1f646-2642",
        "unicode_alt": "1f646-200d-2642-fe0f"
    },
    ":ok_person:": {
        "category": "People & Body",
        "name": "person gesturing OK",
        "unicode": "1f646"
    },
    ":ok_woman:": {
        "category": "People & Body",
        "name": "woman gesturing OK",
        "unicode": "1f646-2640",
        "unicode_alt": "1f646-200d-2640-fe0f"
    },
    ":old_key:": {
        "category": "Objects",
        "name": "old key",
        "unicode": "1f5dd",
        "unicode_alt": "1f5dd-fe0f"
    },
    ":older_adult:": {
        "category": "People & Body",
        "name": "older person",
        "unicode": "1f9d3"
    },
    ":older_man:": {
        "category": "People & Body",
        "name": "old man",
        "unicode": "1f474"
    },
    ":older_woman:": {
        "category": "People & Body",
        "name": "old woman",
        "unicode": "1f475"
    },
    ":olive:": {
        "category": "Food & Drink",
        "name": "olive",
        "unicode": "1fad2"
    },
    ":om:": {
        "category": "Symbols",
        "name": "om",
        "unicode": "1f549",
        "unicode_alt": "1f549-fe0f"
    },
    ":oman:": {
        "category": "Flags",
        "name": "flag: Oman",
        "unicode": "1f1f4-1f1f2"
    },
    ":on:": {
        "category": "Symbols",
        "name": "ON! arrow",
        "unicode": "1f51b"
    },
    ":oncoming_automobile:": {
        "category": "Travel & Places",
        "name": "oncoming automobile",
        "unicode": "1f698"
    },
    ":oncoming_bus:": {
        "category": "Travel & Places",
        "name": "oncoming bus",
        "unicode": "1f68d"
    },
    ":oncoming_police_car:": {
        "category": "Travel & Places",
        "name": "oncoming police car",
        "unicode": "1f694"
    },
    ":oncoming_taxi:": {
        "category": "Travel & Places",
        "name": "oncoming taxi",
        "unicode": "1f696"
    },
    ":one:": {
        "category": "Symbols",
        "name": "keycap: 1",
        "unicode": "0031-20e3",
        "unicode_alt": "0031-fe0f-20e3"
    },
    ":one_piece_swimsuit:": {
        "category": "Objects",
        "name": "one-piece swimsuit",
        "unicode": "1fa71"
    },
    ":onion:": {
        "category": "Food & Drink",
        "name": "onion",
        "unicode": "1f9c5"
    },
    ":open_file_folder:": {
        "category": "Objects",
        "name": "open file folder",
        "unicode": "1f4c2"
    },
    ":open_hands:": {
        "category": "People & Body",
        "name": "open hands",
        "unicode": "1f450"
    },
    ":open_mouth:": {
        "category": "Smileys & Emotion",
        "name": "face with open mouth",
        "unicode": "1f62e"
    },
    ":open_umbrella:": {
        "category": "Travel & Places",
        "name": "umbrella",
        "unicode": "2602",
        "unicode_alt": "2602-fe0f"
    },
    ":ophiuchus:": {
        "category": "Symbols",
        "name": "Ophiuchus",
        "unicode": "26ce"
    },
    ":orange_book:": {
        "category": "Objects",
        "name": "orange book",
        "unicode": "1f4d9"
    },
    ":orange_circle:": {
        "category": "Symbols",
        "name": "orange circle",
        "unicode": "1f7e0"
    },
    ":orange_heart:": {
        "category": "Smileys & Emotion",
        "name": "orange heart",
        "unicode": "1f9e1"
    },
    ":orange_square:": {
        "category": "Symbols",
        "name": "orange square",
        "unicode": "1f7e7"
    },
    ":orangutan:": {
        "category": "Animals & Nature",
        "name": "orangutan",
        "unicode": "1f9a7"
    },
    ":orthodox_cross:": {
        "category": "Symbols",
        "name": "orthodox cross",
        "unicode": "2626",
        "unicode_alt": "2626-fe0f"
    },
    ":otter:": {
        "category": "Animals & Nature",
        "name": "otter",
        "unicode": "1f9a6"
    },
    ":outbox_tray:": {
        "category": "Objects",
        "name": "outbox tray",
        "unicode": "1f4e4"
    },
    ":owl:": {
        "category": "Animals & Nature",
        "name": "owl",
        "unicode": "1f989"
    },
    ":ox:": {
        "category": "Animals & Nature",
        "name": "ox",
        "unicode": "1f402"
    },
    ":oyster:": {
        "category": "Food & Drink",
        "name": "oyster",
        "unicode": "1f9aa"
    },
    ":package:": {
        "category": "Objects",
        "name": "package",
        "unicode": "1f4e6"
    },
    ":page_facing_up:": {
        "category": "Objects",
        "name": "page facing up",
        "unicode": "1f4c4"
    },
    ":page_with_curl:": {
        "category": "Objects",
        "name": "page with curl",
        "unicode": "1f4c3"
    },
    ":pager:": {
        "category": "Objects",
        "name": "pager",
        "unicode": "1f4df"
    },
    ":paintbrush:": {
        "category": "Objects",
        "name": "paintbrush",
        "unicode": "1f58c",
        "unicode_alt": "1f58c-fe0f"
    },
    ":pakistan:": {
        "category": "Flags",
        "name": "flag: Pakistan",
        "unicode": "1f1f5-1f1f0"
    },
    ":palau:": {
        "category": "Flags",
        "name": "flag: Palau",
        "unicode": "1f1f5-1f1fc"
    },
    ":palestinian_territories:": {
        "category": "Flags",
        "name": "flag: Palestinian Territories",
        "unicode": "1f1f5-1f1f8"
    },
    ":palm_down_hand:": {
        "category": "People & Body",
        "name": "palm down hand",
        "unicode": "1faf3"
    },
    ":palm_tree:": {
        "category": "Animals & Nature",
        "name": "palm tree",
        "unicode": "1f334"
    },
    ":palm_up_hand:": {
        "category": "People & Body",
        "name": "palm up hand",
        "unicode": "1faf4"
    },
    ":palms_up_together:": {
        "category": "People & Body",
        "name": "palms up together",
        "unicode": "1f932"
    },
    ":panama:": {
        "category": "Flags",
        "name": "flag: Panama",
        "unicode": "1f1f5-1f1e6"
    },
    ":pancakes:": {
        "category": "Food & Drink",
        "name": "pancakes",
        "unicode": "1f95e"
    },
    ":panda_face:": {
        "category": "Animals & Nature",
        "name": "panda",
        "unicode": "1f43c"
    },
    ":paperclip:": {
        "category": "Objects",
        "name": "paperclip",
        "unicode": "1f4ce"
    },
    ":paperclips:": {
        "category": "Objects",
        "name": "linked paperclips",
        "unicode": "1f587",
        "unicode_alt": "1f587-fe0f"
    },
    ":papua_new_guinea:": {
        "category": "Flags",
        "name": "flag: Papua New Guinea",
        "unicode": "1f1f5-1f1ec"
    },
    ":parachute:": {
        "category": "Travel & Places",
        "name": "parachute",
        "unicode": "1fa82"
    },
    ":paraguay:": {
        "category": "Flags",
        "name": "flag: Paraguay",
        "unicode": "1f1f5-1f1fe"
    },
    ":parasol_on_ground:": {
        "category": "Travel & Places",
        "name": "umbrella on ground",
        "unicode": "26f1",
        "unicode_alt": "26f1-fe0f"
    },
    ":parking:": {
        "category": "Symbols",
        "name": "P button",
        "unicode": "1f17f",
        "unicode_alt": "1f17f-fe0f"
    },
    ":parrot:": {
        "category": "Animals & Nature",
        "name": "parrot",
        "unicode": "1f99c"
    },
    ":part_alternation_mark:": {
        "category": "Symbols",
        "name": "part alternation mark",
        "unicode": "303d",
        "unicode_alt": "303d-fe0f"
    },
    ":partly_sunny:": {
        "category": "Travel & Places",
        "name": "sun behind cloud",
        "unicode": "26c5"
    },
    ":partying_face:": {
        "category": "Smileys & Emotion",
        "name": "partying face",
        "unicode": "1f973"
    },
    ":passenger_ship:": {
        "category": "Travel & Places",
        "name": "passenger ship",
        "unicode": "1f6f3",
        "unicode_alt": "1f6f3-fe0f"
    },
    ":passport_control:": {
        "category": "Symbols",
        "name": "passport control",
        "unicode": "1f6c2"
    },
    ":pause_button:": {
        "category": "Symbols",
        "name": "pause button",
        "unicode": "23f8",
        "unicode_alt": "23f8-fe0f"
    },
    ":pea_pod:": {
        "category": "Food & Drink",
        "name": "pea pod",
        "unicode": "1fadb"
    },
    ":peace_symbol:": {
        "category": "Symbols",
        "name": "peace symbol",
        "unicode": "262e",
        "unicode_alt": "262e-fe0f"
    },
    ":peach:": {
        "category": "Food & Drink",
        "name": "peach",
        "unicode": "1f351"
    },
    ":peacock:": {
        "category": "Animals & Nature",
        "name": "peacock",
        "unicode": "1f99a"
    },
    ":peanuts:": {
        "category": "Food & Drink",
        "name": "peanuts",
        "unicode": "1f95c"
    },
    ":pear:": {
        "category": "Food & Drink",
        "name": "pear",
        "unicode": "1f350"
    },
    ":pen:": {
        "category": "Objects",
        "name": "pen",
        "unicode": "1f58a",
        "unicode_alt": "1f58a-fe0f"
    },
    ":pencil2:": {
        "category": "Objects",
        "name": "pencil",
        "unicode": "270f",
        "unicode_alt": "270f-fe0f"
    },
    ":penguin:": {
        "category": "Animals & Nature",
        "name": "penguin",
        "unicode": "1f427"
    },
    ":pensive:": {
        "category": "Smileys & Emotion",
        "name": "pensive face",
        "unicode": "1f614"
    },
    ":people_holding_hands:": {
        "category": "People & Body",
        "name": "people holding hands",
        "unicode": "1f9d1-1f91d-1f9d1",
        "unicode_alt": "1f9d1-200d-1f91d-200d-1f9d1"
    },
    ":people_hugging:": {
        "category": "People & Body",
        "name": "people hugging",
        "unicode": "1fac2"
    },
    ":performing_arts:": {
        "category": "Activities",
        "name": "performing arts",
        "unicode": "1f3ad"
    },
    ":persevere:": {
        "category": "Smileys & Emotion",
        "name": "persevering face",
        "unicode": "1f623"
    },
    ":person_bald:": {
        "category": "People & Body",
        "name": "person: bald",
        "unicode": "1f9d1-1f9b2",
        "unicode_alt": "1f9d1-200d-1f9b2"
    },
    ":person_curly_hair:": {
        "category": "People & Body",
        "name": "person: curly hair",
        "unicode": "1f9d1-1f9b1",
        "unicode_alt": "1f9d1-200d-1f9b1"
    },
    ":person_feeding_baby:": {
        "category": "People & Body",
        "name": "person feeding baby",
        "unicode": "1f9d1-1f37c",
        "unicode_alt": "1f9d1-200d-1f37c"
    },
    ":person_fencing:": {
        "category": "People & Body",
        "name": "person fencing",
        "unicode": "1f93a"
    },
    ":person_in_manual_wheelchair:": {
        "category": "People & Body",
        "name": "person in manual wheelchair",
        "unicode": "1f9d1-1f9bd",
        "unicode_alt": "1f9d1-200d-1f9bd"
    },
    ":person_in_motorized_wheelchair:": {
        "category": "People & Body",
        "name": "person in motorized wheelchair",
        "unicode": "1f9d1-1f9bc",
        "unicode_alt": "1f9d1-200d-1f9bc"
    },
    ":person_in_tuxedo:": {
        "category": "People & Body",
        "name": "person in tuxedo",
        "unicode": "1f935"
    },
    ":person_red_hair:": {
        "category": "People & Body",
        "name": "person: red hair",
        "unicode": "1f9d1-1f9b0",
        "unicode_alt": "1f9d1-200d-1f9b0"
    },
    ":person_white_hair:": {
        "category": "People & Body",
        "name": "person: white hair",
        "unicode": "1f9d1-1f9b3",
        "unicode_alt": "1f9d1-200d-1f9b3"
    },
    ":person_with_crown:": {
        "category": "People & Body",
        "name": "person with crown",
        "unicode": "1fac5"
    },
    ":person_with_probing_cane:": {
        "category": "People & Body",
        "name": "person with white cane",
        "unicode": "1f9d1-1f9af",
        "unicode_alt": "1f9d1-200d-1f9af"
    },
    ":person_with_turban:": {
        "category": "People & Body",
        "name": "person wearing turban",
        "unicode": "1f473"
    },
    ":person_with_veil:": {
        "category": "People & Body",
        "name": "person with veil",
        "unicode": "1f470"
    },
    ":peru:": {
        "category": "Flags",
        "name": "flag: Peru",
        "unicode": "1f1f5-1f1ea"
    },
    ":petri_dish:": {
        "category": "Objects",
        "name": "petri dish",
        "unicode": "1f9eb"
    },
    ":philippines:": {
        "category": "Flags",
        "name": "flag: Philippines",
        "unicode": "1f1f5-1f1ed"
    },
    ":phone:": {
        "category": "Objects",
        "name": "telephone",
        "unicode": "260e",
        "unicode_alt": "260e-fe0f"
    },
    ":pick:": {
        "category": "Objects",
        "name": "pick",
        "unicode": "26cf",
        "unicode_alt": "26cf-fe0f"
    },
    ":pickup_truck:": {
        "category": "Travel & Places",
        "name": "pickup truck",
        "unicode": "1f6fb"
    },
    ":pie:": {
        "category": "Food & Drink",
        "name": "pie",
        "unicode": "1f967"
    },
    ":pig2:": {
        "category": "Animals & Nature",
        "name": "pig",
        "unicode": "1f416"
    },
    ":pig:": {
        "category": "Animals & Nature",
        "name": "pig face",
        "unicode": "1f437"
    },
    ":pig_nose:": {
        "category": "Animals & Nature",
        "name": "pig nose",
        "unicode": "1f43d"
    },
    ":pill:": {
        "category": "Objects",
        "name": "pill",
        "unicode": "1f48a"
    },
    ":pilot:": {
        "category": "People & Body",
        "name": "pilot",
        "unicode": "1f9d1-2708",
        "unicode_alt": "1f9d1-200d-2708-fe0f"
    },
    ":pinata:": {
        "category": "Activities",
        "name": "pi\u00f1ata",
        "unicode": "1fa85"
    },
    ":pinched_fingers:": {
        "category": "People & Body",
        "name": "pinched fingers",
        "unicode": "1f90c"
    },
    ":pinching_hand:": {
        "category": "People & Body",
        "name": "pinching hand",
        "unicode": "1f90f"
    },
    ":pineapple:": {
        "category": "Food & Drink",
        "name": "pineapple",
        "unicode": "1f34d"
    },
    ":ping_pong:": {
        "category": "Activities",
        "name": "ping pong",
        "unicode": "1f3d3"
    },
    ":pink_heart:": {
        "category": "Smileys & Emotion",
        "name": "pink heart",
        "unicode": "1fa77"
    },
    ":pirate_flag:": {
        "category": "Flags",
        "name": "pirate flag",
        "unicode": "1f3f4-2620",
        "unicode_alt": "1f3f4-200d-2620-fe0f"
    },
    ":pisces:": {
        "category": "Symbols",
        "name": "Pisces",
        "unicode": "2653"
    },
    ":pitcairn_islands:": {
        "category": "Flags",
        "name": "flag: Pitcairn Islands",
        "unicode": "1f1f5-1f1f3"
    },
    ":pizza:": {
        "category": "Food & Drink",
        "name": "pizza",
        "unicode": "1f355"
    },
    ":placard:": {
        "category": "Objects",
        "name": "placard",
        "unicode": "1faa7"
    },
    ":place_of_worship:": {
        "category": "Symbols",
        "name": "place of worship",
        "unicode": "1f6d0"
    },
    ":plate_with_cutlery:": {
        "category": "Food & Drink",
        "name": "fork and knife with plate",
        "unicode": "1f37d",
        "unicode_alt": "1f37d-fe0f"
    },
    ":play_or_pause_button:": {
        "category": "Symbols",
        "name": "play or pause button",
        "unicode": "23ef",
        "unicode_alt": "23ef-fe0f"
    },
    ":playground_slide:": {
        "category": "Travel & Places",
        "name": "playground slide",
        "unicode": "1f6dd"
    },
    ":pleading_face:": {
        "category": "Smileys & Emotion",
        "name": "pleading face",
        "unicode": "1f97a"
    },
    ":plunger:": {
        "category": "Objects",
        "name": "plunger",
        "unicode": "1faa0"
    },
    ":point_down:": {
        "category": "People & Body",
        "name": "backhand index pointing down",
        "unicode": "1f447"
    },
    ":point_left:": {
        "category": "People & Body",
        "name": "backhand index pointing left",
        "unicode": "1f448"
    },
    ":point_right:": {
        "category": "People & Body",
        "name": "backhand index pointing right",
        "unicode": "1f449"
    },
    ":point_up:": {
        "category": "People & Body",
        "name": "index pointing up",
        "unicode": "261d",
        "unicode_alt": "261d-fe0f"
    },
    ":point_up_2:": {
        "category": "People & Body",
        "name": "backhand index pointing up",
        "unicode": "1f446"
    },
    ":poland:": {
        "category": "Flags",
        "name": "flag: Poland",
        "unicode": "1f1f5-1f1f1"
    },
    ":polar_bear:": {
        "category": "Animals & Nature",
        "name": "polar bear",
        "unicode": "1f43b-2744",
        "unicode_alt": "1f43b-200d-2744-fe0f"
    },
    ":police_car:": {
        "category": "Travel & Places",
        "name": "police car",
        "unicode": "1f693"
    },
    ":police_officer:": {
        "category": "People & Body",
        "name": "police officer",
        "unicode": "1f46e"
    },
    ":policeman:": {
        "category": "People & Body",
        "name": "man police officer",
        "unicode": "1f46e-2642",
        "unicode_alt": "1f46e-200d-2642-fe0f"
    },
    ":policewoman:": {
        "category": "People & Body",
        "name": "woman police officer",
        "unicode": "1f46e-2640",
        "unicode_alt": "1f46e-200d-2640-fe0f"
    },
    ":poodle:": {
        "category": "Animals & Nature",
        "name": "poodle",
        "unicode": "1f429"
    },
    ":popcorn:": {
        "category": "Food & Drink",
        "name": "popcorn",
        "unicode": "1f37f"
    },
    ":portugal:": {
        "category": "Flags",
        "name": "flag: Portugal",
        "unicode": "1f1f5-1f1f9"
    },
    ":post_office:": {
        "category": "Travel & Places",
        "name": "Japanese post office",
        "unicode": "1f3e3"
    },
    ":postal_horn:": {
        "category": "Objects",
        "name": "postal horn",
        "unicode": "1f4ef"
    },
    ":postbox:": {
        "category": "Objects",
        "name": "postbox",
        "unicode": "1f4ee"
    },
    ":potable_water:": {
        "category": "Symbols",
        "name": "potable water",
        "unicode": "1f6b0"
    },
    ":potato:": {
        "category": "Food & Drink",
        "name": "potato",
        "unicode": "1f954"
    },
    ":potted_plant:": {
        "category": "Animals & Nature",
        "name": "potted plant",
        "unicode": "1fab4"
    },
    ":pouch:": {
        "category": "Objects",
        "name": "clutch bag",
        "unicode": "1f45d"
    },
    ":poultry_leg:": {
        "category": "Food & Drink",
        "name": "poultry leg",
        "unicode": "1f357"
    },
    ":pound:": {
        "category": "Objects",
        "name": "pound banknote",
        "unicode": "1f4b7"
    },
    ":pouring_liquid:": {
        "category": "Food & Drink",
        "name": "pouring liquid",
        "unicode": "1fad7"
    },
    ":pouting_cat:": {
        "category": "Smileys & Emotion",
        "name": "pouting cat",
        "unicode": "1f63e"
    },
    ":pouting_face:": {
        "category": "People & Body",
        "name": "person pouting",
        "unicode": "1f64e"
    },
    ":pouting_man:": {
        "category": "People & Body",
        "name": "man pouting",
        "unicode": "1f64e-2642",
        "unicode_alt": "1f64e-200d-2642-fe0f"
    },
    ":pouting_woman:": {
        "category": "People & Body",
        "name": "woman pouting",
        "unicode": "1f64e-2640",
        "unicode_alt": "1f64e-200d-2640-fe0f"
    },
    ":pray:": {
        "category": "People & Body",
        "name": "folded hands",
        "unicode": "1f64f"
    },
    ":prayer_beads:": {
        "category": "Objects",
        "name": "prayer beads",
        "unicode": "1f4ff"
    },
    ":pregnant_man:": {
        "category": "People & Body",
        "name": "pregnant man",
        "unicode": "1fac3"
    },
    ":pregnant_person:": {
        "category": "People & Body",
        "name": "pregnant person",
        "unicode": "1fac4"
    },
    ":pregnant_woman:": {
        "category": "People & Body",
        "name": "pregnant woman",
        "unicode": "1f930"
    },
    ":pretzel:": {
        "category": "Food & Drink",
        "name": "pretzel",
        "unicode": "1f968"
    },
    ":previous_track_button:": {
        "category": "Symbols",
        "name": "last track button",
        "unicode": "23ee",
        "unicode_alt": "23ee-fe0f"
    },
    ":prince:": {
        "category": "People & Body",
        "name": "prince",
        "unicode": "1f934"
    },
    ":princess:": {
        "category": "People & Body",
        "name": "princess",
        "unicode": "1f478"
    },
    ":printer:": {
        "category": "Objects",
        "name": "printer",
        "unicode": "1f5a8",
        "unicode_alt": "1f5a8-fe0f"
    },
    ":probing_cane:": {
        "category": "Objects",
        "name": "white cane",
        "unicode": "1f9af"
    },
    ":puerto_rico:": {
        "category": "Flags",
        "name": "flag: Puerto Rico",
        "unicode": "1f1f5-1f1f7"
    },
    ":purple_circle:": {
        "category": "Symbols",
        "name": "purple circle",
        "unicode": "1f7e3"
    },
    ":purple_heart:": {
        "category": "Smileys & Emotion",
        "name": "purple heart",
        "unicode": "1f49c"
    },
    ":purple_square:": {
        "category": "Symbols",
        "name": "purple square",
        "unicode": "1f7ea"
    },
    ":purse:": {
        "category": "Objects",
        "name": "purse",
        "unicode": "1f45b"
    },
    ":pushpin:": {
        "category": "Objects",
        "name": "pushpin",
        "unicode": "1f4cc"
    },
    ":put_litter_in_its_place:": {
        "category": "Symbols",
        "name": "litter in bin sign",
        "unicode": "1f6ae"
    },
    ":qatar:": {
        "category": "Flags",
        "name": "flag: Qatar",
        "unicode": "1f1f6-1f1e6"
    },
    ":question:": {
        "category": "Symbols",
        "name": "red question mark",
        "unicode": "2753"
    },
    ":rabbit2:": {
        "category": "Animals & Nature",
        "name": "rabbit",
        "unicode": "1f407"
    },
    ":rabbit:": {
        "category": "Animals & Nature",
        "name": "rabbit face",
        "unicode": "1f430"
    },
    ":raccoon:": {
        "category": "Animals & Nature",
        "name": "raccoon",
        "unicode": "1f99d"
    },
    ":racehorse:": {
        "category": "Animals & Nature",
        "name": "horse",
        "unicode": "1f40e"
    },
    ":racing_car:": {
        "category": "Travel & Places",
        "name": "racing car",
        "unicode": "1f3ce",
        "unicode_alt": "1f3ce-fe0f"
    },
    ":radio:": {
        "category": "Objects",
        "name": "radio",
        "unicode": "1f4fb"
    },
    ":radio_button:": {
        "category": "Symbols",
        "name": "radio button",
        "unicode": "1f518"
    },
    ":radioactive:": {
        "category": "Symbols",
        "name": "radioactive",
        "unicode": "2622",
        "unicode_alt": "2622-fe0f"
    },
    ":rage:": {
        "category": "Smileys & Emotion",
        "name": "enraged face",
        "unicode": "1f621"
    },
    ":railway_car:": {
        "category": "Travel & Places",
        "name": "railway car",
        "unicode": "1f683"
    },
    ":railway_track:": {
        "category": "Travel & Places",
        "name": "railway track",
        "unicode": "1f6e4",
        "unicode_alt": "1f6e4-fe0f"
    },
    ":rainbow:": {
        "category": "Travel & Places",
        "name": "rainbow",
        "unicode": "1f308"
    },
    ":rainbow_flag:": {
        "category": "Flags",
        "name": "rainbow flag",
        "unicode": "1f3f3-1f308",
        "unicode_alt": "1f3f3-fe0f-200d-1f308"
    },
    ":raised_back_of_hand:": {
        "category": "People & Body",
        "name": "raised back of hand",
        "unicode": "1f91a"
    },
    ":raised_eyebrow:": {
        "category": "Smileys & Emotion",
        "name": "face with raised eyebrow",
        "unicode": "1f928"
    },
    ":raised_hand_with_fingers_splayed:": {
        "category": "People & Body",
        "name": "hand with fingers splayed",
        "unicode": "1f590",
        "unicode_alt": "1f590-fe0f"
    },
    ":raised_hands:": {
        "category": "People & Body",
        "name": "raising hands",
        "unicode": "1f64c"
    },
    ":raising_hand:": {
        "category": "People & Body",
        "name": "person raising hand",
        "unicode": "1f64b"
    },
    ":raising_hand_man:": {
        "category": "People & Body",
        "name": "man raising hand",
        "unicode": "1f64b-2642",
        "unicode_alt": "1f64b-200d-2642-fe0f"
    },
    ":raising_hand_woman:": {
        "category": "People & Body",
        "name": "woman raising hand",
        "unicode": "1f64b-2640",
        "unicode_alt": "1f64b-200d-2640-fe0f"
    },
    ":ram:": {
        "category": "Animals & Nature",
        "name": "ram",
        "unicode": "1f40f"
    },
    ":ramen:": {
        "category": "Food & Drink",
        "name": "steaming bowl",
        "unicode": "1f35c"
    },
    ":rat:": {
        "category": "Animals & Nature",
        "name": "rat",
        "unicode": "1f400"
    },
    ":razor:": {
        "category": "Objects",
        "name": "razor",
        "unicode": "1fa92"
    },
    ":receipt:": {
        "category": "Objects",
        "name": "receipt",
        "unicode": "1f9fe"
    },
    ":record_button:": {
        "category": "Symbols",
        "name": "record button",
        "unicode": "23fa",
        "unicode_alt": "23fa-fe0f"
    },
    ":recycle:": {
        "category": "Symbols",
        "name": "recycling symbol",
        "unicode": "267b",
        "unicode_alt": "267b-fe0f"
    },
    ":red_circle:": {
        "category": "Symbols",
        "name": "red circle",
        "unicode": "1f534"
    },
    ":red_envelope:": {
        "category": "Activities",
        "name": "red envelope",
        "unicode": "1f9e7"
    },
    ":red_haired_man:": {
        "category": "People & Body",
        "name": "man: red hair",
        "unicode": "1f468-1f9b0",
        "unicode_alt": "1f468-200d-1f9b0"
    },
    ":red_haired_woman:": {
        "category": "People & Body",
        "name": "woman: red hair",
        "unicode": "1f469-1f9b0",
        "unicode_alt": "1f469-200d-1f9b0"
    },
    ":red_square:": {
        "category": "Symbols",
        "name": "red square",
        "unicode": "1f7e5"
    },
    ":registered:": {
        "category": "Symbols",
        "name": "registered",
        "unicode": "00ae",
        "unicode_alt": "00ae-fe0f"
    },
    ":relaxed:": {
        "category": "Smileys & Emotion",
        "name": "smiling face",
        "unicode": "263a",
        "unicode_alt": "263a-fe0f"
    },
    ":relieved:": {
        "category": "Smileys & Emotion",
        "name": "relieved face",
        "unicode": "1f60c"
    },
    ":reminder_ribbon:": {
        "category": "Activities",
        "name": "reminder ribbon",
        "unicode": "1f397",
        "unicode_alt": "1f397-fe0f"
    },
    ":repeat:": {
        "category": "Symbols",
        "name": "repeat button",
        "unicode": "1f501"
    },
    ":repeat_one:": {
        "category": "Symbols",
        "name": "repeat single button",
        "unicode": "1f502"
    },
    ":rescue_worker_helmet:": {
        "category": "Objects",
        "name": "rescue worker\u2019s helmet",
        "unicode": "26d1",
        "unicode_alt": "26d1-fe0f"
    },
    ":restroom:": {
        "category": "Symbols",
        "name": "restroom",
        "unicode": "1f6bb"
    },
    ":reunion:": {
        "category": "Flags",
        "name": "flag: R\u00e9union",
        "unicode": "1f1f7-1f1ea"
    },
    ":revolving_hearts:": {
        "category": "Smileys & Emotion",
        "name": "revolving hearts",
        "unicode": "1f49e"
    },
    ":rewind:": {
        "category": "Symbols",
        "name": "fast reverse button",
        "unicode": "23ea"
    },
    ":rhinoceros:": {
        "category": "Animals & Nature",
        "name": "rhinoceros",
        "unicode": "1f98f"
    },
    ":ribbon:": {
        "category": "Activities",
        "name": "ribbon",
        "unicode": "1f380"
    },
    ":rice:": {
        "category": "Food & Drink",
        "name": "cooked rice",
        "unicode": "1f35a"
    },
    ":rice_ball:": {
        "category": "Food & Drink",
        "name": "rice ball",
        "unicode": "1f359"
    },
    ":rice_cracker:": {
        "category": "Food & Drink",
        "name": "rice cracker",
        "unicode": "1f358"
    },
    ":rice_scene:": {
        "category": "Activities",
        "name": "moon viewing ceremony",
        "unicode": "1f391"
    },
    ":right_anger_bubble:": {
        "category": "Smileys & Emotion",
        "name": "right anger bubble",
        "unicode": "1f5ef",
        "unicode_alt": "1f5ef-fe0f"
    },
    ":rightwards_hand:": {
        "category": "People & Body",
        "name": "rightwards hand",
        "unicode": "1faf1"
    },
    ":rightwards_pushing_hand:": {
        "category": "People & Body",
        "name": "rightwards pushing hand",
        "unicode": "1faf8"
    },
    ":ring:": {
        "category": "Objects",
        "name": "ring",
        "unicode": "1f48d"
    },
    ":ring_buoy:": {
        "category": "Travel & Places",
        "name": "ring buoy",
        "unicode": "1f6df"
    },
    ":ringed_planet:": {
        "category": "Travel & Places",
        "name": "ringed planet",
        "unicode": "1fa90"
    },
    ":robot:": {
        "category": "Smileys & Emotion",
        "name": "robot",
        "unicode": "1f916"
    },
    ":rock:": {
        "category": "Travel & Places",
        "name": "rock",
        "unicode": "1faa8"
    },
    ":rocket:": {
        "category": "Travel & Places",
        "name": "rocket",
        "unicode": "1f680"
    },
    ":rofl:": {
        "category": "Smileys & Emotion",
        "name": "rolling on the floor laughing",
        "unicode": "1f923"
    },
    ":roll_eyes:": {
        "category": "Smileys & Emotion",
        "name": "face with rolling eyes",
        "unicode": "1f644"
    },
    ":roll_of_paper:": {
        "category": "Objects",
        "name": "roll of paper",
        "unicode": "1f9fb"
    },
    ":roller_coaster:": {
        "category": "Travel & Places",
        "name": "roller coaster",
        "unicode": "1f3a2"
    },
    ":roller_skate:": {
        "category": "Travel & Places",
        "name": "roller skate",
        "unicode": "1f6fc"
    },
    ":romania:": {
        "category": "Flags",
        "name": "flag: Romania",
        "unicode": "1f1f7-1f1f4"
    },
    ":rooster:": {
        "category": "Animals & Nature",
        "name": "rooster",
        "unicode": "1f413"
    },
    ":rose:": {
        "category": "Animals & Nature",
        "name": "rose",
        "unicode": "1f339"
    },
    ":rosette:": {
        "category": "Animals & Nature",
        "name": "rosette",
        "unicode": "1f3f5",
        "unicode_alt": "1f3f5-fe0f"
    },
    ":rotating_light:": {
        "category": "Travel & Places",
        "name": "police car light",
        "unicode": "1f6a8"
    },
    ":round_pushpin:": {
        "category": "Objects",
        "name": "round pushpin",
        "unicode": "1f4cd"
    },
    ":rowboat:": {
        "category": "People & Body",
        "name": "person rowing boat",
        "unicode": "1f6a3"
    },
    ":rowing_man:": {
        "category": "People & Body",
        "name": "man rowing boat",
        "unicode": "1f6a3-2642",
        "unicode_alt": "1f6a3-200d-2642-fe0f"
    },
    ":rowing_woman:": {
        "category": "People & Body",
        "name": "woman rowing boat",
        "unicode": "1f6a3-2640",
        "unicode_alt": "1f6a3-200d-2640-fe0f"
    },
    ":ru:": {
        "category": "Flags",
        "name": "flag: Russia",
        "unicode": "1f1f7-1f1fa"
    },
    ":rugby_football:": {
        "category": "Activities",
        "name": "rugby football",
        "unicode": "1f3c9"
    },
    ":runner:": {
        "category": "People & Body",
        "name": "person running",
        "unicode": "1f3c3"
    },
    ":running_man:": {
        "category": "People & Body",
        "name": "man running",
        "unicode": "1f3c3-2642",
        "unicode_alt": "1f3c3-200d-2642-fe0f"
    },
    ":running_shirt_with_sash:": {
        "category": "Activities",
        "name": "running shirt",
        "unicode": "1f3bd"
    },
    ":running_woman:": {
        "category": "People & Body",
        "name": "woman running",
        "unicode": "1f3c3-2640",
        "unicode_alt": "1f3c3-200d-2640-fe0f"
    },
    ":rwanda:": {
        "category": "Flags",
        "name": "flag: Rwanda",
        "unicode": "1f1f7-1f1fc"
    },
    ":sa:": {
        "category": "Symbols",
        "name": "Japanese \u201cservice charge\u201d button",
        "unicode": "1f202",
        "unicode_alt": "1f202-fe0f"
    },
    ":safety_pin:": {
        "category": "Objects",
        "name": "safety pin",
        "unicode": "1f9f7"
    },
    ":safety_vest:": {
        "category": "Objects",
        "name": "safety vest",
        "unicode": "1f9ba"
    },
    ":sagittarius:": {
        "category": "Symbols",
        "name": "Sagittarius",
        "unicode": "2650"
    },
    ":sake:": {
        "category": "Food & Drink",
        "name": "sake",
        "unicode": "1f376"
    },
    ":salt:": {
        "category": "Food & Drink",
        "name": "salt",
        "unicode": "1f9c2"
    },
    ":saluting_face:": {
        "category": "Smileys & Emotion",
        "name": "saluting face",
        "unicode": "1fae1"
    },
    ":samoa:": {
        "category": "Flags",
        "name": "flag: Samoa",
        "unicode": "1f1fc-1f1f8"
    },
    ":san_marino:": {
        "category": "Flags",
        "name": "flag: San Marino",
        "unicode": "1f1f8-1f1f2"
    },
    ":sandal:": {
        "category": "Objects",
        "name": "woman\u2019s sandal",
        "unicode": "1f461"
    },
    ":sandwich:": {
        "category": "Food & Drink",
        "name": "sandwich",
        "unicode": "1f96a"
    },
    ":santa:": {
        "category": "People & Body",
        "name": "Santa Claus",
        "unicode": "1f385"
    },
    ":sao_tome_principe:": {
        "category": "Flags",
        "name": "flag: S\u00e3o Tom\u00e9 & Pr\u00edncipe",
        "unicode": "1f1f8-1f1f9"
    },
    ":sari:": {
        "category": "Objects",
        "name": "sari",
        "unicode": "1f97b"
    },
    ":satellite:": {
        "category": "Objects",
        "name": "satellite antenna",
        "unicode": "1f4e1"
    },
    ":saudi_arabia:": {
        "category": "Flags",
        "name": "flag: Saudi Arabia",
        "unicode": "1f1f8-1f1e6"
    },
    ":sauna_man:": {
        "category": "People & Body",
        "name": "man in steamy room",
        "unicode": "1f9d6-2642",
        "unicode_alt": "1f9d6-200d-2642-fe0f"
    },
    ":sauna_person:": {
        "category": "People & Body",
        "name": "person in steamy room",
        "unicode": "1f9d6"
    },
    ":sauna_woman:": {
        "category": "People & Body",
        "name": "woman in steamy room",
        "unicode": "1f9d6-2640",
        "unicode_alt": "1f9d6-200d-2640-fe0f"
    },
    ":sauropod:": {
        "category": "Animals & Nature",
        "name": "sauropod",
        "unicode": "1f995"
    },
    ":saxophone:": {
        "category": "Objects",
        "name": "saxophone",
        "unicode": "1f3b7"
    },
    ":scarf:": {
        "category": "Objects",
        "name": "scarf",
        "unicode": "1f9e3"
    },
    ":school:": {
        "category": "Travel & Places",
        "name": "school",
        "unicode": "1f3eb"
    },
    ":school_satchel:": {
        "category": "Objects",
        "name": "backpack",
        "unicode": "1f392"
    },
    ":scientist:": {
        "category": "People & Body",
        "name": "scientist",
        "unicode": "1f9d1-1f52c",
        "unicode_alt": "1f9d1-200d-1f52c"
    },
    ":scissors:": {
        "category": "Objects",
        "name": "scissors",
        "unicode": "2702",
        "unicode_alt": "2702-fe0f"
    },
    ":scorpion:": {
        "category": "Animals & Nature",
        "name": "scorpion",
        "unicode": "1f982"
    },
    ":scorpius:": {
        "category": "Symbols",
        "name": "Scorpio",
        "unicode": "264f"
    },
    ":scotland:": {
        "category": "Flags",
        "name": "flag: Scotland",
        "unicode": "1f3f4-e0067-e0062-e0073-e0063-e0074-e007f"
    },
    ":scream:": {
        "category": "Smileys & Emotion",
        "name": "face screaming in fear",
        "unicode": "1f631"
    },
    ":scream_cat:": {
        "category": "Smileys & Emotion",
        "name": "weary cat",
        "unicode": "1f640"
    },
    ":screwdriver:": {
        "category": "Objects",
        "name": "screwdriver",
        "unicode": "1fa9b"
    },
    ":scroll:": {
        "category": "Objects",
        "name": "scroll",
        "unicode": "1f4dc"
    },
    ":seal:": {
        "category": "Animals & Nature",
        "name": "seal",
        "unicode": "1f9ad"
    },
    ":seat:": {
        "category": "Travel & Places",
        "name": "seat",
        "unicode": "1f4ba"
    },
    ":secret:": {
        "category": "Symbols",
        "name": "Japanese \u201csecret\u201d button",
        "unicode": "3299",
        "unicode_alt": "3299-fe0f"
    },
    ":see_no_evil:": {
        "category": "Smileys & Emotion",
        "name": "see-no-evil monkey",
        "unicode": "1f648"
    },
    ":seedling:": {
        "category": "Animals & Nature",
        "name": "seedling",
        "unicode": "1f331"
    },
    ":selfie:": {
        "category": "People & Body",
        "name": "selfie",
        "unicode": "1f933"
    },
    ":senegal:": {
        "category": "Flags",
        "name": "flag: Senegal",
        "unicode": "1f1f8-1f1f3"
    },
    ":serbia:": {
        "category": "Flags",
        "name": "flag: Serbia",
        "unicode": "1f1f7-1f1f8"
    },
    ":service_dog:": {
        "category": "Animals & Nature",
        "name": "service dog",
        "unicode": "1f415-1f9ba",
        "unicode_alt": "1f415-200d-1f9ba"
    },
    ":seven:": {
        "category": "Symbols",
        "name": "keycap: 7",
        "unicode": "0037-20e3",
        "unicode_alt": "0037-fe0f-20e3"
    },
    ":sewing_needle:": {
        "category": "Activities",
        "name": "sewing needle",
        "unicode": "1faa1"
    },
    ":seychelles:": {
        "category": "Flags",
        "name": "flag: Seychelles",
        "unicode": "1f1f8-1f1e8"
    },
    ":shaking_face:": {
        "category": "Smileys & Emotion",
        "name": "shaking face",
        "unicode": "1fae8"
    },
    ":shallow_pan_of_food:": {
        "category": "Food & Drink",
        "name": "shallow pan of food",
        "unicode": "1f958"
    },
    ":shamrock:": {
        "category": "Animals & Nature",
        "name": "shamrock",
        "unicode": "2618",
        "unicode_alt": "2618-fe0f"
    },
    ":shark:": {
        "category": "Animals & Nature",
        "name": "shark",
        "unicode": "1f988"
    },
    ":shaved_ice:": {
        "category": "Food & Drink",
        "name": "shaved ice",
        "unicode": "1f367"
    },
    ":sheep:": {
        "category": "Animals & Nature",
        "name": "ewe",
        "unicode": "1f411"
    },
    ":shell:": {
        "category": "Animals & Nature",
        "name": "spiral shell",
        "unicode": "1f41a"
    },
    ":shield:": {
        "category": "Objects",
        "name": "shield",
        "unicode": "1f6e1",
        "unicode_alt": "1f6e1-fe0f"
    },
    ":shinto_shrine:": {
        "category": "Travel & Places",
        "name": "shinto shrine",
        "unicode": "26e9",
        "unicode_alt": "26e9-fe0f"
    },
    ":ship:": {
        "category": "Travel & Places",
        "name": "ship",
        "unicode": "1f6a2"
    },
    ":shirt:": {
        "category": "Objects",
        "name": "t-shirt",
        "unicode": "1f455"
    },
    ":shopping:": {
        "category": "Objects",
        "name": "shopping bags",
        "unicode": "1f6cd",
        "unicode_alt": "1f6cd-fe0f"
    },
    ":shopping_cart:": {
        "category": "Objects",
        "name": "shopping cart",
        "unicode": "1f6d2"
    },
    ":shorts:": {
        "category": "Objects",
        "name": "shorts",
        "unicode": "1fa73"
    },
    ":shower:": {
        "category": "Objects",
        "name": "shower",
        "unicode": "1f6bf"
    },
    ":shrimp:": {
        "category": "Food & Drink",
        "name": "shrimp",
        "unicode": "1f990"
    },
    ":shrug:": {
        "category": "People & Body",
        "name": "person shrugging",
        "unicode": "1f937"
    },
    ":shushing_face:": {
        "category": "Smileys & Emotion",
        "name": "shushing face",
        "unicode": "1f92b"
    },
    ":sierra_leone:": {
        "category": "Flags",
        "name": "flag: Sierra Leone",
        "unicode": "1f1f8-1f1f1"
    },
    ":signal_strength:": {
        "category": "Symbols",
        "name": "antenna bars",
        "unicode": "1f4f6"
    },
    ":singapore:": {
        "category": "Flags",
        "name": "flag: Singapore",
        "unicode": "1f1f8-1f1ec"
    },
    ":singer:": {
        "category": "People & Body",
        "name": "singer",
        "unicode": "1f9d1-1f3a4",
        "unicode_alt": "1f9d1-200d-1f3a4"
    },
    ":sint_maarten:": {
        "category": "Flags",
        "name": "flag: Sint Maarten",
        "unicode": "1f1f8-1f1fd"
    },
    ":six:": {
        "category": "Symbols",
        "name": "keycap: 6",
        "unicode": "0036-20e3",
        "unicode_alt": "0036-fe0f-20e3"
    },
    ":six_pointed_star:": {
        "category": "Symbols",
        "name": "dotted six-pointed star",
        "unicode": "1f52f"
    },
    ":skateboard:": {
        "category": "Travel & Places",
        "name": "skateboard",
        "unicode": "1f6f9"
    },
    ":ski:": {
        "category": "Activities",
        "name": "skis",
        "unicode": "1f3bf"
    },
    ":skier:": {
        "category": "People & Body",
        "name": "skier",
        "unicode": "26f7",
        "unicode_alt": "26f7-fe0f"
    },
    ":skull:": {
        "category": "Smileys & Emotion",
        "name": "skull",
        "unicode": "1f480"
    },
    ":skull_and_crossbones:": {
        "category": "Smileys & Emotion",
        "name": "skull and crossbones",
        "unicode": "2620",
        "unicode_alt": "2620-fe0f"
    },
    ":skunk:": {
        "category": "Animals & Nature",
        "name": "skunk",
        "unicode": "1f9a8"
    },
    ":sled:": {
        "category": "Activities",
        "name": "sled",
        "unicode": "1f6f7"
    },
    ":sleeping:": {
        "category": "Smileys & Emotion",
        "name": "sleeping face",
        "unicode": "1f634"
    },
    ":sleeping_bed:": {
        "category": "People & Body",
        "name": "person in bed",
        "unicode": "1f6cc"
    },
    ":sleepy:": {
        "category": "Smileys & Emotion",
        "name": "sleepy face",
        "unicode": "1f62a"
    },
    ":slightly_frowning_face:": {
        "category": "Smileys & Emotion",
        "name": "slightly frowning face",
        "unicode": "1f641"
    },
    ":slightly_smiling_face:": {
        "category": "Smileys & Emotion",
        "name": "slightly smiling face",
        "unicode": "1f642"
    },
    ":slot_machine:": {
        "category": "Activities",
        "name": "slot machine",
        "unicode": "1f3b0"
    },
    ":sloth:": {
        "category": "Animals & Nature",
        "name": "sloth",
        "unicode": "1f9a5"
    },
    ":slovakia:": {
        "category": "Flags",
        "name": "flag: Slovakia",
        "unicode": "1f1f8-1f1f0"
    },
    ":slovenia:": {
        "category": "Flags",
        "name": "flag: Slovenia",
        "unicode": "1f1f8-1f1ee"
    },
    ":small_airplane:": {
        "category": "Travel & Places",
        "name": "small airplane",
        "unicode": "1f6e9",
        "unicode_alt": "1f6e9-fe0f"
    },
    ":small_blue_diamond:": {
        "category": "Symbols",
        "name": "small blue diamond",
        "unicode": "1f539"
    },
    ":small_orange_diamond:": {
        "category": "Symbols",
        "name": "small orange diamond",
        "unicode": "1f538"
    },
    ":small_red_triangle:": {
        "category": "Symbols",
        "name": "red triangle pointed up",
        "unicode": "1f53a"
    },
    ":small_red_triangle_down:": {
        "category": "Symbols",
        "name": "red triangle pointed down",
        "unicode": "1f53b"
    },
    ":smile:": {
        "category": "Smileys & Emotion",
        "name": "grinning face with smiling eyes",
        "unicode": "1f604"
    },
    ":smile_cat:": {
        "category": "Smileys & Emotion",
        "name": "grinning cat with smiling eyes",
        "unicode": "1f638"
    },
    ":smiley:": {
        "category": "Smileys & Emotion",
        "name": "grinning face with big eyes",
        "unicode": "1f603"
    },
    ":smiley_cat:": {
        "category": "Smileys & Emotion",
        "name": "grinning cat",
        "unicode": "1f63a"
    },
    ":smiling_face_with_tear:": {
        "category": "Smileys & Emotion",
        "name": "smiling face with tear",
        "unicode": "1f972"
    },
    ":smiling_face_with_three_hearts:": {
        "category": "Smileys & Emotion",
        "name": "smiling face with hearts",
        "unicode": "1f970"
    },
    ":smiling_imp:": {
        "category": "Smileys & Emotion",
        "name": "smiling face with horns",
        "unicode": "1f608"
    },
    ":smirk:": {
        "category": "Smileys & Emotion",
        "name": "smirking face",
        "unicode": "1f60f"
    },
    ":smirk_cat:": {
        "category": "Smileys & Emotion",
        "name": "cat with wry smile",
        "unicode": "1f63c"
    },
    ":smoking:": {
        "category": "Objects",
        "name": "cigarette",
        "unicode": "1f6ac"
    },
    ":snail:": {
        "category": "Animals & Nature",
        "name": "snail",
        "unicode": "1f40c"
    },
    ":snake:": {
        "category": "Animals & Nature",
        "name": "snake",
        "unicode": "1f40d"
    },
    ":sneezing_face:": {
        "category": "Smileys & Emotion",
        "name": "sneezing face",
        "unicode": "1f927"
    },
    ":snowboarder:": {
        "category": "People & Body",
        "name": "snowboarder",
        "unicode": "1f3c2"
    },
    ":snowflake:": {
        "category": "Travel & Places",
        "name": "snowflake",
        "unicode": "2744",
        "unicode_alt": "2744-fe0f"
    },
    ":snowman:": {
        "category": "Travel & Places",
        "name": "snowman without snow",
        "unicode": "26c4"
    },
    ":snowman_with_snow:": {
        "category": "Travel & Places",
        "name": "snowman",
        "unicode": "2603",
        "unicode_alt": "2603-fe0f"
    },
    ":soap:": {
        "category": "Objects",
        "name": "soap",
        "unicode": "1f9fc"
    },
    ":sob:": {
        "category": "Smileys & Emotion",
        "name": "loudly crying face",
        "unicode": "1f62d"
    },
    ":soccer:": {
        "category": "Activities",
        "name": "soccer ball",
        "unicode": "26bd"
    },
    ":socks:": {
        "category": "Objects",
        "name": "socks",
        "unicode": "1f9e6"
    },
    ":softball:": {
        "category": "Activities",
        "name": "softball",
        "unicode": "1f94e"
    },
    ":solomon_islands:": {
        "category": "Flags",
        "name": "flag: Solomon Islands",
        "unicode": "1f1f8-1f1e7"
    },
    ":somalia:": {
        "category": "Flags",
        "name": "flag: Somalia",
        "unicode": "1f1f8-1f1f4"
    },
    ":soon:": {
        "category": "Symbols",
        "name": "SOON arrow",
        "unicode": "1f51c"
    },
    ":sos:": {
        "category": "Symbols",
        "name": "SOS button",
        "unicode": "1f198"
    },
    ":sound:": {
        "category": "Objects",
        "name": "speaker medium volume",
        "unicode": "1f509"
    },
    ":south_africa:": {
        "category": "Flags",
        "name": "flag: South Africa",
        "unicode": "1f1ff-1f1e6"
    },
    ":south_georgia_south_sandwich_islands:": {
        "category": "Flags",
        "name": "flag: South Georgia & South Sandwich Islands",
        "unicode": "1f1ec-1f1f8"
    },
    ":south_sudan:": {
        "category": "Flags",
        "name": "flag: South Sudan",
        "unicode": "1f1f8-1f1f8"
    },
    ":space_invader:": {
        "category": "Smileys & Emotion",
        "name": "alien monster",
        "unicode": "1f47e"
    },
    ":spades:": {
        "category": "Activities",
        "name": "spade suit",
        "unicode": "2660",
        "unicode_alt": "2660-fe0f"
    },
    ":spaghetti:": {
        "category": "Food & Drink",
        "name": "spaghetti",
        "unicode": "1f35d"
    },
    ":sparkle:": {
        "category": "Symbols",
        "name": "sparkle",
        "unicode": "2747",
        "unicode_alt": "2747-fe0f"
    },
    ":sparkler:": {
        "category": "Activities",
        "name": "sparkler",
        "unicode": "1f387"
    },
    ":sparkles:": {
        "category": "Activities",
        "name": "sparkles",
        "unicode": "2728"
    },
    ":sparkling_heart:": {
        "category": "Smileys & Emotion",
        "name": "sparkling heart",
        "unicode": "1f496"
    },
    ":speak_no_evil:": {
        "category": "Smileys & Emotion",
        "name": "speak-no-evil monkey",
        "unicode": "1f64a"
    },
    ":speaker:": {
        "category": "Objects",
        "name": "speaker low volume",
        "unicode": "1f508"
    },
    ":speaking_head:": {
        "category": "People & Body",
        "name": "speaking head",
        "unicode": "1f5e3",
        "unicode_alt": "1f5e3-fe0f"
    },
    ":speech_balloon:": {
        "category": "Smileys & Emotion",
        "name": "speech balloon",
        "unicode": "1f4ac"
    },
    ":speedboat:": {
        "category": "Travel & Places",
        "name": "speedboat",
        "unicode": "1f6a4"
    },
    ":spider:": {
        "category": "Animals & Nature",
        "name": "spider",
        "unicode": "1f577",
        "unicode_alt": "1f577-fe0f"
    },
    ":spider_web:": {
        "category": "Animals & Nature",
        "name": "spider web",
        "unicode": "1f578",
        "unicode_alt": "1f578-fe0f"
    },
    ":spiral_calendar:": {
        "category": "Objects",
        "name": "spiral calendar",
        "unicode": "1f5d3",
        "unicode_alt": "1f5d3-fe0f"
    },
    ":spiral_notepad:": {
        "category": "Objects",
        "name": "spiral notepad",
        "unicode": "1f5d2",
        "unicode_alt": "1f5d2-fe0f"
    },
    ":sponge:": {
        "category": "Objects",
        "name": "sponge",
        "unicode": "1f9fd"
    },
    ":spoon:": {
        "category": "Food & Drink",
        "name": "spoon",
        "unicode": "1f944"
    },
    ":squid:": {
        "category": "Food & Drink",
        "name": "squid",
        "unicode": "1f991"
    },
    ":sri_lanka:": {
        "category": "Flags",
        "name": "flag: Sri Lanka",
        "unicode": "1f1f1-1f1f0"
    },
    ":st_barthelemy:": {
        "category": "Flags",
        "name": "flag: St. Barth\u00e9lemy",
        "unicode": "1f1e7-1f1f1"
    },
    ":st_helena:": {
        "category": "Flags",
        "name": "flag: St. Helena",
        "unicode": "1f1f8-1f1ed"
    },
    ":st_kitts_nevis:": {
        "category": "Flags",
        "name": "flag: St. Kitts & Nevis",
        "unicode": "1f1f0-1f1f3"
    },
    ":st_lucia:": {
        "category": "Flags",
        "name": "flag: St. Lucia",
        "unicode": "1f1f1-1f1e8"
    },
    ":st_martin:": {
        "category": "Flags",
        "name": "flag: St. Martin",
        "unicode": "1f1f2-1f1eb"
    },
    ":st_pierre_miquelon:": {
        "category": "Flags",
        "name": "flag: St. Pierre & Miquelon",
        "unicode": "1f1f5-1f1f2"
    },
    ":st_vincent_grenadines:": {
        "category": "Flags",
        "name": "flag: St. Vincent & Grenadines",
        "unicode": "1f1fb-1f1e8"
    },
    ":stadium:": {
        "category": "Travel & Places",
        "name": "stadium",
        "unicode": "1f3df",
        "unicode_alt": "1f3df-fe0f"
    },
    ":standing_man:": {
        "category": "People & Body",
        "name": "man standing",
        "unicode": "1f9cd-2642",
        "unicode_alt": "1f9cd-200d-2642-fe0f"
    },
    ":standing_person:": {
        "category": "People & Body",
        "name": "person standing",
        "unicode": "1f9cd"
    },
    ":standing_woman:": {
        "category": "People & Body",
        "name": "woman standing",
        "unicode": "1f9cd-2640",
        "unicode_alt": "1f9cd-200d-2640-fe0f"
    },
    ":star2:": {
        "category": "Travel & Places",
        "name": "glowing star",
        "unicode": "1f31f"
    },
    ":star:": {
        "category": "Travel & Places",
        "name": "star",
        "unicode": "2b50"
    },
    ":star_and_crescent:": {
        "category": "Symbols",
        "name": "star and crescent",
        "unicode": "262a",
        "unicode_alt": "262a-fe0f"
    },
    ":star_of_david:": {
        "category": "Symbols",
        "name": "star of David",
        "unicode": "2721",
        "unicode_alt": "2721-fe0f"
    },
    ":star_struck:": {
        "category": "Smileys & Emotion",
        "name": "star-struck",
        "unicode": "1f929"
    },
    ":stars:": {
        "category": "Travel & Places",
        "name": "shooting star",
        "unicode": "1f320"
    },
    ":station:": {
        "category": "Travel & Places",
        "name": "station",
        "unicode": "1f689"
    },
    ":statue_of_liberty:": {
        "category": "Travel & Places",
        "name": "Statue of Liberty",
        "unicode": "1f5fd"
    },
    ":steam_locomotive:": {
        "category": "Travel & Places",
        "name": "locomotive",
        "unicode": "1f682"
    },
    ":stethoscope:": {
        "category": "Objects",
        "name": "stethoscope",
        "unicode": "1fa7a"
    },
    ":stew:": {
        "category": "Food & Drink",
        "name": "pot of food",
        "unicode": "1f372"
    },
    ":stop_button:": {
        "category": "Symbols",
        "name": "stop button",
        "unicode": "23f9",
        "unicode_alt": "23f9-fe0f"
    },
    ":stop_sign:": {
        "category": "Travel & Places",
        "name": "stop sign",
        "unicode": "1f6d1"
    },
    ":stopwatch:": {
        "category": "Travel & Places",
        "name": "stopwatch",
        "unicode": "23f1",
        "unicode_alt": "23f1-fe0f"
    },
    ":straight_ruler:": {
        "category": "Objects",
        "name": "straight ruler",
        "unicode": "1f4cf"
    },
    ":strawberry:": {
        "category": "Food & Drink",
        "name": "strawberry",
        "unicode": "1f353"
    },
    ":stuck_out_tongue:": {
        "category": "Smileys & Emotion",
        "name": "face with tongue",
        "unicode": "1f61b"
    },
    ":stuck_out_tongue_closed_eyes:": {
        "category": "Smileys & Emotion",
        "name": "squinting face with tongue",
        "unicode": "1f61d"
    },
    ":stuck_out_tongue_winking_eye:": {
        "category": "Smileys & Emotion",
        "name": "winking face with tongue",
        "unicode": "1f61c"
    },
    ":student:": {
        "category": "People & Body",
        "name": "student",
        "unicode": "1f9d1-1f393",
        "unicode_alt": "1f9d1-200d-1f393"
    },
    ":studio_microphone:": {
        "category": "Objects",
        "name": "studio microphone",
        "unicode": "1f399",
        "unicode_alt": "1f399-fe0f"
    },
    ":stuffed_flatbread:": {
        "category": "Food & Drink",
        "name": "stuffed flatbread",
        "unicode": "1f959"
    },
    ":sudan:": {
        "category": "Flags",
        "name": "flag: Sudan",
        "unicode": "1f1f8-1f1e9"
    },
    ":sun_behind_large_cloud:": {
        "category": "Travel & Places",
        "name": "sun behind large cloud",
        "unicode": "1f325",
        "unicode_alt": "1f325-fe0f"
    },
    ":sun_behind_rain_cloud:": {
        "category": "Travel & Places",
        "name": "sun behind rain cloud",
        "unicode": "1f326",
        "unicode_alt": "1f326-fe0f"
    },
    ":sun_behind_small_cloud:": {
        "category": "Travel & Places",
        "name": "sun behind small cloud",
        "unicode": "1f324",
        "unicode_alt": "1f324-fe0f"
    },
    ":sun_with_face:": {
        "category": "Travel & Places",
        "name": "sun with face",
        "unicode": "1f31e"
    },
    ":sunflower:": {
        "category": "Animals & Nature",
        "name": "sunflower",
        "unicode": "1f33b"
    },
    ":sunglasses:": {
        "category": "Smileys & Emotion",
        "name": "smiling face with sunglasses",
        "unicode": "1f60e"
    },
    ":sunny:": {
        "category": "Travel & Places",
        "name": "sun",
        "unicode": "2600",
        "unicode_alt": "2600-fe0f"
    },
    ":sunrise:": {
        "category": "Travel & Places",
        "name": "sunrise",
        "unicode": "1f305"
    },
    ":sunrise_over_mountains:": {
        "category": "Travel & Places",
        "name": "sunrise over mountains",
        "unicode": "1f304"
    },
    ":superhero:": {
        "category": "People & Body",
        "name": "superhero",
        "unicode": "1f9b8"
    },
    ":superhero_man:": {
        "category": "People & Body",
        "name": "man superhero",
        "unicode": "1f9b8-2642",
        "unicode_alt": "1f9b8-200d-2642-fe0f"
    },
    ":superhero_woman:": {
        "category": "People & Body",
        "name": "woman superhero",
        "unicode": "1f9b8-2640",
        "unicode_alt": "1f9b8-200d-2640-fe0f"
    },
    ":supervillain:": {
        "category": "People & Body",
        "name": "supervillain",
        "unicode": "1f9b9"
    },
    ":supervillain_man:": {
        "category": "People & Body",
        "name": "man supervillain",
        "unicode": "1f9b9-2642",
        "unicode_alt": "1f9b9-200d-2642-fe0f"
    },
    ":supervillain_woman:": {
        "category": "People & Body",
        "name": "woman supervillain",
        "unicode": "1f9b9-2640",
        "unicode_alt": "1f9b9-200d-2640-fe0f"
    },
    ":surfer:": {
        "category": "People & Body",
        "name": "person surfing",
        "unicode": "1f3c4"
    },
    ":surfing_man:": {
        "category": "People & Body",
        "name": "man surfing",
        "unicode": "1f3c4-2642",
        "unicode_alt": "1f3c4-200d-2642-fe0f"
    },
    ":surfing_woman:": {
        "category": "People & Body",
        "name": "woman surfing",
        "unicode": "1f3c4-2640",
        "unicode_alt": "1f3c4-200d-2640-fe0f"
    },
    ":suriname:": {
        "category": "Flags",
        "name": "flag: Suriname",
        "unicode": "1f1f8-1f1f7"
    },
    ":sushi:": {
        "category": "Food & Drink",
        "name": "sushi",
        "unicode": "1f363"
    },
    ":suspension_railway:": {
        "category": "Travel & Places",
        "name": "suspension railway",
        "unicode": "1f69f"
    },
    ":svalbard_jan_mayen:": {
        "category": "Flags",
        "name": "flag: Svalbard & Jan Mayen",
        "unicode": "1f1f8-1f1ef"
    },
    ":swan:": {
        "category": "Animals & Nature",
        "name": "swan",
        "unicode": "1f9a2"
    },
    ":swaziland:": {
        "category": "Flags",
        "name": "flag: Eswatini",
        "unicode": "1f1f8-1f1ff"
    },
    ":sweat:": {
        "category": "Smileys & Emotion",
        "name": "downcast face with sweat",
        "unicode": "1f613"
    },
    ":sweat_drops:": {
        "category": "Smileys & Emotion",
        "name": "sweat droplets",
        "unicode": "1f4a6"
    },
    ":sweat_smile:": {
        "category": "Smileys & Emotion",
        "name": "grinning face with sweat",
        "unicode": "1f605"
    },
    ":sweden:": {
        "category": "Flags",
        "name": "flag: Sweden",
        "unicode": "1f1f8-1f1ea"
    },
    ":sweet_potato:": {
        "category": "Food & Drink",
        "name": "roasted sweet potato",
        "unicode": "1f360"
    },
    ":swim_brief:": {
        "category": "Objects",
        "name": "briefs",
        "unicode": "1fa72"
    },
    ":swimmer:": {
        "category": "People & Body",
        "name": "person swimming",
        "unicode": "1f3ca"
    },
    ":swimming_man:": {
        "category": "People & Body",
        "name": "man swimming",
        "unicode": "1f3ca-2642",
        "unicode_alt": "1f3ca-200d-2642-fe0f"
    },
    ":swimming_woman:": {
        "category": "People & Body",
        "name": "woman swimming",
        "unicode": "1f3ca-2640",
        "unicode_alt": "1f3ca-200d-2640-fe0f"
    },
    ":switzerland:": {
        "category": "Flags",
        "name": "flag: Switzerland",
        "unicode": "1f1e8-1f1ed"
    },
    ":symbols:": {
        "category": "Symbols",
        "name": "input symbols",
        "unicode": "1f523"
    },
    ":synagogue:": {
        "category": "Travel & Places",
        "name": "synagogue",
        "unicode": "1f54d"
    },
    ":syria:": {
        "category": "Flags",
        "name": "flag: Syria",
        "unicode": "1f1f8-1f1fe"
    },
    ":syringe:": {
        "category": "Objects",
        "name": "syringe",
        "unicode": "1f489"
    },
    ":t-rex:": {
        "category": "Animals & Nature",
        "name": "T-Rex",
        "unicode": "1f996"
    },
    ":taco:": {
        "category": "Food & Drink",
        "name": "taco",
        "unicode": "1f32e"
    },
    ":tada:": {
        "category": "Activities",
        "name": "party popper",
        "unicode": "1f389"
    },
    ":taiwan:": {
        "category": "Flags",
        "name": "flag: Taiwan",
        "unicode": "1f1f9-1f1fc"
    },
    ":tajikistan:": {
        "category": "Flags",
        "name": "flag: Tajikistan",
        "unicode": "1f1f9-1f1ef"
    },
    ":takeout_box:": {
        "category": "Food & Drink",
        "name": "takeout box",
        "unicode": "1f961"
    },
    ":tamale:": {
        "category": "Food & Drink",
        "name": "tamale",
        "unicode": "1fad4"
    },
    ":tanabata_tree:": {
        "category": "Activities",
        "name": "tanabata tree",
        "unicode": "1f38b"
    },
    ":tangerine:": {
        "category": "Food & Drink",
        "name": "tangerine",
        "unicode": "1f34a"
    },
    ":tanzania:": {
        "category": "Flags",
        "name": "flag: Tanzania",
        "unicode": "1f1f9-1f1ff"
    },
    ":taurus:": {
        "category": "Symbols",
        "name": "Taurus",
        "unicode": "2649"
    },
    ":taxi:": {
        "category": "Travel & Places",
        "name": "taxi",
        "unicode": "1f695"
    },
    ":tea:": {
        "category": "Food & Drink",
        "name": "teacup without handle",
        "unicode": "1f375"
    },
    ":teacher:": {
        "category": "People & Body",
        "name": "teacher",
        "unicode": "1f9d1-1f3eb",
        "unicode_alt": "1f9d1-200d-1f3eb"
    },
    ":teapot:": {
        "category": "Food & Drink",
        "name": "teapot",
        "unicode": "1fad6"
    },
    ":technologist:": {
        "category": "People & Body",
        "name": "technologist",
        "unicode": "1f9d1-1f4bb",
        "unicode_alt": "1f9d1-200d-1f4bb"
    },
    ":teddy_bear:": {
        "category": "Activities",
        "name": "teddy bear",
        "unicode": "1f9f8"
    },
    ":telephone_receiver:": {
        "category": "Objects",
        "name": "telephone receiver",
        "unicode": "1f4de"
    },
    ":telescope:": {
        "category": "Objects",
        "name": "telescope",
        "unicode": "1f52d"
    },
    ":tennis:": {
        "category": "Activities",
        "name": "tennis",
        "unicode": "1f3be"
    },
    ":tent:": {
        "category": "Travel & Places",
        "name": "tent",
        "unicode": "26fa"
    },
    ":test_tube:": {
        "category": "Objects",
        "name": "test tube",
        "unicode": "1f9ea"
    },
    ":thailand:": {
        "category": "Flags",
        "name": "flag: Thailand",
        "unicode": "1f1f9-1f1ed"
    },
    ":thermometer:": {
        "category": "Travel & Places",
        "name": "thermometer",
        "unicode": "1f321",
        "unicode_alt": "1f321-fe0f"
    },
    ":thinking:": {
        "category": "Smileys & Emotion",
        "name": "thinking face",
        "unicode": "1f914"
    },
    ":thong_sandal:": {
        "category": "Objects",
        "name": "thong sandal",
        "unicode": "1fa74"
    },
    ":thought_balloon:": {
        "category": "Smileys & Emotion",
        "name": "thought balloon",
        "unicode": "1f4ad"
    },
    ":thread:": {
        "category": "Activities",
        "name": "thread",
        "unicode": "1f9f5"
    },
    ":three:": {
        "category": "Symbols",
        "name": "keycap: 3",
        "unicode": "0033-20e3",
        "unicode_alt": "0033-fe0f-20e3"
    },
    ":ticket:": {
        "category": "Activities",
        "name": "ticket",
        "unicode": "1f3ab"
    },
    ":tickets:": {
        "category": "Activities",
        "name": "admission tickets",
        "unicode": "1f39f",
        "unicode_alt": "1f39f-fe0f"
    },
    ":tiger2:": {
        "category": "Animals & Nature",
        "name": "tiger",
        "unicode": "1f405"
    },
    ":tiger:": {
        "category": "Animals & Nature",
        "name": "tiger face",
        "unicode": "1f42f"
    },
    ":timer_clock:": {
        "category": "Travel & Places",
        "name": "timer clock",
        "unicode": "23f2",
        "unicode_alt": "23f2-fe0f"
    },
    ":timor_leste:": {
        "category": "Flags",
        "name": "flag: Timor-Leste",
        "unicode": "1f1f9-1f1f1"
    },
    ":tipping_hand_man:": {
        "category": "People & Body",
        "name": "man tipping hand",
        "unicode": "1f481-2642",
        "unicode_alt": "1f481-200d-2642-fe0f"
    },
    ":tipping_hand_person:": {
        "category": "People & Body",
        "name": "person tipping hand",
        "unicode": "1f481"
    },
    ":tipping_hand_woman:": {
        "category": "People & Body",
        "name": "woman tipping hand",
        "unicode": "1f481-2640",
        "unicode_alt": "1f481-200d-2640-fe0f"
    },
    ":tired_face:": {
        "category": "Smileys & Emotion",
        "name": "tired face",
        "unicode": "1f62b"
    },
    ":tm:": {
        "category": "Symbols",
        "name": "trade mark",
        "unicode": "2122",
        "unicode_alt": "2122-fe0f"
    },
    ":togo:": {
        "category": "Flags",
        "name": "flag: Togo",
        "unicode": "1f1f9-1f1ec"
    },
    ":toilet:": {
        "category": "Objects",
        "name": "toilet",
        "unicode": "1f6bd"
    },
    ":tokelau:": {
        "category": "Flags",
        "name": "flag: Tokelau",
        "unicode": "1f1f9-1f1f0"
    },
    ":tokyo_tower:": {
        "category": "Travel & Places",
        "name": "Tokyo tower",
        "unicode": "1f5fc"
    },
    ":tomato:": {
        "category": "Food & Drink",
        "name": "tomato",
        "unicode": "1f345"
    },
    ":tonga:": {
        "category": "Flags",
        "name": "flag: Tonga",
        "unicode": "1f1f9-1f1f4"
    },
    ":tongue:": {
        "category": "People & Body",
        "name": "tongue",
        "unicode": "1f445"
    },
    ":toolbox:": {
        "category": "Objects",
        "name": "toolbox",
        "unicode": "1f9f0"
    },
    ":tooth:": {
        "category": "People & Body",
        "name": "tooth",
        "unicode": "1f9b7"
    },
    ":toothbrush:": {
        "category": "Objects",
        "name": "toothbrush",
        "unicode": "1faa5"
    },
    ":top:": {
        "category": "Symbols",
        "name": "TOP arrow",
        "unicode": "1f51d"
    },
    ":tophat:": {
        "category": "Objects",
        "name": "top hat",
        "unicode": "1f3a9"
    },
    ":tornado:": {
        "category": "Travel & Places",
        "name": "tornado",
        "unicode": "1f32a",
        "unicode_alt": "1f32a-fe0f"
    },
    ":tr:": {
        "category": "Flags",
        "name": "flag: Turkey",
        "unicode": "1f1f9-1f1f7"
    },
    ":trackball:": {
        "category": "Objects",
        "name": "trackball",
        "unicode": "1f5b2",
        "unicode_alt": "1f5b2-fe0f"
    },
    ":tractor:": {
        "category": "Travel & Places",
        "name": "tractor",
        "unicode": "1f69c"
    },
    ":traffic_light:": {
        "category": "Travel & Places",
        "name": "horizontal traffic light",
        "unicode": "1f6a5"
    },
    ":train2:": {
        "category": "Travel & Places",
        "name": "train",
        "unicode": "1f686"
    },
    ":train:": {
        "category": "Travel & Places",
        "name": "tram car",
        "unicode": "1f68b"
    },
    ":tram:": {
        "category": "Travel & Places",
        "name": "tram",
        "unicode": "1f68a"
    },
    ":transgender_flag:": {
        "category": "Flags",
        "name": "transgender flag",
        "unicode": "1f3f3-26a7",
        "unicode_alt": "1f3f3-fe0f-200d-26a7-fe0f"
    },
    ":transgender_symbol:": {
        "category": "Symbols",
        "name": "transgender symbol",
        "unicode": "26a7",
        "unicode_alt": "26a7-fe0f"
    },
    ":triangular_flag_on_post:": {
        "category": "Flags",
        "name": "triangular flag",
        "unicode": "1f6a9"
    },
    ":triangular_ruler:": {
        "category": "Objects",
        "name": "triangular ruler",
        "unicode": "1f4d0"
    },
    ":trident:": {
        "category": "Symbols",
        "name": "trident emblem",
        "unicode": "1f531"
    },
    ":trinidad_tobago:": {
        "category": "Flags",
        "name": "flag: Trinidad & Tobago",
        "unicode": "1f1f9-1f1f9"
    },
    ":tristan_da_cunha:": {
        "category": "Flags",
        "name": "flag: Tristan da Cunha",
        "unicode": "1f1f9-1f1e6"
    },
    ":triumph:": {
        "category": "Smileys & Emotion",
        "name": "face with steam from nose",
        "unicode": "1f624"
    },
    ":troll:": {
        "category": "People & Body",
        "name": "troll",
        "unicode": "1f9cc"
    },
    ":trolleybus:": {
        "category": "Travel & Places",
        "name": "trolleybus",
        "unicode": "1f68e"
    },
    ":trophy:": {
        "category": "Activities",
        "name": "trophy",
        "unicode": "1f3c6"
    },
    ":tropical_drink:": {
        "category": "Food & Drink",
        "name": "tropical drink",
        "unicode": "1f379"
    },
    ":tropical_fish:": {
        "category": "Animals & Nature",
        "name": "tropical fish",
        "unicode": "1f420"
    },
    ":truck:": {
        "category": "Travel & Places",
        "name": "delivery truck",
        "unicode": "1f69a"
    },
    ":trumpet:": {
        "category": "Objects",
        "name": "trumpet",
        "unicode": "1f3ba"
    },
    ":tulip:": {
        "category": "Animals & Nature",
        "name": "tulip",
        "unicode": "1f337"
    },
    ":tumbler_glass:": {
        "category": "Food & Drink",
        "name": "tumbler glass",
        "unicode": "1f943"
    },
    ":tunisia:": {
        "category": "Flags",
        "name": "flag: Tunisia",
        "unicode": "1f1f9-1f1f3"
    },
    ":turkey:": {
        "category": "Animals & Nature",
        "name": "turkey",
        "unicode": "1f983"
    },
    ":turkmenistan:": {
        "category": "Flags",
        "name": "flag: Turkmenistan",
        "unicode": "1f1f9-1f1f2"
    },
    ":turks_caicos_islands:": {
        "category": "Flags",
        "name": "flag: Turks & Caicos Islands",
        "unicode": "1f1f9-1f1e8"
    },
    ":turtle:": {
        "category": "Animals & Nature",
        "name": "turtle",
        "unicode": "1f422"
    },
    ":tuvalu:": {
        "category": "Flags",
        "name": "flag: Tuvalu",
        "unicode": "1f1f9-1f1fb"
    },
    ":tv:": {
        "category": "Objects",
        "name": "television",
        "unicode": "1f4fa"
    },
    ":twisted_rightwards_arrows:": {
        "category": "Symbols",
        "name": "shuffle tracks button",
        "unicode": "1f500"
    },
    ":two:": {
        "category": "Symbols",
        "name": "keycap: 2",
        "unicode": "0032-20e3",
        "unicode_alt": "0032-fe0f-20e3"
    },
    ":two_hearts:": {
        "category": "Smileys & Emotion",
        "name": "two hearts",
        "unicode": "1f495"
    },
    ":two_men_holding_hands:": {
        "category": "People & Body",
        "name": "men holding hands",
        "unicode": "1f46c"
    },
    ":two_women_holding_hands:": {
        "category": "People & Body",
        "name": "women holding hands",
        "unicode": "1f46d"
    },
    ":u5272:": {
        "category": "Symbols",
        "name": "Japanese \u201cdiscount\u201d button",
        "unicode": "1f239"
    },
    ":u5408:": {
        "category": "Symbols",
        "name": "Japanese \u201cpassing grade\u201d button",
        "unicode": "1f234"
    },
    ":u55b6:": {
        "category": "Symbols",
        "name": "Japanese \u201copen for business\u201d button",
        "unicode": "1f23a"
    },
    ":u6307:": {
        "category": "Symbols",
        "name": "Japanese \u201creserved\u201d button",
        "unicode": "1f22f"
    },
    ":u6708:": {
        "category": "Symbols",
        "name": "Japanese \u201cmonthly amount\u201d button",
        "unicode": "1f237",
        "unicode_alt": "1f237-fe0f"
    },
    ":u6709:": {
        "category": "Symbols",
        "name": "Japanese \u201cnot free of charge\u201d button",
        "unicode": "1f236"
    },
    ":u6e80:": {
        "category": "Symbols",
        "name": "Japanese \u201cno vacancy\u201d button",
        "unicode": "1f235"
    },
    ":u7121:": {
        "category": "Symbols",
        "name": "Japanese \u201cfree of charge\u201d button",
        "unicode": "1f21a"
    },
    ":u7533:": {
        "category": "Symbols",
        "name": "Japanese \u201capplication\u201d button",
        "unicode": "1f238"
    },
    ":u7981:": {
        "category": "Symbols",
        "name": "Japanese \u201cprohibited\u201d button",
        "unicode": "1f232"
    },
    ":u7a7a:": {
        "category": "Symbols",
        "name": "Japanese \u201cvacancy\u201d button",
        "unicode": "1f233"
    },
    ":uganda:": {
        "category": "Flags",
        "name": "flag: Uganda",
        "unicode": "1f1fa-1f1ec"
    },
    ":ukraine:": {
        "category": "Flags",
        "name": "flag: Ukraine",
        "unicode": "1f1fa-1f1e6"
    },
    ":umbrella:": {
        "category": "Travel & Places",
        "name": "umbrella with rain drops",
        "unicode": "2614"
    },
    ":unamused:": {
        "category": "Smileys & Emotion",
        "name": "unamused face",
        "unicode": "1f612"
    },
    ":underage:": {
        "category": "Symbols",
        "name": "no one under eighteen",
        "unicode": "1f51e"
    },
    ":unicorn:": {
        "category": "Animals & Nature",
        "name": "unicorn",
        "unicode": "1f984"
    },
    ":united_arab_emirates:": {
        "category": "Flags",
        "name": "flag: United Arab Emirates",
        "unicode": "1f1e6-1f1ea"
    },
    ":united_nations:": {
        "category": "Flags",
        "name": "flag: United Nations",
        "unicode": "1f1fa-1f1f3"
    },
    ":unlock:": {
        "category": "Objects",
        "name": "unlocked",
        "unicode": "1f513"
    },
    ":up:": {
        "category": "Symbols",
        "name": "UP! button",
        "unicode": "1f199"
    },
    ":upside_down_face:": {
        "category": "Smileys & Emotion",
        "name": "upside-down face",
        "unicode": "1f643"
    },
    ":uruguay:": {
        "category": "Flags",
        "name": "flag: Uruguay",
        "unicode": "1f1fa-1f1fe"
    },
    ":us:": {
        "category": "Flags",
        "name": "flag: United States",
        "unicode": "1f1fa-1f1f8"
    },
    ":us_outlying_islands:": {
        "category": "Flags",
        "name": "flag: U.S. Outlying Islands",
        "unicode": "1f1fa-1f1f2"
    },
    ":us_virgin_islands:": {
        "category": "Flags",
        "name": "flag: U.S. Virgin Islands",
        "unicode": "1f1fb-1f1ee"
    },
    ":uzbekistan:": {
        "category": "Flags",
        "name": "flag: Uzbekistan",
        "unicode": "1f1fa-1f1ff"
    },
    ":v:": {
        "category": "People & Body",
        "name": "victory hand",
        "unicode": "270c",
        "unicode_alt": "270c-fe0f"
    },
    ":vampire:": {
        "category": "People & Body",
        "name": "vampire",
        "unicode": "1f9db"
    },
    ":vampire_man:": {
        "category": "People & Body",
        "name": "man vampire",
        "unicode": "1f9db-2642",
        "unicode_alt": "1f9db-200d-2642-fe0f"
    },
    ":vampire_woman:": {
        "category": "People & Body",
        "name": "woman vampire",
        "unicode": "1f9db-2640",
        "unicode_alt": "1f9db-200d-2640-fe0f"
    },
    ":vanuatu:": {
        "category": "Flags",
        "name": "flag: Vanuatu",
        "unicode": "1f1fb-1f1fa"
    },
    ":vatican_city:": {
        "category": "Flags",
        "name": "flag: Vatican City",
        "unicode": "1f1fb-1f1e6"
    },
    ":venezuela:": {
        "category": "Flags",
        "name": "flag: Venezuela",
        "unicode": "1f1fb-1f1ea"
    },
    ":vertical_traffic_light:": {
        "category": "Travel & Places",
        "name": "vertical traffic light",
        "unicode": "1f6a6"
    },
    ":vhs:": {
        "category": "Objects",
        "name": "videocassette",
        "unicode": "1f4fc"
    },
    ":vibration_mode:": {
        "category": "Symbols",
        "name": "vibration mode",
        "unicode": "1f4f3"
    },
    ":video_camera:": {
        "category": "Objects",
        "name": "video camera",
        "unicode": "1f4f9"
    },
    ":video_game:": {
        "category": "Activities",
        "name": "video game",
        "unicode": "1f3ae"
    },
    ":vietnam:": {
        "category": "Flags",
        "name": "flag: Vietnam",
        "unicode": "1f1fb-1f1f3"
    },
    ":violin:": {
        "category": "Objects",
        "name": "violin",
        "unicode": "1f3bb"
    },
    ":virgo:": {
        "category": "Symbols",
        "name": "Virgo",
        "unicode": "264d"
    },
    ":volcano:": {
        "category": "Travel & Places",
        "name": "volcano",
        "unicode": "1f30b"
    },
    ":volleyball:": {
        "category": "Activities",
        "name": "volleyball",
        "unicode": "1f3d0"
    },
    ":vomiting_face:": {
        "category": "Smileys & Emotion",
        "name": "face vomiting",
        "unicode": "1f92e"
    },
    ":vs:": {
        "category": "Symbols",
        "name": "VS button",
        "unicode": "1f19a"
    },
    ":vulcan_salute:": {
        "category": "People & Body",
        "name": "vulcan salute",
        "unicode": "1f596"
    },
    ":waffle:": {
        "category": "Food & Drink",
        "name": "waffle",
        "unicode": "1f9c7"
    },
    ":wales:": {
        "category": "Flags",
        "name": "flag: Wales",
        "unicode": "1f3f4-e0067-e0062-e0077-e006c-e0073-e007f"
    },
    ":walking:": {
        "category": "People & Body",
        "name": "person walking",
        "unicode": "1f6b6"
    },
    ":walking_man:": {
        "category": "People & Body",
        "name": "man walking",
        "unicode": "1f6b6-2642",
        "unicode_alt": "1f6b6-200d-2642-fe0f"
    },
    ":walking_woman:": {
        "category": "People & Body",
        "name": "woman walking",
        "unicode": "1f6b6-2640",
        "unicode_alt": "1f6b6-200d-2640-fe0f"
    },
    ":wallis_futuna:": {
        "category": "Flags",
        "name": "flag: Wallis & Futuna",
        "unicode": "1f1fc-1f1eb"
    },
    ":waning_crescent_moon:": {
        "category": "Travel & Places",
        "name": "waning crescent moon",
        "unicode": "1f318"
    },
    ":waning_gibbous_moon:": {
        "category": "Travel & Places",
        "name": "waning gibbous moon",
        "unicode": "1f316"
    },
    ":warning:": {
        "category": "Symbols",
        "name": "warning",
        "unicode": "26a0",
        "unicode_alt": "26a0-fe0f"
    },
    ":wastebasket:": {
        "category": "Objects",
        "name": "wastebasket",
        "unicode": "1f5d1",
        "unicode_alt": "1f5d1-fe0f"
    },
    ":watch:": {
        "category": "Travel & Places",
        "name": "watch",
        "unicode": "231a"
    },
    ":water_buffalo:": {
        "category": "Animals & Nature",
        "name": "water buffalo",
        "unicode": "1f403"
    },
    ":water_polo:": {
        "category": "People & Body",
        "name": "person playing water polo",
        "unicode": "1f93d"
    },
    ":watermelon:": {
        "category": "Food & Drink",
        "name": "watermelon",
        "unicode": "1f349"
    },
    ":wave:": {
        "category": "People & Body",
        "name": "waving hand",
        "unicode": "1f44b"
    },
    ":wavy_dash:": {
        "category": "Symbols",
        "name": "wavy dash",
        "unicode": "3030",
        "unicode_alt": "3030-fe0f"
    },
    ":waxing_crescent_moon:": {
        "category": "Travel & Places",
        "name": "waxing crescent moon",
        "unicode": "1f312"
    },
    ":wc:": {
        "category": "Symbols",
        "name": "water closet",
        "unicode": "1f6be"
    },
    ":weary:": {
        "category": "Smileys & Emotion",
        "name": "weary face",
        "unicode": "1f629"
    },
    ":wedding:": {
        "category": "Travel & Places",
        "name": "wedding",
        "unicode": "1f492"
    },
    ":weight_lifting:": {
        "category": "People & Body",
        "name": "person lifting weights",
        "unicode": "1f3cb",
        "unicode_alt": "1f3cb-fe0f"
    },
    ":weight_lifting_man:": {
        "category": "People & Body",
        "name": "man lifting weights",
        "unicode": "1f3cb-2642",
        "unicode_alt": "1f3cb-fe0f-200d-2642-fe0f"
    },
    ":weight_lifting_woman:": {
        "category": "People & Body",
        "name": "woman lifting weights",
        "unicode": "1f3cb-2640",
        "unicode_alt": "1f3cb-fe0f-200d-2640-fe0f"
    },
    ":western_sahara:": {
        "category": "Flags",
        "name": "flag: Western Sahara",
        "unicode": "1f1ea-1f1ed"
    },
    ":whale2:": {
        "category": "Animals & Nature",
        "name": "whale",
        "unicode": "1f40b"
    },
    ":whale:": {
        "category": "Animals & Nature",
        "name": "spouting whale",
        "unicode": "1f433"
    },
    ":wheel:": {
        "category": "Travel & Places",
        "name": "wheel",
        "unicode": "1f6de"
    },
    ":wheel_of_dharma:": {
        "category": "Symbols",
        "name": "wheel of dharma",
        "unicode": "2638",
        "unicode_alt": "2638-fe0f"
    },
    ":wheelchair:": {
        "category": "Symbols",
        "name": "wheelchair symbol",
        "unicode": "267f"
    },
    ":white_check_mark:": {
        "category": "Symbols",
        "name": "check mark button",
        "unicode": "2705"
    },
    ":white_circle:": {
        "category": "Symbols",
        "name": "white circle",
        "unicode": "26aa"
    },
    ":white_flag:": {
        "category": "Flags",
        "name": "white flag",
        "unicode": "1f3f3",
        "unicode_alt": "1f3f3-fe0f"
    },
    ":white_flower:": {
        "category": "Animals & Nature",
        "name": "white flower",
        "unicode": "1f4ae"
    },
    ":white_haired_man:": {
        "category": "People & Body",
        "name": "man: white hair",
        "unicode": "1f468-1f9b3",
        "unicode_alt": "1f468-200d-1f9b3"
    },
    ":white_haired_woman:": {
        "category": "People & Body",
        "name": "woman: white hair",
        "unicode": "1f469-1f9b3",
        "unicode_alt": "1f469-200d-1f9b3"
    },
    ":white_heart:": {
        "category": "Smileys & Emotion",
        "name": "white heart",
        "unicode": "1f90d"
    },
    ":white_large_square:": {
        "category": "Symbols",
        "name": "white large square",
        "unicode": "2b1c"
    },
    ":white_medium_small_square:": {
        "category": "Symbols",
        "name": "white medium-small square",
        "unicode": "25fd"
    },
    ":white_medium_square:": {
        "category": "Symbols",
        "name": "white medium square",
        "unicode": "25fb",
        "unicode_alt": "25fb-fe0f"
    },
    ":white_small_square:": {
        "category": "Symbols",
        "name": "white small square",
        "unicode": "25ab",
        "unicode_alt": "25ab-fe0f"
    },
    ":white_square_button:": {
        "category": "Symbols",
        "name": "white square button",
        "unicode": "1f533"
    },
    ":wilted_flower:": {
        "category": "Animals & Nature",
        "name": "wilted flower",
        "unicode": "1f940"
    },
    ":wind_chime:": {
        "category": "Activities",
        "name": "wind chime",
        "unicode": "1f390"
    },
    ":wind_face:": {
        "category": "Travel & Places",
        "name": "wind face",
        "unicode": "1f32c",
        "unicode_alt": "1f32c-fe0f"
    },
    ":window:": {
        "category": "Objects",
        "name": "window",
        "unicode": "1fa9f"
    },
    ":wine_glass:": {
        "category": "Food & Drink",
        "name": "wine glass",
        "unicode": "1f377"
    },
    ":wing:": {
        "category": "Animals & Nature",
        "name": "wing",
        "unicode": "1fabd"
    },
    ":wink:": {
        "category": "Smileys & Emotion",
        "name": "winking face",
        "unicode": "1f609"
    },
    ":wireless:": {
        "category": "Symbols",
        "name": "wireless",
        "unicode": "1f6dc"
    },
    ":wolf:": {
        "category": "Animals & Nature",
        "name": "wolf",
        "unicode": "1f43a"
    },
    ":woman:": {
        "category": "People & Body",
        "name": "woman",
        "unicode": "1f469"
    },
    ":woman_artist:": {
        "category": "People & Body",
        "name": "woman artist",
        "unicode": "1f469-1f3a8",
        "unicode_alt": "1f469-200d-1f3a8"
    },
    ":woman_astronaut:": {
        "category": "People & Body",
        "name": "woman astronaut",
        "unicode": "1f469-1f680",
        "unicode_alt": "1f469-200d-1f680"
    },
    ":woman_beard:": {
        "category": "People & Body",
        "name": "woman: beard",
        "unicode": "1f9d4-2640",
        "unicode_alt": "1f9d4-200d-2640-fe0f"
    },
    ":woman_cartwheeling:": {
        "category": "People & Body",
        "name": "woman cartwheeling",
        "unicode": "1f938-2640",
        "unicode_alt": "1f938-200d-2640-fe0f"
    },
    ":woman_cook:": {
        "category": "People & Body",
        "name": "woman cook",
        "unicode": "1f469-1f373",
        "unicode_alt": "1f469-200d-1f373"
    },
    ":woman_dancing:": {
        "category": "People & Body",
        "name": "woman dancing",
        "unicode": "1f483"
    },
    ":woman_facepalming:": {
        "category": "People & Body",
        "name": "woman facepalming",
        "unicode": "1f926-2640",
        "unicode_alt": "1f926-200d-2640-fe0f"
    },
    ":woman_factory_worker:": {
        "category": "People & Body",
        "name": "woman factory worker",
        "unicode": "1f469-1f3ed",
        "unicode_alt": "1f469-200d-1f3ed"
    },
    ":woman_farmer:": {
        "category": "People & Body",
        "name": "woman farmer",
        "unicode": "1f469-1f33e",
        "unicode_alt": "1f469-200d-1f33e"
    },
    ":woman_feeding_baby:": {
        "category": "People & Body",
        "name": "woman feeding baby",
        "unicode": "1f469-1f37c",
        "unicode_alt": "1f469-200d-1f37c"
    },
    ":woman_firefighter:": {
        "category": "People & Body",
        "name": "woman firefighter",
        "unicode": "1f469-1f692",
        "unicode_alt": "1f469-200d-1f692"
    },
    ":woman_health_worker:": {
        "category": "People & Body",
        "name": "woman health worker",
        "unicode": "1f469-2695",
        "unicode_alt": "1f469-200d-2695-fe0f"
    },
    ":woman_in_manual_wheelchair:": {
        "category": "People & Body",
        "name": "woman in manual wheelchair",
        "unicode": "1f469-1f9bd",
        "unicode_alt": "1f469-200d-1f9bd"
    },
    ":woman_in_motorized_wheelchair:": {
        "category": "People & Body",
        "name": "woman in motorized wheelchair",
        "unicode": "1f469-1f9bc",
        "unicode_alt": "1f469-200d-1f9bc"
    },
    ":woman_in_tuxedo:": {
        "category": "People & Body",
        "name": "woman in tuxedo",
        "unicode": "1f935-2640",
        "unicode_alt": "1f935-200d-2640-fe0f"
    },
    ":woman_judge:": {
        "category": "People & Body",
        "name": "woman judge",
        "unicode": "1f469-2696",
        "unicode_alt": "1f469-200d-2696-fe0f"
    },
    ":woman_juggling:": {
        "category": "People & Body",
        "name": "woman juggling",
        "unicode": "1f939-2640",
        "unicode_alt": "1f939-200d-2640-fe0f"
    },
    ":woman_mechanic:": {
        "category": "People & Body",
        "name": "woman mechanic",
        "unicode": "1f469-1f527",
        "unicode_alt": "1f469-200d-1f527"
    },
    ":woman_office_worker:": {
        "category": "People & Body",
        "name": "woman office worker",
        "unicode": "1f469-1f4bc",
        "unicode_alt": "1f469-200d-1f4bc"
    },
    ":woman_pilot:": {
        "category": "People & Body",
        "name": "woman pilot",
        "unicode": "1f469-2708",
        "unicode_alt": "1f469-200d-2708-fe0f"
    },
    ":woman_playing_handball:": {
        "category": "People & Body",
        "name": "woman playing handball",
        "unicode": "1f93e-2640",
        "unicode_alt": "1f93e-200d-2640-fe0f"
    },
    ":woman_playing_water_polo:": {
        "category": "People & Body",
        "name": "woman playing water polo",
        "unicode": "1f93d-2640",
        "unicode_alt": "1f93d-200d-2640-fe0f"
    },
    ":woman_scientist:": {
        "category": "People & Body",
        "name": "woman scientist",
        "unicode": "1f469-1f52c",
        "unicode_alt": "1f469-200d-1f52c"
    },
    ":woman_shrugging:": {
        "category": "People & Body",
        "name": "woman shrugging",
        "unicode": "1f937-2640",
        "unicode_alt": "1f937-200d-2640-fe0f"
    },
    ":woman_singer:": {
        "category": "People & Body",
        "name": "woman singer",
        "unicode": "1f469-1f3a4",
        "unicode_alt": "1f469-200d-1f3a4"
    },
    ":woman_student:": {
        "category": "People & Body",
        "name": "woman student",
        "unicode": "1f469-1f393",
        "unicode_alt": "1f469-200d-1f393"
    },
    ":woman_teacher:": {
        "category": "People & Body",
        "name": "woman teacher",
        "unicode": "1f469-1f3eb",
        "unicode_alt": "1f469-200d-1f3eb"
    },
    ":woman_technologist:": {
        "category": "People & Body",
        "name": "woman technologist",
        "unicode": "1f469-1f4bb",
        "unicode_alt": "1f469-200d-1f4bb"
    },
    ":woman_with_headscarf:": {
        "category": "People & Body",
        "name": "woman with headscarf",
        "unicode": "1f9d5"
    },
    ":woman_with_probing_cane:": {
        "category": "People & Body",
        "name": "woman with white cane",
        "unicode": "1f469-1f9af",
        "unicode_alt": "1f469-200d-1f9af"
    },
    ":woman_with_turban:": {
        "category": "People & Body",
        "name": "woman wearing turban",
        "unicode": "1f473-2640",
        "unicode_alt": "1f473-200d-2640-fe0f"
    },
    ":woman_with_veil:": {
        "category": "People & Body",
        "name": "woman with veil",
        "unicode": "1f470-2640",
        "unicode_alt": "1f470-200d-2640-fe0f"
    },
    ":womans_clothes:": {
        "category": "Objects",
        "name": "woman\u2019s clothes",
        "unicode": "1f45a"
    },
    ":womans_hat:": {
        "category": "Objects",
        "name": "woman\u2019s hat",
        "unicode": "1f452"
    },
    ":women_wrestling:": {
        "category": "People & Body",
        "name": "women wrestling",
        "unicode": "1f93c-2640",
        "unicode_alt": "1f93c-200d-2640-fe0f"
    },
    ":womens:": {
        "category": "Symbols",
        "name": "women\u2019s room",
        "unicode": "1f6ba"
    },
    ":wood:": {
        "category": "Travel & Places",
        "name": "wood",
        "unicode": "1fab5"
    },
    ":woozy_face:": {
        "category": "Smileys & Emotion",
        "name": "woozy face",
        "unicode": "1f974"
    },
    ":world_map:": {
        "category": "Travel & Places",
        "name": "world map",
        "unicode": "1f5fa",
        "unicode_alt": "1f5fa-fe0f"
    },
    ":worm:": {
        "category": "Animals & Nature",
        "name": "worm",
        "unicode": "1fab1"
    },
    ":worried:": {
        "category": "Smileys & Emotion",
        "name": "worried face",
        "unicode": "1f61f"
    },
    ":wrench:": {
        "category": "Objects",
        "name": "wrench",
        "unicode": "1f527"
    },
    ":wrestling:": {
        "category": "People & Body",
        "name": "people wrestling",
        "unicode": "1f93c"
    },
    ":writing_hand:": {
        "category": "People & Body",
        "name": "writing hand",
        "unicode": "270d",
        "unicode_alt": "270d-fe0f"
    },
    ":x:": {
        "category": "Symbols",
        "name": "cross mark",
        "unicode": "274c"
    },
    ":x_ray:": {
        "category": "Objects",
        "name": "x-ray",
        "unicode": "1fa7b"
    },
    ":yarn:": {
        "category": "Activities",
        "name": "yarn",
        "unicode": "1f9f6"
    },
    ":yawning_face:": {
        "category": "Smileys & Emotion",
        "name": "yawning face",
        "unicode": "1f971"
    },
    ":yellow_circle:": {
        "category": "Symbols",
        "name": "yellow circle",
        "unicode": "1f7e1"
    },
    ":yellow_heart:": {
        "category": "Smileys & Emotion",
        "name": "yellow heart",
        "unicode": "1f49b"
    },
    ":yellow_square:": {
        "category": "Symbols",
        "name": "yellow square",
        "unicode": "1f7e8"
    },
    ":yemen:": {
        "category": "Flags",
        "name": "flag: Yemen",
        "unicode": "1f1fe-1f1ea"
    },
    ":yen:": {
        "category": "Objects",
        "name": "yen banknote",
        "unicode": "1f4b4"
    },
    ":yin_yang:": {
        "category": "Symbols",
        "name": "yin yang",
        "unicode": "262f",
        "unicode_alt": "262f-fe0f"
    },
    ":yo_yo:": {
        "category": "Activities",
        "name": "yo-yo",
        "unicode": "1fa80"
    },
    ":yum:": {
        "category": "Smileys & Emotion",
        "name": "face savoring food",
        "unicode": "1f60b"
    },
    ":zambia:": {
        "category": "Flags",
        "name": "flag: Zambia",
        "unicode": "1f1ff-1f1f2"
    },
    ":zany_face:": {
        "category": "Smileys & Emotion",
        "name": "zany face",
        "unicode": "1f92a"
    },
    ":zap:": {
        "category": "Travel & Places",
        "name": "high voltage",
        "unicode": "26a1"
    },
    ":zebra:": {
        "category": "Animals & Nature",
        "name": "zebra",
        "unicode": "1f993"
    },
    ":zero:": {
        "category": "Symbols",
        "name": "keycap: 0",
        "unicode": "0030-20e3",
        "unicode_alt": "0030-fe0f-20e3"
    },
    ":zimbabwe:": {
        "category": "Flags",
        "name": "flag: Zimbabwe",
        "unicode": "1f1ff-1f1fc"
    },
    ":zipper_mouth_face:": {
        "category": "Smileys & Emotion",
        "name": "zipper-mouth face",
        "unicode": "1f910"
    },
    ":zombie:": {
        "category": "People & Body",
        "name": "zombie",
        "unicode": "1f9df"
    },
    ":zombie_man:": {
        "category": "People & Body",
        "name": "man zombie",
        "unicode": "1f9df-2642",
        "unicode_alt": "1f9df-200d-2642-fe0f"
    },
    ":zombie_woman:": {
        "category": "People & Body",
        "name": "woman zombie",
        "unicode": "1f9df-2640",
        "unicode_alt": "1f9df-200d-2640-fe0f"
    },
    ":zzz:": {
        "category": "Smileys & Emotion",
        "name": "ZZZ",
        "unicode": "1f4a4"
    }
}
aliases = {
    ":basketball_man:": ":bouncing_ball_man:",
    ":basketball_woman:": ":bouncing_ball_woman:",
    ":blonde_woman:": ":blond_haired_woman:",
    ":bride_with_veil:": ":woman_with_veil:",
    ":collision:": ":boom:",
    ":cop:": ":police_officer:",
    ":dancer:": ":woman_dancing:",
    ":e-mail:": ":email:",
    ":european_union:": ":eu:",
    ":facepunch:": ":fist_oncoming:",
    ":fist:": ":fist_raised:",
    ":flipper:": ":dolphin:",
    ":fu:": ":middle_finger:",
    ":heavy_exclamation_mark:": ":exclamation:",
    ":honeybee:": ":bee:",
    ":information_desk_person:": ":tipping_hand_person:",
    ":knife:": ":hocho:",
    ":lantern:": ":izakaya_lantern:",
    ":mandarin:": ":tangerine:",
    ":ng_man:": ":no_good_man:",
    ":ng_woman:": ":no_good_woman:",
    ":open_book:": ":book:",
    ":orange:": ":tangerine:",
    ":paw_prints:": ":feet:",
    ":pencil:": ":memo:",
    ":poop:": ":hankey:",
    ":pout:": ":rage:",
    ":punch:": ":fist_oncoming:",
    ":raised_hand:": ":hand:",
    ":red_car:": ":car:",
    ":running:": ":runner:",
    ":sailboat:": ":boat:",
    ":sassy_man:": ":tipping_hand_man:",
    ":sassy_woman:": ":tipping_hand_woman:",
    ":satisfied:": ":laughing:",
    ":shit:": ":hankey:",
    ":shoe:": ":mans_shoe:",
    ":telephone:": ":phone:",
    ":thumbsdown:": ":-1:",
    ":thumbsup:": ":+1:",
    ":tshirt:": ":shirt:",
    ":uk:": ":gb:",
    ":waxing_gibbous_moon:": ":moon:"
}
