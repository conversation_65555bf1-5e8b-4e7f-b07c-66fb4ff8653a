<!DOCTYPE html>
<html>

<head>
    <title>我的论坛</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">
    <!-- Markdown样式 -->
    <style>
        .markdown-preview {
            color: #6b7280;
            line-height: 1.5;
        }
        .markdown-preview h1, .markdown-preview h2, .markdown-preview h3,
        .markdown-preview h4, .markdown-preview h5, .markdown-preview h6 {
            margin: 0;
            font-weight: 600;
            color: #374151;
        }
        .markdown-preview p { margin: 0.3em 0; }
        .markdown-preview code {
            background-color: #f3f4f6;
            padding: 0.1em 0.3em;
            border-radius: 2px;
            font-size: 0.85em;
        }
        .markdown-preview strong { font-weight: normal; color: #374151; }
        .markdown-preview em { font-style: italic; }
    </style>
</head>

<body class="font-sans m-0 p-0 leading-relaxed">
    <div class="w-[95%] max-w-3xl mx-auto my-5 px-4 md:w-full md:my-8 md:px-6">
        <!-- 更新后的分类导航 -->
        <!-- 排序控件 -->

        <div class="my-6 py-2">
            <div class="flex flex-wrap gap-2 items-center md:gap-3">
                <a href="?category=" class="tag-item {% if not selected_category %}active{% endif %}">全部</a>
                {% for cat in categories %}
                <a href="?category={{ cat.category }}"
                    class="tag-item {% if selected_category == cat.category %}active{% endif %}">
                    {{ cat.category }}
                    <span class="tag-count">{{ cat.count }}</span>
                </a>
                {% endfor %}
                <a href="{{ url_for('new_post') }}" class="btn-success ml-auto">发布</a>
            </div>
        </div>



        <!-- 帖子列表保持不变 -->
        <!-- 在帖子列表循环部分更新为 -->
        {% for post in posts %}
        <div class="card mb-4 border border-gray-200 hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5">
            <div class="flex flex-col gap-3">
                <div class="flex justify-between items-start gap-4">
                    <h3 class="m-0 text-lg leading-snug">
                        <a href="{{ url_for('view_post', post_id=post.id) }}" class="text-gray-900 no-underline hover:text-primary-500 transition-colors duration-200">
                            {{ post.title }}
                        </a>
                    </h3>
                    <time class="text-gray-500 text-sm whitespace-nowrap flex-shrink-0">{{ post.created_at | beijing_time }}</time>
                </div>

                <!-- 新增内容摘要 -->
                {% if post.content %}
                <div class="text-ellipsis-2 text-gray-600 m-0 text-sm leading-relaxed markdown-preview">
                    {{ post.content | markdown | safe | truncate(150, true, '...') }}
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
</body>

</html>
