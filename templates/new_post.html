<!DOCTYPE html>
<html>
<head>
    <title>新建帖子</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">
    <script>
        function toggleMarkdownHelp() {
            const helpDiv = document.getElementById('markdown-help');
            helpDiv.classList.toggle('hidden');
        }

        // 添加一些快捷键支持
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('content');

            textarea.addEventListener('keydown', function(e) {
                // Ctrl+B 加粗
                if (e.ctrlKey && e.key === 'b') {
                    e.preventDefault();
                    insertMarkdown('**', '**');
                }
                // Ctrl+I 斜体
                if (e.ctrlKey && e.key === 'i') {
                    e.preventDefault();
                    insertMarkdown('*', '*');
                }
                // Tab键插入4个空格
                if (e.key === 'Tab') {
                    e.preventDefault();
                    insertText('    ');
                }
            });

            function insertMarkdown(before, after) {
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const selectedText = textarea.value.substring(start, end);
                const newText = before + selectedText + after;

                textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
                textarea.selectionStart = start + before.length;
                textarea.selectionEnd = start + before.length + selectedText.length;
                textarea.focus();
            }

            function insertText(text) {
                const start = textarea.selectionStart;
                textarea.value = textarea.value.substring(0, start) + text + textarea.value.substring(start);
                textarea.selectionStart = textarea.selectionEnd = start + text.length;
                textarea.focus();
            }
        });
    </script>
</head>
<body class="font-sans m-0 p-0 leading-relaxed">
    <div class="w-[95%] max-w-3xl mx-auto my-5 px-4 md:w-full md:my-8 md:px-6">
        <div class="flex flex-col md:flex-row justify-between items-stretch md:items-center mb-4 py-2 gap-4">
            <h1 class="m-0 text-primary-500 text-xl md:text-2xl text-center md:text-left">发表新帖子</h1>
            <a href="{{ url_for('index') }}" class="btn-primary text-center">← 返回列表</a>
        </div>

        <form action="{{ url_for('create_post') }}" method="post" class="bg-white p-6 rounded-lg shadow-card mt-3 max-w-2xl mx-auto md:p-8">
            <div class="mb-3">
                <label for="title" class="block mb-1 text-gray-700 font-medium text-sm">标题</label>
                <input type="text" id="title" name="title" placeholder="请输入帖子标题" required class="w-full max-w-full px-3 py-2 border-2 border-gray-300 rounded-md text-sm transition-all duration-300 bg-gray-50 box-border focus:outline-none focus:border-primary-500 focus:bg-white focus:shadow-sm">
            </div>
            <div class="mb-3">
                <label for="category" class="block mb-1 text-gray-700 font-medium text-sm">分类</label>
                <input type="text" id="category" name="category" placeholder="请输入分类名称" required class="w-full max-w-full px-3 py-2 border-2 border-gray-300 rounded-md text-sm transition-all duration-300 bg-gray-50 box-border focus:outline-none focus:border-primary-500 focus:bg-white focus:shadow-sm">
            </div>
            <div class="mb-3">
                <div class="flex justify-between items-center mb-1">
                    <label for="content" class="text-gray-700 font-medium text-sm">内容</label>
                    <button type="button" onclick="toggleMarkdownHelp()" class="text-xs text-blue-600 hover:text-blue-800 underline">Markdown语法帮助</button>
                </div>
                <div id="markdown-help" class="hidden mb-2 p-3 bg-blue-50 border border-blue-200 rounded-md text-xs">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div>
                            <strong>基础语法：</strong><br>
                            **粗体** <em>*斜体*</em><br>
                            # 标题1 ## 标题2<br>
                            > 引用文本<br>
                            `代码`
                        </div>
                        <div>
                            <strong>列表和链接：</strong><br>
                            - 无序列表<br>
                            1. 有序列表<br>
                            [链接文字](URL)<br>
                            ![图片](URL)
                        </div>
                    </div>
                </div>
                <textarea id="content" name="content" rows="12" placeholder="支持Markdown语法，如：**粗体** *斜体* # 标题 > 引用 等..." required class="w-full max-w-full px-3 py-2 border-2 border-gray-300 rounded-md text-sm transition-all duration-300 bg-gray-50 box-border resize-y min-h-[150px] focus:outline-none focus:border-primary-500 focus:bg-white focus:shadow-sm"></textarea>
            </div>
            <div class="flex justify-end gap-2 mt-6">
                <button type="submit" class="btn-success">发表帖子</button>
            </div>
        </form>
    </div>
</body>
</html>