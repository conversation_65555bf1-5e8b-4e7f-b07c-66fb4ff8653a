<!DOCTYPE html>
<html>
<head>
    <title>编辑帖子</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/output.css') }}">
    <script>
        function toggleMarkdownHelp() {
            const helpDiv = document.getElementById('markdown-help');
            helpDiv.classList.toggle('hidden');
        }
    </script>
</head>
<body class="font-sans m-0 p-0 leading-relaxed">
    <div class="w-[95%] max-w-3xl mx-auto my-5 px-4 md:w-full md:my-8 md:px-6">
        <div class="flex flex-col md:flex-row justify-between items-stretch md:items-center mb-4 py-2 gap-4">
            <h1 class="m-0 text-primary-500 text-xl md:text-2xl text-center md:text-left">编辑帖子</h1>
            <a href="{{ url_for('index') }}" class="btn-primary text-center">← 返回列表</a>
        </div>

        <form action="{{ url_for('edit_post', post_id=post.id) }}" method="post" class="bg-white p-6 rounded-lg shadow-card mt-3 max-w-2xl mx-auto md:p-8">
            <div class="mb-3">
                <input type="text" name="title" value="{{ post.title }}" required class="w-full max-w-full px-3 py-2 border-2 border-gray-300 rounded-md text-sm transition-all duration-300 bg-gray-50 box-border focus:outline-none focus:border-primary-500 focus:bg-white focus:shadow-sm">
            </div>
            <div class="mb-3">
                <input type="text" name="category" value="{{ post.category }}" placeholder="输入分类" required class="w-full max-w-full px-3 py-2 border-2 border-gray-300 rounded-md text-sm transition-all duration-300 bg-gray-50 box-border focus:outline-none focus:border-primary-500 focus:bg-white focus:shadow-sm">
            </div>
            <div class="mb-3">
                <div class="flex justify-between items-center mb-1">
                    <label class="text-gray-700 font-medium text-sm">内容</label>
                    <button type="button" onclick="toggleMarkdownHelp()" class="text-xs text-blue-600 hover:text-blue-800 underline">Markdown语法帮助</button>
                </div>
                <div id="markdown-help" class="hidden mb-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs">
                    <strong>快捷语法：</strong> **粗体** *斜体* `代码` > 引用 # 标题 - 列表
                </div>
                <textarea name="content" required class="w-full max-w-full px-3 py-2 border-2 border-gray-300 rounded-md text-sm transition-all duration-300 bg-gray-50 box-border resize-y min-h-[150px] focus:outline-none focus:border-primary-500 focus:bg-white focus:shadow-sm" placeholder="支持Markdown语法...">{{ post.content }}</textarea>
            </div>
            <div class="flex justify-end gap-2 mt-6">
                <button type="submit" class="btn-success">保存修改</button>
            </div>
        </form>
    </div>
</body>
</html>
